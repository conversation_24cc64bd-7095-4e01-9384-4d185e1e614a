import { useState } from 'react';
import { useSpacedRepetition } from '@/hooks/useSpacedRepetition';
import { useSession } from 'next-auth/react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ThumbsUp, ThumbsDown, Clock, CheckCircle2 } from 'lucide-react';

interface SpacedRepetitionReviewProps {
  questionId: string;
  question: string;
  answer: string;
}

export function SpacedRepetitionReview({
  questionId,
  question,
  answer,
}: SpacedRepetitionReviewProps) {
  const { data: session } = useSession();
  const { currentCard, stats, isLoading, error, rateCard, createCard } = useSpacedRepetition(
    session?.user?.id || ''
  );

  const [showAnswer, setShowAnswer] = useState(false);

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
        <div className="h-32 bg-gray-200 rounded"></div>
        <div className="h-8 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error.message}</AlertDescription>
      </Alert>
    );
  }

  if (!currentCard) {
    return (
      <div className="text-center py-8">
        <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No cards due for review!</h3>
        <p className="text-gray-500">
          {stats?.upcoming ? `${stats.upcoming} cards coming up soon` : 'All caught up!'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-500">Review Progress</h3>
          <div className="flex items-center space-x-2">
            <Progress
              value={(stats?.total ? (stats.total - stats.due) / stats.total : 0) * 100}
              className="w-32"
            />
            <span className="text-sm text-gray-500">
              {stats?.total ? `${stats.total - stats.due}/${stats.total}` : '0/0'}
            </span>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm font-medium text-gray-500">Next Review</div>
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <Clock className="h-4 w-4" />
            <span>{currentCard.nextReview.toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      <Card className="p-6">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">{question}</h2>

          {showAnswer ? (
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-700">{answer}</p>
              </div>

              <div className="flex justify-between space-x-4">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => {
                    rateCard(1); // Hard
                    setShowAnswer(false);
                  }}
                >
                  <ThumbsDown className="h-4 w-4 mr-2" />
                  Hard
                </Button>
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => {
                    rateCard(3); // Good
                    setShowAnswer(false);
                  }}
                >
                  <ThumbsUp className="h-4 w-4 mr-2" />
                  Good
                </Button>
                <Button
                  className="flex-1 bg-green-600 hover:bg-green-700"
                  onClick={() => {
                    rateCard(5); // Easy
                    setShowAnswer(false);
                  }}
                >
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  Easy
                </Button>
              </div>
            </div>
          ) : (
            <Button className="w-full" onClick={() => setShowAnswer(true)}>
              Show Answer
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
}
