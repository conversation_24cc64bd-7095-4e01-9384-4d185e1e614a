/**
 * API-related types and interfaces
 */

import { BaseEntity, PaginatedResponse, ApiResponse, ApiError } from './common';
import { User } from './auth';

// HTTP Methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Request configuration
export interface RequestConfig {
  method?: HttpMethod;
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

// API Client interface
export interface ApiClient {
  get<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
  post<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
  put<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
  patch<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
  delete<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
}

// Course-related API types
export interface Course extends BaseEntity {
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in minutes
  instructor: User;
  enrollmentCount: number;
  rating: number;
  reviewCount: number;
  tags: string[];
  thumbnail?: string;
  isPublic: boolean;
  isEnrolled?: boolean;
  progress?: number;
  modules: CourseModule[];
}

export interface CourseModule extends BaseEntity {
  title: string;
  description: string;
  order: number;
  duration: number;
  isCompleted?: boolean;
  lessons: Lesson[];
}

export interface Lesson extends BaseEntity {
  title: string;
  description: string;
  type: 'video' | 'text' | 'quiz' | 'assignment';
  content: string;
  duration: number;
  order: number;
  isCompleted?: boolean;
  resources?: Resource[];
}

export interface Resource extends BaseEntity {
  title: string;
  type: 'document' | 'video' | 'audio' | 'image' | 'link';
  url: string;
  size?: number;
  description?: string;
}

// Quiz-related API types
export interface Quiz extends BaseEntity {
  title: string;
  description: string;
  timeLimit?: number; // in minutes
  passingScore: number;
  allowRetakes: boolean;
  shuffleQuestions: boolean;
  questions: QuizQuestion[];
  attempts?: QuizAttempt[];
}

export interface QuizQuestion extends BaseEntity {
  question: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay';
  options?: string[];
  correctAnswer: string;
  explanation?: string;
  points: number;
  order: number;
}

export interface QuizAttempt extends BaseEntity {
  quizId: string;
  userId: string;
  score: number;
  totalPoints: number;
  percentage: number;
  timeSpent: number; // in seconds
  startedAt: string;
  completedAt?: string;
  answers: QuizAnswer[];
}

export interface QuizAnswer {
  questionId: string;
  answer: string;
  isCorrect: boolean;
  points: number;
}

// Progress tracking API types
export interface Progress extends BaseEntity {
  userId: string;
  courseId: string;
  moduleId?: string;
  lessonId?: string;
  completionPercentage: number;
  timeSpent: number; // in seconds
  lastAccessedAt: string;
  isCompleted: boolean;
}

export interface LearningPath extends BaseEntity {
  title: string;
  description: string;
  courses: Course[];
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: string[];
  progress?: number;
}

// Analytics API types
export interface AnalyticsData {
  userId: string;
  timeSpent: number;
  coursesCompleted: number;
  averageScore: number;
  learningStreak: number;
  weakAreas: string[];
  strongAreas: string[];
  recommendations: Recommendation[];
  performanceMetrics: PerformanceMetric[];
}

export interface Recommendation {
  type: 'course' | 'topic' | 'practice';
  title: string;
  description: string;
  confidence: number;
  reason: string;
  resourceId?: string;
}

export interface PerformanceMetric {
  metric: string;
  value: number;
  trend: 'up' | 'down' | 'stable';
  period: string;
  comparison?: number;
}

// Search API types
export interface SearchRequest {
  query: string;
  filters?: {
    category?: string;
    difficulty?: string;
    duration?: {
      min?: number;
      max?: number;
    };
    rating?: {
      min?: number;
      max?: number;
    };
    tags?: string[];
  };
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
  pagination?: {
    page: number;
    limit: number;
  };
}

export interface SearchResponse<T> {
  results: T[];
  total: number;
  facets: SearchFacet[];
  suggestions: string[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface SearchFacet {
  field: string;
  values: Array<{
    value: string;
    count: number;
  }>;
}

// Health check API types
export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  services: ServiceHealth[];
}

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  error?: string;
}