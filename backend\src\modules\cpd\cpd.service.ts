import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import {
  CPDActivity,
  CPDCycle,
  CPDActivityType,
} from '../../entities/cpd-tracking.entity';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';

@Injectable()
export class CPDService {
  constructor(
    @InjectRepository(CPDActivity)
    private cpdActivityRepository: Repository<CPDActivity>,
    @InjectRepository(CPDCycle)
    private cpdCycleRepository: Repository<CPDCycle>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Material)
    private materialRepository: Repository<Material>,
  ) {}

  async createCPDActivity(
    userId: string,
    data: {
      activityType: CPDActivityType;
      points: number;
      description: string;
      title: string;
      materialId?: string;
      metadata?: any;
    },
  ): Promise<CPDActivity> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) throw new Error('User not found');

    // Create the activity
    const activity = this.cpdActivityRepository.create({
      title: data.title,
      description: data.description,
      points: data.points,
      activity_date: new Date(),
      is_verified: false,
      user: user,
    });

    if (data.materialId) {
      const material = await this.materialRepository.findOne({
        where: { id: data.materialId },
      });
      if (material) {
        // Add material reference if needed
      }
    }

    // Save and return the activity
    return await this.cpdActivityRepository.save(activity);
  }

  async getCurrentCPDCycle(userId: string): Promise<CPDCycle> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) throw new Error('User not found');

    const currentDate = new Date();
    let cycle = await this.cpdCycleRepository.findOne({
      where: {
        user: { id: userId },
        start_date: LessThanOrEqual(currentDate),
        end_date: MoreThanOrEqual(currentDate),
      },
    });

    if (!cycle) {
      // Create new cycle if none exists
      cycle = this.cpdCycleRepository.create({
        user: { id: userId },
        start_date: new Date(),
        end_date: new Date(
          new Date().setFullYear(new Date().getFullYear() + 1),
        ),
        total_points: 0,
        required_points: 0,
        is_completed: false,
      });
      cycle = await this.cpdCycleRepository.save(cycle);
    }

    return cycle;
  }

  async getCPDActivities(
    userId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      activityType?: string;
      isVerified?: boolean;
    } = {},
  ): Promise<CPDActivity[]> {
    const query = this.cpdActivityRepository
      .createQueryBuilder('activity')
      .where('activity.user.id = :userId', { userId });

    if (options.startDate) {
      query.andWhere('activity.activity_date >= :startDate', {
        startDate: options.startDate,
      });
    }
    if (options.endDate) {
      query.andWhere('activity.activity_date <= :endDate', {
        endDate: options.endDate,
      });
    }
    if (options.activityType) {
      query.andWhere('activity.activity_type = :activityType', {
        activityType: options.activityType,
      });
    }
    if (options.isVerified !== undefined) {
      query.andWhere('activity.is_verified = :isVerified', {
        isVerified: options.isVerified,
      });
    }

    return query.getMany();
  }

  async verifyCPDActivity(
    activityId: string,
    verified: boolean,
    notes?: string,
  ): Promise<CPDActivity> {
    const activity = await this.cpdActivityRepository.findOne({
      where: { id: activityId },
    });
    if (!activity) throw new Error('Activity not found');

    activity.is_verified = verified;
    if (notes) activity.verification_notes = notes;

    return this.cpdActivityRepository.save(activity);
  }

  async updateCPDCycle(
    cycleId: string,
    data: {
      required_points?: number;
    },
  ): Promise<CPDCycle> {
    const cycle = await this.cpdCycleRepository.findOne({
      where: { id: cycleId },
    });
    if (!cycle) throw new Error('Cycle not found');

    if (data.required_points !== undefined) {
      cycle.required_points = data.required_points;
    }

    return this.cpdCycleRepository.save(cycle);
  }
}
