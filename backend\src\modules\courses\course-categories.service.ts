import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CourseCategory } from '../../entities/course-category.entity';

export interface CreateCategoryDto {
  name: string;
  description?: string;
  slug: string;
  color?: string;
  icon?: string;
  parent_id?: string;
  sort_order?: number;
}

export interface UpdateCategoryDto extends Partial<CreateCategoryDto> {
  is_active?: boolean;
}

@Injectable()
export class CourseCategoriesService {
  constructor(
    @InjectRepository(CourseCategory)
    private readonly categoryRepository: Repository<CourseCategory>,
  ) {}

  async create(createCategoryDto: CreateCategoryDto): Promise<CourseCategory> {
    // Check if slug already exists
    const existingCategory = await this.categoryRepository.findOne({
      where: { slug: createCategoryDto.slug },
    });

    if (existingCategory) {
      throw new BadRequestException('Category slug already exists');
    }

    // Validate parent category if provided
    if (createCategoryDto.parent_id) {
      const parentCategory = await this.categoryRepository.findOne({
        where: { id: createCategoryDto.parent_id },
      });
      if (!parentCategory) {
        throw new NotFoundException('Parent category not found');
      }
    }

    const category = this.categoryRepository.create(createCategoryDto);
    return await this.categoryRepository.save(category);
  }

  async findAll(includeInactive = false): Promise<CourseCategory[]> {
    const queryBuilder = this.categoryRepository
      .createQueryBuilder('category')
      .leftJoinAndSelect('category.children', 'children')
      .leftJoinAndSelect('category.courses', 'courses')
      .where('category.parent_id IS NULL') // Only root categories
      .orderBy('category.sort_order', 'ASC')
      .addOrderBy('category.name', 'ASC');

    if (!includeInactive) {
      queryBuilder.andWhere('category.is_active = :isActive', { isActive: true });
    }

    return await queryBuilder.getMany();
  }

  async findOne(id: string): Promise<CourseCategory> {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['parent', 'children', 'courses'],
    });

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async findBySlug(slug: string): Promise<CourseCategory> {
    const category = await this.categoryRepository.findOne({
      where: { slug },
      relations: ['parent', 'children', 'courses'],
    });

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async update(id: string, updateCategoryDto: UpdateCategoryDto): Promise<CourseCategory> {
    const category = await this.categoryRepository.findOne({ where: { id } });

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    // Check if slug is being updated and doesn't conflict
    if (updateCategoryDto.slug && updateCategoryDto.slug !== category.slug) {
      const existingCategory = await this.categoryRepository.findOne({
        where: { slug: updateCategoryDto.slug },
      });
      if (existingCategory) {
        throw new BadRequestException('Category slug already exists');
      }
    }

    // Validate parent category if being updated
    if (updateCategoryDto.parent_id) {
      const parentCategory = await this.categoryRepository.findOne({
        where: { id: updateCategoryDto.parent_id },
      });
      if (!parentCategory) {
        throw new NotFoundException('Parent category not found');
      }

      // Prevent circular reference
      if (updateCategoryDto.parent_id === id) {
        throw new BadRequestException('Category cannot be its own parent');
      }
    }

    Object.assign(category, updateCategoryDto);
    return await this.categoryRepository.save(category);
  }

  async remove(id: string): Promise<void> {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['children', 'courses'],
    });

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    // Check if category has children or courses
    if (category.children && category.children.length > 0) {
      throw new BadRequestException('Cannot delete category with subcategories');
    }

    if (category.courses && category.courses.length > 0) {
      throw new BadRequestException('Cannot delete category with courses');
    }

    await this.categoryRepository.remove(category);
  }

  async getHierarchy(): Promise<CourseCategory[]> {
    return await this.categoryRepository
      .createQueryBuilder('category')
      .leftJoinAndSelect('category.children', 'children')
      .leftJoinAndSelect('children.children', 'grandchildren')
      .where('category.parent_id IS NULL')
      .andWhere('category.is_active = :isActive', { isActive: true })
      .orderBy('category.sort_order', 'ASC')
      .addOrderBy('children.sort_order', 'ASC')
      .addOrderBy('grandchildren.sort_order', 'ASC')
      .getMany();
  }

  async getCategoryStats(id: string) {
    const category = await this.categoryRepository
      .createQueryBuilder('category')
      .leftJoinAndSelect('category.courses', 'courses')
      .leftJoinAndSelect('courses.enrollments', 'enrollments')
      .where('category.id = :id', { id })
      .getOne();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    const totalCourses = category.courses?.length || 0;
    const totalEnrollments = category.courses?.reduce(
      (sum, course) => sum + (course.enrollments?.length || 0),
      0
    ) || 0;
    const averageRating = category.courses?.length > 0
      ? category.courses.reduce((sum, course) => sum + course.rating, 0) / category.courses.length
      : 0;

    return {
      category: {
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
      },
      stats: {
        total_courses: totalCourses,
        total_enrollments: totalEnrollments,
        average_rating: Math.round(averageRating * 100) / 100,
      },
    };
  }
}
