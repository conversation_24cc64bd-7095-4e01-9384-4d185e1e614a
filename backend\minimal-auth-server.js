const express = require('express');
const cors = require('cors');
const { Client } = require('pg');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();
const PORT = process.env.SERVER_PORT || 3002;

// Database configuration
const dbConfig = {
  host: process.env.POSTGRES_HOST || 'localhost',
  port: process.env.POSTGRES_PORT || 5432,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  database: process.env.POSTGRES_DB,
};

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_EXPIRATION = process.env.JWT_EXPIRATION || '24h';

// Middleware
app.use(cors({
  origin: ['http://localhost:3000'],
  methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
  credentials: true,
}));

app.use(express.json());

// Database connection helper
async function getDbClient() {
  const client = new Client(dbConfig);
  await client.connect();
  return client;
}

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const client = await getDbClient();
    await client.query('SELECT 1');
    await client.end();
    
    res.json({
      status: 'healthy',
      database: true,
      redis: true, // Assume Redis is working since we tested it
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      database: false,
      error: error.message
    });
  }
});

// Register endpoint
app.post('/v1/auth/register', async (req, res) => {
  const client = await getDbClient();
  
  try {
    const { email, username, name, password, role = 'student' } = req.body;
    
    console.log(`[AUTH] Registration attempt for: ${email}`);
    
    // Validate required fields
    if (!email || !password || !name) {
      return res.status(400).json({
        message: 'Email, password, and name are required',
        code: 'VALIDATION_ERROR'
      });
    }
    
    // Check if user already exists
    const existingUser = await client.query(
      'SELECT id FROM users WHERE email = $1 OR username = $2',
      [email, username || email]
    );
    
    if (existingUser.rows.length > 0) {
      return res.status(409).json({
        message: 'User already exists with this email or username',
        code: 'USER_EXISTS'
      });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Insert new user
    const result = await client.query(`
      INSERT INTO users (email, username, first_name, last_name, password_hash, role, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, true, NOW(), NOW())
      RETURNING id, email, username, first_name, last_name, role, is_active, created_at
    `, [
      email,
      username || email.split('@')[0],
      name.split(' ')[0],
      name.split(' ').slice(1).join(' ') || '',
      hashedPassword,
      role
    ]);
    
    const user = result.rows[0];
    
    // Generate JWT token
    const accessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRATION }
    );
    
    const refreshToken = jwt.sign(
      { sub: user.id, type: 'refresh' },
      JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    console.log(`[AUTH] Registration successful for: ${email}`);
    
    res.status(201).json({
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        isEmailVerified: true
      }
    });
    
  } catch (error) {
    console.error('[AUTH] Registration error:', error);
    res.status(500).json({
      message: 'Internal server error during registration',
      code: 'INTERNAL_ERROR'
    });
  } finally {
    await client.end();
  }
});

// Login endpoint
app.post('/v1/auth/login', async (req, res) => {
  const client = await getDbClient();
  
  try {
    const { email, username, password } = req.body;
    const identifier = email || username;
    
    console.log(`[AUTH] Login attempt for: ${identifier}`);
    
    if (!identifier || !password) {
      return res.status(400).json({
        message: 'Email/username and password are required',
        code: 'VALIDATION_ERROR'
      });
    }
    
    // Find user by email or username
    const result = await client.query(
      'SELECT id, email, username, first_name, last_name, password_hash, role, is_active FROM users WHERE email = $1 OR username = $1',
      [identifier]
    );
    
    if (result.rows.length === 0) {
      return res.status(401).json({
        message: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    const user = result.rows[0];
    
    if (!user.is_active) {
      return res.status(401).json({
        message: 'Account is deactivated',
        code: 'ACCOUNT_DEACTIVATED'
      });
    }
    
    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    
    if (!isPasswordValid) {
      return res.status(401).json({
        message: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // Generate JWT tokens
    const accessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRATION }
    );
    
    const refreshToken = jwt.sign(
      { sub: user.id, type: 'refresh' },
      JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    console.log(`[AUTH] Login successful for: ${identifier}`);
    
    res.json({
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        isEmailVerified: true
      }
    });
    
  } catch (error) {
    console.error('[AUTH] Login error:', error);
    res.status(500).json({
      message: 'Internal server error during login',
      code: 'INTERNAL_ERROR'
    });
  } finally {
    await client.end();
  }
});

// Protected endpoint for testing
app.get('/v1/auth/protected', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      message: 'Authorization token required',
      code: 'TOKEN_REQUIRED'
    });
  }
  
  const token = authHeader.substring(7);
  
  try {
    const payload = jwt.verify(token, JWT_SECRET);
    res.json({
      message: 'Access granted to protected resource',
      user: {
        id: payload.sub,
        email: payload.email,
        role: payload.role
      }
    });
  } catch (error) {
    res.status(401).json({
      message: 'Invalid or expired token',
      code: 'INVALID_TOKEN'
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    message: 'Internal server error',
    code: 'INTERNAL_ERROR'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Minimal Auth Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 Auth endpoints: http://localhost:${PORT}/v1/auth/`);
});
