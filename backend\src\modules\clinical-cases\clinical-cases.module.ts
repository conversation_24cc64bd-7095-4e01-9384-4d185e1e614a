import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicalCasesService } from './clinical-cases.service';
import { ClinicalCasesController } from './clinical-cases.controller';
import { ClinicalCase } from '../../entities/clinical-case.entity';
import { CaseAttempt } from '../../entities/case-attempt.entity';
import { Course } from '../../entities/course.entity';
import { Unit } from '../../entities/unit.entity';
import { User } from '../../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ClinicalCase,
      CaseAttempt,
      Course,
      Unit,
      User,
    ]),
  ],
  controllers: [ClinicalCasesController],
  providers: [ClinicalCasesService],
  exports: [ClinicalCasesService],
})
export class ClinicalCasesModule {}
