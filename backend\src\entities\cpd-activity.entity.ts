import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { CPDCycle } from './cpd-tracking.entity';

@Entity('cpd_activities')
export class CPDActivity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text')
  description: string;

  @Column({ type: 'int' })
  points: number;

  @Column()
  activity_date: Date;

  @Column({ type: 'boolean', default: false })
  is_verified: boolean;

  @Column({ nullable: true })
  verification_notes: string;

  @ManyToOne(() => User, (user) => user.cpd_activities)
  user: User;

  @ManyToOne(() => CPDCycle, (cycle) => cycle.activities)
  cycle: CPDCycle;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
