import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsDate,
  ValidateNested,
} from 'class-validator';

class SecurityQuestion {
  @IsString()
  question: string;

  @IsString()
  answer: string;
}

export class CreateUserSecuritySettingsDto {
  @IsString()
  userId: string;

  @IsBoolean()
  @IsOptional()
  twoFactorEnabled?: boolean;

  @IsString()
  @IsOptional()
  twoFactorSecret?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  backupCodes?: string[];

  @ValidateNested({ each: true })
  @Type(() => SecurityQuestion)
  @IsOptional()
  securityQuestions?: SecurityQuestion[];

  @IsBoolean()
  @IsOptional()
  emailNotificationsEnabled?: boolean;

  @IsBoolean()
  @IsOptional()
  isEmailVerified?: boolean;

  @IsString()
  @IsOptional()
  emailVerificationToken?: string | null;

  @IsDate()
  @IsOptional()
  emailVerificationExpires?: Date | null;
}
