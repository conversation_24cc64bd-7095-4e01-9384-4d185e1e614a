'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import api from '@/services/api';
import { Calendar, ChevronLeft, ChevronRight, Clock, Plus } from 'lucide-react';

// Define the Event interface for type safety
interface Event {
  id: string;
  title: string;
  description: string;
  date: Date;
  endDate: Date;
  type: string;
  location: string;
  instructor: string;
}

export default function SchedulePage() {
  const [events, setEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState('week');

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setIsLoading(true);
        const placeholderEvents: Event[] = [
          {
            id: '1',
            title: 'Anatomy Lecture',
            description: 'Introduction to the skeletal system',
            date: new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              currentDate.getDate(),
              10,
              0
            ),
            endDate: new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              currentDate.getDate(),
              12,
              0
            ),
            type: 'lecture',
            location: 'Room 101',
            instructor: 'Dr. Sarah Johnson',
          },
          // ... other events
        ];
        setEvents(placeholderEvents);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching events:', error);
        setIsLoading(false);
      }
    };
    fetchEvents();
  }, [currentDate]);

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' });
  };

  const getEventBadgeVariant = (
    type: string
  ): 'default' | 'secondary' | 'outline' | 'destructive' => {
    switch (type?.toLowerCase()) {
      case 'lecture':
        return 'default';
      case 'lab':
        return 'secondary';
      case 'discussion':
        return 'outline';
      case 'seminar':
        return 'destructive';
      case 'study':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const navigatePrevious = () => {
    const newDate = new Date(currentDate);
    if (view === 'day') newDate.setDate(newDate.getDate() - 1);
    else if (view === 'week') newDate.setDate(newDate.getDate() - 7);
    else if (view === 'month') newDate.setMonth(newDate.getMonth() - 1);
    setCurrentDate(newDate);
  };

  const navigateNext = () => {
    const newDate = new Date(currentDate);
    if (view === 'day') newDate.setDate(newDate.getDate() + 1);
    else if (view === 'week') newDate.setDate(newDate.getDate() + 7);
    else if (view === 'month') newDate.setMonth(newDate.getMonth() + 1);
    setCurrentDate(newDate);
  };

  const navigateToday = () => {
    setCurrentDate(new Date());
  };

  const generateWeekDays = () => {
    const days: Date[] = [];
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const weekDays = generateWeekDays();

  const getFilteredEvents = () => {
    if (view === 'day') {
      return events.filter(
        event =>
          event.date.getDate() === currentDate.getDate() &&
          event.date.getMonth() === currentDate.getMonth() &&
          event.date.getFullYear() === currentDate.getFullYear()
      );
    } else if (view === 'week') {
      const startOfWeek = weekDays[0];
      const endOfWeek = weekDays[6];
      return events.filter(
        event =>
          event.date >= startOfWeek &&
          event.date <=
            new Date(endOfWeek.getFullYear(), endOfWeek.getMonth(), endOfWeek.getDate(), 23, 59, 59)
      );
    }
    return events;
  };

  const filteredEvents = getFilteredEvents();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Schedule</h1>
        <Button variant="default" size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Event
        </Button>
      </div>

      <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center space-x-2">
          <Button variant="secondary" size="sm" onClick={navigatePrevious}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="secondary" size="sm" onClick={navigateToday}>
            Today
          </Button>
          <Button variant="secondary" size="sm" onClick={navigateNext}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        <h2 className="text-lg font-medium">
          {view === 'day'
            ? formatDate(currentDate)
            : view === 'week'
              ? `${formatDate(weekDays[0])} - ${formatDate(weekDays[6])}`
              : currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </h2>
        <div className="flex items-center space-x-2">
          <Button
            variant={view === 'day' ? 'default' : 'secondary'}
            size="sm"
            onClick={() => setView('day')}
          >
            Day
          </Button>
          <Button
            variant={view === 'week' ? 'default' : 'secondary'}
            size="sm"
            onClick={() => setView('week')}
          >
            Week
          </Button>
          <Button
            variant={view === 'month' ? 'default' : 'secondary'}
            size="sm"
            onClick={() => setView('month')}
          >
            Month
          </Button>
        </div>
      </div>

      {view === 'week' && (
        <div className="grid grid-cols-7 gap-2 mb-4">
          {weekDays.map(day => (
            <div
              key={day.toISOString()}
              className={`text-center p-2 rounded-lg ${
                day.getDate() === new Date().getDate() &&
                day.getMonth() === new Date().getMonth() &&
                day.getFullYear() === new Date().getFullYear()
                  ? 'bg-blue-100'
                  : 'bg-white'
              }`}
            >
              <div className="text-xs text-gray-500">
                {day.toLocaleDateString('en-US', { weekday: 'short' })}
              </div>
              <div className="text-lg font-medium">{day.getDate()}</div>
            </div>
          ))}
        </div>
      )}

      {filteredEvents.length === 0 ? (
        <div className="text-center py-10">
          <Calendar className="h-12 w-12 mx-auto text-gray-400" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">No events scheduled</h3>
          <p className="mt-1 text-sm text-gray-500">
            {view === 'day'
              ? 'There are no events scheduled for today.'
              : view === 'week'
                ? 'There are no events scheduled for this week.'
                : 'There are no events scheduled for this month.'}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {view === 'day' ? (
            filteredEvents.map(event => (
              <Card key={event.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-20 text-center">
                      <div className="text-sm font-medium">{formatTime(event.date)}</div>
                      <div className="text-xs text-gray-500">to {formatTime(event.endDate)}</div>
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex items-center justify-between">
                        <Badge variant={getEventBadgeVariant(event.type)} className="mb-2">
                          {event.type?.charAt(0).toUpperCase() + event.type?.slice(1) || 'Event'}
                        </Badge>
                        <div className="text-sm text-gray-500">{event.location}</div>
                      </div>
                      <h3 className="text-lg font-medium">{event.title}</h3>
                      <p className="text-sm text-gray-500 mt-1">{event.description}</p>
                      {event.instructor && (
                        <p className="text-sm text-gray-500 mt-2">Instructor: {event.instructor}</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div>
              {weekDays.map(day => {
                const dayEvents = events.filter(
                  event =>
                    event.date.getDate() === day.getDate() &&
                    event.date.getMonth() === day.getMonth() &&
                    event.date.getFullYear() === day.getFullYear()
                );
                if (dayEvents.length === 0) return null;
                return (
                  <div key={day.toISOString()} className="mb-6">
                    <h3 className="text-lg font-medium mb-2">{formatDate(day)}</h3>
                    <div className="space-y-2">
                      {dayEvents.map(event => (
                        <Card key={event.id} className="hover:shadow-md transition-shadow">
                          <CardContent className="p-3">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 w-16 text-center">
                                <div className="text-sm font-medium">{formatTime(event.date)}</div>
                              </div>
                              <div className="ml-3 flex-1">
                                <div className="flex items-center justify-between">
                                  <h4 className="font-medium">{event.title}</h4>
                                  <Badge variant={getEventBadgeVariant(event.type)}>
                                    {event.type?.charAt(0).toUpperCase() + event.type?.slice(1) ||
                                      'Event'}
                                  </Badge>
                                </div>
                                <div className="text-xs text-gray-500">{event.location}</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
