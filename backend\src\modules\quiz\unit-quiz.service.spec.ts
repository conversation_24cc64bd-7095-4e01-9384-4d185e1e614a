import { UnitQuizService } from './unit-quiz.service';
import { NotFoundException } from '@nestjs/common';

describe('UnitQuizService', () => {
  let service: UnitQuizService;
  let mockQuizRepo: any;

  beforeEach(() => {
    mockQuizRepo = {
      findOne: jest.fn(),
    };
    service = new UnitQuizService(mockQuizRepo, null, null);
    service.analyticsService = { recordUnitQuizScore: jest.fn() };
    service.gamificationService = { rewardForUnitQuiz: jest.fn() };
  });

  it('should score quiz and call analytics/gamification', async () => {
    const quiz = {
      questions: [
        { id: 'q1', correctAnswer: 'A' },
        { id: 'q2', correctAnswer: 'B' },
        { id: 'q3', correctAnswer: 'C' },
      ],
    };
    mockQuizRepo.findOne.mockResolvedValue(quiz);
    const answers = { q1: 'A', q2: 'B', q3: 'C' };
    const result = await service.scoreQuiz('user1', 'unit1', answers);
    expect(result.score).toBe(100);
    expect(result.passed).toBe(true);
    expect(service.analyticsService.recordUnitQuizScore).toHaveBeenCalledWith(
      'user1',
      'unit1',
      100,
    );
    expect(service.gamificationService.rewardForUnitQuiz).toHaveBeenCalledWith(
      'user1',
      'unit1',
      true,
    );
  });

  it('should return 0 score and not pass if all answers are wrong', async () => {
    const quiz = {
      questions: [
        { id: 'q1', correctAnswer: 'A' },
        { id: 'q2', correctAnswer: 'B' },
      ],
    };
    mockQuizRepo.findOne.mockResolvedValue(quiz);
    const answers = { q1: 'X', q2: 'Y' };
    const result = await service.scoreQuiz('user1', 'unit1', answers);
    expect(result.score).toBe(0);
    expect(result.passed).toBe(false);
  });

  it('should throw NotFoundException if quiz not found', async () => {
    mockQuizRepo.findOne.mockResolvedValue(null);
    await expect(service.scoreQuiz('user1', 'unit1', {})).rejects.toThrow(
      NotFoundException,
    );
  });
});
