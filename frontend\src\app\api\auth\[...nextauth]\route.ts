import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import GithubProvider from 'next-auth/providers/github';
import { NextAuthOptions } from 'next-auth';
import { JWT } from 'next-auth/jwt';

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string;
      accessToken?: string;
    };
  }

  interface User {
    id: string;
    name: string;
    email: string;
    image?: string;
    role: string;
    accessToken: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id?: string;
    role?: string;
    accessToken?: string;
  }
}

export const authOptions: NextAuthOptions = {
  debug: process.env.NODE_ENV === 'development',
  providers: [
    // Credentials Provider
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: '<PERSON>rna<PERSON>', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          throw new Error('Please enter both username and password');
        }

        try {
          // First try the real API
          try {
            // Check if the username looks like an email
            const isEmail = credentials.username.includes('@');

            const response = await fetch('http://localhost:3002/v1/auth/login', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(
                isEmail
                  ? { email: credentials.username, password: credentials.password }
                  : { username: credentials.username, password: credentials.password }
              ),
              // Set a short timeout to quickly fall back to test accounts if backend is not available
              signal: AbortSignal.timeout(2000),
            });

            const data = await response.json();

            if (response.ok && data) {
              return {
                id: data.user.id,
                name: data.user.name || data.user.username,
                email: data.user.email,
                role: data.user.role,
                accessToken: data.accessToken || data.access_token,
              };
            } else {
              // Handle specific error messages from the backend
              if (data.message) {
                throw new Error(data.message);
              }
              throw new Error('Authentication failed');
            }
          } catch (apiError: any) {
            console.warn('API login failed:', apiError);
            // Pass through the error message from the backend
            if (apiError.message) {
              throw new Error(apiError.message);
            }
            throw new Error('Authentication failed');
          }

          // Development mode logging
          if (process.env.NODE_ENV !== 'production') {
            console.log('Development mode: Authentication attempted');
          } else {
            console.log('Production mode: Test accounts disabled');
          }

          throw new Error('Invalid username or password');
        } catch (error: any) {
          throw new Error(error.message || 'Authentication failed');
        }
      },
    }),
  ],
  pages: {
    signIn: '/auth/login',
    signOut: '/auth/login',
    error: '/auth/error',
  },
  callbacks: {
    async jwt({ token, user }: { token: JWT; user?: any }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.accessToken = user.accessToken;
      }
      return token;
    },
    async session({ session, token }: { session: any; token: JWT }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.accessToken = token.accessToken as string;
      }
      return session;
    },
  },
  session: {
    strategy: 'jwt',
    // Reduced session duration for security
    maxAge: 3600, // 1 hour
  },
  events: {
    async signIn({ user }) {
      console.log('User signed in:', user.email);
    },
    async signOut() {
      console.log('User signed out');
    },
    async session() {
      console.log('Session accessed');
    },
    // The 'error' event is not supported by NextAuth.js
    // Error handling should be done in the callbacks
  },
  secret: process.env.NEXTAUTH_SECRET,
  // Enable CSRF protection
  useSecureCookies: process.env.NODE_ENV === 'production',
  cookies: {
    sessionToken: {
      name: `__Secure-next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
