import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { ProgressService } from './progress.service';
import { JwtAuthGuard } from '../modules/auth/jwt-auth.guard';

@Controller('progress')
@UseGuards(JwtAuthGuard)
export class ProgressController {
  constructor(private readonly progressService: ProgressService) {}

  @Post(':unitId')
  async updateProgress(
    @Param('unitId') unitId: string,
    @Body('userId') userId: string,
    @Body('status') status: string,
  ) {
    return this.progressService.updateProgress(userId, unitId, status);
  }

  @Get('user/:userId')
  async getProgressByUser(@Param('userId') userId: string) {
    return this.progressService.getProgressByUser(userId);
  }
}
