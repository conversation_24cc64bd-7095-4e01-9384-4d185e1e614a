import { UnitQuiz } from './unit-quiz.entity';
import { ManyToOne } from 'typeorm';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('quiz_questions')
export class QuizQuestion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('text')
  text: string;

  @ManyToOne(() => UnitQuiz, (quiz) => quiz.questions, { nullable: true })
  unitQuiz: UnitQuiz;

  @Column('text')
  correct_answer: string;
}
