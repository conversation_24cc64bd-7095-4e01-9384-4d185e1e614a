#!/usr/bin/env node

/**
 * Version Synchronization Script
 * Synchronizes version numbers across all services in the monorepo
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function readPackageJson(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    log(`❌ Error reading ${filePath}: ${error.message}`, 'red');
    return null;
  }
}

function writePackageJson(filePath, packageData) {
  try {
    fs.writeFileSync(filePath, JSON.stringify(packageData, null, 2) + '\n');
    return true;
  } catch (error) {
    log(`❌ Error writing ${filePath}: ${error.message}`, 'red');
    return false;
  }
}

function updatePythonVersion(filePath, version) {
  try {
    if (!fs.existsSync(filePath)) {
      // Create __init__.py with version if it doesn't exist
      fs.writeFileSync(filePath, `__version__ = "${version}"\n`);
      return true;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    
    // Update existing version or add new one
    if (content.includes('__version__')) {
      content = content.replace(/__version__\s*=\s*["'][^"']*["']/, `__version__ = "${version}"`);
    } else {
      content = `__version__ = "${version}"\n${content}`;
    }
    
    fs.writeFileSync(filePath, content);
    return true;
  } catch (error) {
    log(`❌ Error updating Python version in ${filePath}: ${error.message}`, 'red');
    return false;
  }
}

function updateDockerCompose(filePath, version) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️ Docker compose file not found: ${filePath}`, 'yellow');
      return true;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    
    // Update image tags with new version
    content = content.replace(
      /(image:\s*[^:\s]+):[\w.-]+/g,
      `$1:${version}`
    );
    
    // Update environment variables
    content = content.replace(
      /(APP_VERSION[=:]\s*)[\w.-]+/g,
      `$1${version}`
    );
    
    fs.writeFileSync(filePath, content);
    return true;
  } catch (error) {
    log(`❌ Error updating Docker compose ${filePath}: ${error.message}`, 'red');
    return false;
  }
}

function syncVersions() {
  log('🔄 Starting version synchronization...', 'cyan');
  
  // Read root package.json to get the master version
  const rootPackagePath = path.join(process.cwd(), 'package.json');
  const rootPackage = readPackageJson(rootPackagePath);
  
  if (!rootPackage) {
    log('❌ Failed to read root package.json', 'red');
    process.exit(1);
  }
  
  const masterVersion = rootPackage.version;
  log(`📦 Master version: ${masterVersion}`, 'bright');
  
  let success = true;
  const updates = [];
  
  // Update frontend package.json
  const frontendPackagePath = path.join(process.cwd(), 'frontend', 'package.json');
  const frontendPackage = readPackageJson(frontendPackagePath);
  if (frontendPackage) {
    if (frontendPackage.version !== masterVersion) {
      frontendPackage.version = masterVersion;
      if (writePackageJson(frontendPackagePath, frontendPackage)) {
        updates.push('Frontend package.json');
        log(`✅ Updated frontend version to ${masterVersion}`, 'green');
      } else {
        success = false;
      }
    } else {
      log(`✅ Frontend version already up to date`, 'green');
    }
  }
  
  // Update backend package.json
  const backendPackagePath = path.join(process.cwd(), 'backend', 'package.json');
  const backendPackage = readPackageJson(backendPackagePath);
  if (backendPackage) {
    if (backendPackage.version !== masterVersion) {
      backendPackage.version = masterVersion;
      if (writePackageJson(backendPackagePath, backendPackage)) {
        updates.push('Backend package.json');
        log(`✅ Updated backend version to ${masterVersion}`, 'green');
      } else {
        success = false;
      }
    } else {
      log(`✅ Backend version already up to date`, 'green');
    }
  }
  
  // Update Python analytics version
  const pythonVersionPath = path.join(process.cwd(), 'backend', 'python_analytics', '__init__.py');
  if (updatePythonVersion(pythonVersionPath, masterVersion)) {
    updates.push('Python analytics __init__.py');
    log(`✅ Updated Python analytics version to ${masterVersion}`, 'green');
  } else {
    success = false;
  }
  
  // Update backend configuration
  const configPath = path.join(process.cwd(), 'backend', 'src', 'config', 'configuration.ts');
  if (fs.existsSync(configPath)) {
    try {
      let content = fs.readFileSync(configPath, 'utf8');
      content = content.replace(
        /(version:\s*process\.env\.APP_VERSION\s*\|\|\s*)['"][^'"]*['"]/,
        `$1'${masterVersion}'`
      );
      fs.writeFileSync(configPath, content);
      updates.push('Backend configuration');
      log(`✅ Updated backend configuration version to ${masterVersion}`, 'green');
    } catch (error) {
      log(`❌ Error updating backend configuration: ${error.message}`, 'red');
      success = false;
    }
  }
  
  // Update Docker compose files
  const dockerFiles = [
    'docker-compose.yml',
    'docker-compose.dev.yml',
    'docker-compose.prod.yml'
  ];
  
  for (const dockerFile of dockerFiles) {
    const dockerPath = path.join(process.cwd(), dockerFile);
    if (updateDockerCompose(dockerPath, masterVersion)) {
      updates.push(dockerFile);
      log(`✅ Updated ${dockerFile}`, 'green');
    } else {
      success = false;
    }
  }
  
  // Create version info file
  const versionInfo = {
    version: masterVersion,
    timestamp: new Date().toISOString(),
    services: {
      frontend: masterVersion,
      backend: masterVersion,
      analytics: masterVersion
    },
    lastUpdated: updates
  };
  
  const versionInfoPath = path.join(process.cwd(), 'version.json');
  try {
    fs.writeFileSync(versionInfoPath, JSON.stringify(versionInfo, null, 2) + '\n');
    log(`✅ Created version.json`, 'green');
  } catch (error) {
    log(`❌ Error creating version.json: ${error.message}`, 'red');
    success = false;
  }
  
  // Summary
  log('\n📊 Version Synchronization Summary:', 'cyan');
  log(`Version: ${masterVersion}`, 'bright');
  log(`Files updated: ${updates.length}`, 'bright');
  
  if (updates.length > 0) {
    log('\nUpdated files:', 'yellow');
    updates.forEach(file => log(`  • ${file}`, 'yellow'));
  }
  
  if (success) {
    log('\n🎉 Version synchronization completed successfully!', 'green');
    process.exit(0);
  } else {
    log('\n❌ Version synchronization completed with errors!', 'red');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  syncVersions();
}

module.exports = { syncVersions };
