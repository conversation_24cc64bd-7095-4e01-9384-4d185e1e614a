#!/bin/bash
# Setup script for MedTrack Hub development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        REQUIRED_VERSION="20.0.0"
        
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_success "Node.js version $NODE_VERSION is compatible"
        else
            print_error "Node.js version $NODE_VERSION is not compatible. Required: $REQUIRED_VERSION or higher"
            exit 1
        fi
    else
        print_error "Node.js is not installed"
        exit 1
    fi
}

# Function to check Python version
check_python_version() {
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        REQUIRED_VERSION="3.11.0"
        
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_success "Python version $PYTHON_VERSION is compatible"
        else
            print_error "Python version $PYTHON_VERSION is not compatible. Required: $REQUIRED_VERSION or higher"
            exit 1
        fi
    else
        print_error "Python 3 is not installed"
        exit 1
    fi
}

# Function to check Docker
check_docker() {
    if command_exists docker; then
        if docker info >/dev/null 2>&1; then
            print_success "Docker is running"
        else
            print_warning "Docker is installed but not running"
        fi
    else
        print_warning "Docker is not installed"
    fi
}

# Function to setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created .env file from template"
        print_warning "Please edit .env file with your configuration"
    else
        print_warning ".env file already exists"
    fi
    
    if [ ! -f frontend/.env.local ]; then
        cat > frontend/.env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_ANALYTICS_API_URL=http://localhost:5000
EOF
        print_success "Created frontend/.env.local file"
    else
        print_warning "frontend/.env.local file already exists"
    fi
    
    if [ ! -f backend/.env ]; then
        cat > backend/.env << EOF
NODE_ENV=development
PORT=3002
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/medtrack
JWT_SECRET=your-jwt-secret-change-this-in-production
JWT_REFRESH_SECRET=your-jwt-refresh-secret-change-this-in-production
REDIS_URL=redis://localhost:6379
EOF
        print_success "Created backend/.env file"
        print_warning "Please edit backend/.env file with your configuration"
    else
        print_warning "backend/.env file already exists"
    fi
    
    if [ ! -f backend/python_analytics/.env ]; then
        cat > backend/python_analytics/.env << EOF
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/medtrack
JWT_SECRET=your-jwt-secret-change-this-in-production
PORT=5000
EOF
        print_success "Created backend/python_analytics/.env file"
        print_warning "Please edit backend/python_analytics/.env file with your configuration"
    else
        print_warning "backend/python_analytics/.env file already exists"
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    
    # Backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # Python analytics dependencies
    print_status "Installing Python analytics dependencies..."
    cd backend/python_analytics
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Created Python virtual environment"
    fi
    
    # Activate virtual environment and install dependencies
    source venv/bin/activate || source venv/Scripts/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    deactivate
    cd ../..
    
    print_success "All dependencies installed"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    if command_exists docker; then
        print_status "Starting PostgreSQL and Redis with Docker..."
        docker-compose up -d db redis
        
        # Wait for database to be ready
        print_status "Waiting for database to be ready..."
        sleep 10
        
        # Run migrations
        print_status "Running database migrations..."
        cd backend
        npm run migration:run || print_warning "Migration failed - database might not be ready"
        cd ..
        
        print_success "Database setup complete"
    else
        print_warning "Docker not available. Please set up PostgreSQL and Redis manually"
    fi
}

# Function to generate secrets
generate_secrets() {
    print_status "Generating secure secrets..."
    
    JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
    JWT_REFRESH_SECRET=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
    
    print_success "Generated JWT secrets:"
    echo "JWT_SECRET=$JWT_SECRET"
    echo "JWT_REFRESH_SECRET=$JWT_REFRESH_SECRET"
    print_warning "Please update your .env files with these secrets"
}

# Function to run health checks
run_health_checks() {
    print_status "Running health checks..."
    
    if [ -f scripts/health-check.sh ]; then
        chmod +x scripts/health-check.sh
        ./scripts/health-check.sh || print_warning "Some services are not healthy"
    else
        print_warning "Health check script not found"
    fi
}

# Main setup function
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════╗"
    echo "║        MedTrack Hub Setup            ║"
    echo "║     Development Environment         ║"
    echo "╚══════════════════════════════════════╝"
    echo -e "${NC}"
    
    print_status "Starting setup process..."
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    check_node_version
    check_python_version
    check_docker
    
    # Setup environment files
    setup_env_files
    
    # Install dependencies
    install_dependencies
    
    # Generate secrets
    generate_secrets
    
    # Setup database (if Docker is available)
    if command_exists docker; then
        setup_database
    fi
    
    print_success "Setup complete!"
    echo
    print_status "Next steps:"
    echo "1. Edit .env files with your configuration"
    echo "2. Start the development environment: npm run dev"
    echo "3. Or use Docker: docker-compose -f docker-compose.dev.yml up"
    echo "4. Visit http://localhost:3000 to access the application"
    echo
    print_status "For more information, see README.md"
}

# Run main function
main "$@"
