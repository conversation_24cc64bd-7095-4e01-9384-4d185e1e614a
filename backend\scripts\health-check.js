#!/usr/bin/env node
/**
 * Backend health check script
 */

const http = require('http');

const PORT = process.env.PORT || 3002;
const TIMEOUT = 5000;

const healthCheck = () => {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: 'localhost',
      port: PORT,
      path: '/api/health',
      method: 'GET',
      timeout: TIMEOUT
    }, (res) => {
      if (res.statusCode === 200) {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const healthData = JSON.parse(data);
            if (healthData.status === 'ok') {
              resolve('Backend is healthy');
            } else {
              reject(new Error(`Backend health check failed: ${healthData.message || 'Unknown error'}`));
            }
          } catch (err) {
            reject(new Error(`Backend health check failed: Invalid JSON response`));
          }
        });
      } else {
        reject(new Error(`Backend health check failed with status: ${res.statusCode}`));
      }
    });

    req.on('error', (err) => {
      reject(new Error(`Backend health check failed: ${err.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Backend health check timed out'));
    });

    req.end();
  });
};

// Run health check
healthCheck()
  .then((message) => {
    console.log(message);
    process.exit(0);
  })
  .catch((error) => {
    console.error(error.message);
    process.exit(1);
  });
