import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FlashcardsService } from './flashcards.service';
import { FlashcardsController } from './flashcards.controller';
import { Flashcard } from '../entities/flashcard.entity';
import { QuizQuestion } from '../entities/quiz-question.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Flashcard, QuizQuestion])],
  controllers: [FlashcardsController],
  providers: [FlashcardsService],
  exports: [FlashcardsService],
})
export class FlashcardsModule {}
