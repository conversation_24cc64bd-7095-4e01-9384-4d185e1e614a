// src/modules/auth/auth.service.ts
import { Injectable, UnauthorizedException, BadRequestException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole } from '../../entities/user.entity';
import { UserSession } from '../../entities/security.entity';
import { SecurityService } from './security.service';
import { RefreshTokenService } from './refresh-token.service';
import { TokenBlacklistService } from './token-blacklist.service';
import * as bcrypt from 'bcryptjs';
import { LoginRequest, RegisterRequest, AuthResponse, JwtPayload } from './types/auth.types';
import { SECURITY_EVENT_TYPES } from './types/auth.types';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly MAX_FAILED_ATTEMPTS = 5;
  private readonly LOCK_DURATION = 1000 * 60 * 60; // 1 hour

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserSession)
    private sessionRepository: Repository<UserSession>,
    private jwtService: JwtService,
    private securityService: SecurityService,
    private refreshTokenService: RefreshTokenService,
    private tokenBlacklistService: TokenBlacklistService,
  ) {}

  async login(loginRequest: LoginRequest, ipAddress?: string, userAgent?: string): Promise<AuthResponse> {
    const { email, password, deviceId, twoFactorToken } = loginRequest;
    const identifier = email || 'unknown';
    const logContext = { identifier, ipAddress, userAgent };

    try {
      // Find user by email first, then by username if email not provided
      let user = null;
      if (email) {
        user = await this.userRepository.findOne({ where: { email } });
      }

      // If no user found by email and we have a potential username, try username lookup
      if (!user && email) {
        user = await this.userRepository.findOne({ where: { username: email } });
      }

      if (!user) {
        this.logger.warn('Login attempt failed: User not found', logContext);
        await this.logFailedLogin(identifier, 'USER_NOT_FOUND', ipAddress);
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check if account is locked
      if (user.is_locked && user.locked_until && user.locked_until > new Date()) {
        this.logger.warn('Login attempt failed: Account is locked', { ...logContext, userId: user.id });
        throw new UnauthorizedException('Account is locked. Please try again later.');
      }

      // Check if account is inactive
      if (!user.is_active) {
        this.logger.warn('Login attempt failed: Account is inactive', { ...logContext, userId: user.id });
        throw new UnauthorizedException('Account is inactive. Please check your email for activation instructions.');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password_hash);
      if (!isPasswordValid) {
        this.logger.warn('Login attempt failed: Invalid password', { ...logContext, userId: user.id });
        await this.logFailedLogin(email, 'INVALID_PASSWORD', ipAddress);
        
        // Increment failed attempts
        await this.userRepository.update(user.id, {
          failed_login_attempts: user.failed_login_attempts + 1
        });
        
        // Check if we should lock the account
        if (user.failed_login_attempts + 1 >= this.MAX_FAILED_ATTEMPTS) {
          await this.lockAccount(user.id);
          throw new UnauthorizedException('Too many failed attempts. Account has been locked. Please try again later.');
        }
        
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check if 2FA is required
      const securitySettings = await this.securityService.getSecuritySettings(user.id);
      if (securitySettings?.twoFactorEnabled && securitySettings.twoFactorSecret) {
        if (!twoFactorToken) {
          this.logger.warn('Login attempt failed: 2FA token missing', { ...logContext, userId: user.id });
          throw new BadRequestException('Two-factor authentication required');
        }

        const is2FAValid = await this.securityService.verifyTwoFactor(user.id, twoFactorToken);
        if (!is2FAValid) {
          this.logger.warn('Login attempt failed: Invalid 2FA token', { ...logContext, userId: user.id });
          await this.logFailedLogin(email, 'INVALID_2FA', ipAddress);
          throw new UnauthorizedException('Invalid two-factor authentication token');
        }
      }

      // Clear failed login attempts on successful login
      await this.clearFailedLoginAttempts(email);

      // Generate tokens
      const payload: JwtPayload = {
        sub: user.id,
        email: user.email,
        role: user.role,
      };

      const accessToken = this.jwtService.sign(payload);
      const refreshToken = await this.refreshTokenService.createRefreshToken(user.id);

      // Create session record
      await this.createUserSession(user.id, accessToken, deviceId, ipAddress, userAgent);

      // Log successful login
      this.logger.log('Login successful', { ...logContext, userId: user.id });
      await this.securityService.logSecurityEvent(user.id, SECURITY_EVENT_TYPES.LOGIN_SUCCESS, {
        ipAddress,
        deviceId,
        userAgent,
        timestamp: new Date(),
      });

      // Check if email is verified
      const isEmailVerified = await this.securityService.isEmailVerified(user.id);

      return {
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          isEmailVerified,
        },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException || error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error('Login error:', { ...logContext, error: error.message, stack: error.stack });
      throw new UnauthorizedException('Authentication failed');
    }
  }

  async register(registerRequest: RegisterRequest): Promise<AuthResponse> {
    const { email, password, firstName, lastName, username, role } = registerRequest;

    // Check if user already exists by email
    const existingUserByEmail = await this.userRepository.findOne({ where: { email } });
    if (existingUserByEmail) {
      throw new BadRequestException('User with this email already exists');
    }

    // Check if username already exists (if provided)
    if (username) {
      const existingUserByUsername = await this.userRepository.findOne({ where: { username } });
      if (existingUserByUsername) {
        throw new BadRequestException('User with this username already exists');
      }
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const user = this.userRepository.create({
      email,
      username: username || email, // Use provided username or fallback to email
      password_hash: passwordHash,
      first_name: firstName,
      last_name: lastName,
      name: `${firstName} ${lastName}`.trim(),
      role: role === 'admin' ? UserRole.ADMIN : (role === 'teacher' ? UserRole.TEACHER : UserRole.STUDENT),
      createdAt: new Date(),
      updatedAt: new Date()
    });

    const savedUser = await this.userRepository.save(user);
    await this.securityService.sendVerificationEmail(savedUser);

    // Generate tokens (user can use the app but with limited access until verified)
    const payload: JwtPayload = {
      sub: savedUser.id,
      email: savedUser.email,
      role: savedUser.role,
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = await this.refreshTokenService.createRefreshToken(savedUser.id);

    return {
      accessToken,
      refreshToken,
      user: {
        id: savedUser.id,
        email: savedUser.email,
        firstName: savedUser.first_name,
        lastName: savedUser.last_name,
        role: savedUser.role,
        isEmailVerified: false,
      },
    };
  }

  async refreshToken(refreshToken: string, userId: string): Promise<{ accessToken: string; refreshToken: string }> {
    // Validate refresh token
    const isValid = await this.refreshTokenService.validateRefreshToken(refreshToken, userId);
    if (!isValid) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    // Get user
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Generate new tokens
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    const newAccessToken = this.jwtService.sign(payload);
    // Revoke old refresh token and create new one
    await this.refreshTokenService.revokeRefreshToken(refreshToken);
    const newRefreshToken = await this.refreshTokenService.createRefreshToken(userId);

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    };
  }

  async logout(userId: string, refreshToken?: string): Promise<void> {
    // Revoke refresh token if provided
    if (refreshToken) {
      await this.refreshTokenService.revokeRefreshToken(refreshToken);
    }

    // Deactivate all user sessions
    await this.sessionRepository.update(
      { userId, isActive: true },
      { isActive: false, updatedAt: new Date() },
    );

    // Log logout event
    await this.securityService.logSecurityEvent(userId, SECURITY_EVENT_TYPES.SESSION_REVOKED, {
      timestamp: new Date(),
    });
  }

  async logoutAllDevices(userId: string): Promise<void> {
    // Revoke all refresh tokens
    await this.refreshTokenService.revokeAllUserTokens(userId);

    // Deactivate all sessions
    await this.sessionRepository.update(
      { userId, isActive: true },
      { isActive: false, updatedAt: new Date() },
    );

    // Log the event
    await this.securityService.logSecurityEvent(userId, SECURITY_EVENT_TYPES.SESSION_REVOKED, {
      timestamp: new Date(),
      allDevices: true,
    });
  }

  private async createUserSession(
    userId: string,
    accessToken: string,
    deviceId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    const session = this.sessionRepository.create({
      userId,
      deviceId,
      userAgent,
      ipAddress,
      lastAccessed: new Date(),
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await this.sessionRepository.save(session);
  }

  private async logFailedLogin(email: string, reason: string, ipAddress?: string): Promise<void> {
    // Find user if exists to log the event
    const user = await this.userRepository.findOne({ where: { email } });
    if (user) {
      await this.securityService.logSecurityEvent(user.id, SECURITY_EVENT_TYPES.LOGIN_FAILURE, {
        reason,
        ipAddress,
        timestamp: new Date(),
      });
    }
  }

  async validateToken(token: string): Promise<any> {
    try {
      const payload = this.jwtService.verify(token);
      const user = await this.userRepository.findOne({ where: { id: payload.sub } });
      if (!user) {
        throw new UnauthorizedException('User not found');
      }
      return {
        id: user.id,
        email: user.email,
        role: user.role,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  async findUserByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({ where: { email } });
  }

  async validateUser(email: string, password: string): Promise<Omit<User, 'password_hash'> | null> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      return null;
    }

    const { password_hash, ...result } = user;
    return result;
  }

  private async lockAccount(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      is_locked: true,
      locked_until: new Date(Date.now() + this.LOCK_DURATION),
    });
  }

  private async clearFailedLoginAttempts(email: string): Promise<void> {
    await this.userRepository.update(
      { email },
      { 
        failed_login_attempts: 0,
        is_locked: false,
        locked_until: new Date(0) // Set to epoch time (1970-01-01) to indicate no lock
      }
    );
  }
}