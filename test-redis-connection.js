const Redis = require('ioredis');
require('dotenv').config({ path: './backend/.env' });

async function testRedisConnection() {
    const redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
    });

    try {
        console.log('Attempting to connect to Redis...');
        console.log(`Host: ${redis.options.host}:${redis.options.port}`);
        
        // Test basic connectivity
        const pong = await redis.ping();
        console.log('✅ Redis connection successful!');
        console.log('📡 Ping response:', pong);
        
        // Test basic operations
        await redis.set('test:connection', 'Hello Redis!');
        const value = await redis.get('test:connection');
        console.log('📝 Set/Get test:', value);
        
        // Test with expiration
        await redis.setex('test:expiring', 60, 'This will expire in 60 seconds');
        const ttl = await redis.ttl('test:expiring');
        console.log('⏰ TTL test:', ttl, 'seconds remaining');
        
        // Get Redis info
        const info = await redis.info('server');
        const lines = info.split('\r\n');
        const version = lines.find(line => line.startsWith('redis_version:'));
        if (version) {
            console.log('🔧 Redis version:', version.split(':')[1]);
        }
        
        // Clean up test keys
        await redis.del('test:connection', 'test:expiring');
        console.log('🧹 Cleaned up test keys');
        
    } catch (error) {
        console.error('❌ Redis connection failed:');
        console.error('Error:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Suggestions:');
            console.log('   - Make sure Redis server is running');
            console.log('   - Check if the port 6379 is correct');
            console.log('   - Verify the host is accessible');
            console.log('   - Try: docker run -d --name redis-medical -p 6379:6379 redis:7-alpine');
        }
    } finally {
        redis.disconnect();
    }
}

testRedisConnection();
