import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsDate,
  IsBoolean,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class QuizAttemptSyncDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  quizId: string;

  @ApiProperty()
  @IsNumber()
  score: number;

  @ApiProperty()
  @IsNumber()
  timeSpent: number;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  startedAt: Date;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  completedAt: Date;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  synced?: boolean;

  @ApiProperty({ type: Object })
  answers: Record<string, string>;
}
