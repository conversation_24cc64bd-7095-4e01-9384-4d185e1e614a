# 🚀 Deployment Guide

This guide covers deploying MedTrack Hub in various environments.

## 📋 Prerequisites

- Docker and Docker Compose
- Domain name (for production)
- SSL certificates (for production)
- Environment variables configured

## 🏗️ Environment Setup

### 1. Environment Variables

Copy and configure environment files:

```bash
# Production environment
cp .env.example .env.production

# Staging environment
cp .env.example .env.staging
```

### 2. Required Environment Variables

```bash
# Application
NODE_ENV=production
FRONTEND_PORT=3000
BACKEND_PORT=3002
ANALYTICS_PORT=5000

# Database
POSTGRES_HOST=your-db-host
POSTGRES_PORT=5432
POSTGRES_USER=your-db-user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=medtrack

# Redis
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-redis-password

# JWT Secrets (Generate with: openssl rand -base64 32)
JWT_SECRET=your-very-secure-jwt-secret
JWT_REFRESH_SECRET=your-very-secure-refresh-secret

# URLs
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_ANALYTICS_API_URL=https://analytics.yourdomain.com
```

## 🐳 Docker Deployment

### Production Deployment

```bash
# Build and start services
docker-compose up -d --build

# Check service status
docker-compose ps

# View logs
docker-compose logs -f

# Scale services if needed
docker-compose up -d --scale backend=3
```

### Staging Deployment

```bash
# Use staging configuration
docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
```

## 🌐 Reverse Proxy Setup (Nginx)

### Nginx Configuration

```nginx
# /etc/nginx/sites-available/medtrack
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:3002/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Analytics API
    location /analytics/ {
        proxy_pass http://localhost:5000/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## ☁️ Cloud Deployment

### AWS ECS Deployment

1. **Create ECS Cluster**
2. **Build and push images to ECR**
3. **Create task definitions**
4. **Deploy services**

### Google Cloud Run

```bash
# Build and deploy frontend
gcloud run deploy medtrack-frontend \
  --image gcr.io/PROJECT_ID/medtrack-frontend \
  --platform managed \
  --region us-central1

# Build and deploy backend
gcloud run deploy medtrack-backend \
  --image gcr.io/PROJECT_ID/medtrack-backend \
  --platform managed \
  --region us-central1
```

### Azure Container Instances

```bash
# Deploy using Azure CLI
az container create \
  --resource-group medtrack-rg \
  --name medtrack-app \
  --image your-registry/medtrack:latest
```

## 🔍 Health Monitoring

### Health Check Endpoints

- Frontend: `https://yourdomain.com/api/health`
- Backend: `https://api.yourdomain.com/api/health`
- Analytics: `https://analytics.yourdomain.com/health`

### Monitoring Setup

```bash
# Set up monitoring script
crontab -e

# Add health check every 5 minutes
*/5 * * * * /path/to/scripts/health-check.sh
```

## 🔄 Database Migrations

### Production Migration

```bash
# Run migrations
docker-compose exec backend npm run migration:run

# Verify migration
docker-compose exec backend npm run migration:show
```

## 📊 Logging and Monitoring

### Centralized Logging

```yaml
# docker-compose.yml logging configuration
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### Monitoring Tools

- **Prometheus**: Metrics collection
- **Grafana**: Visualization
- **ELK Stack**: Log aggregation
- **Sentry**: Error tracking

## 🔒 Security Considerations

### SSL/TLS

- Use Let's Encrypt for free SSL certificates
- Configure HSTS headers
- Use secure cipher suites

### Firewall Rules

```bash
# Allow only necessary ports
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 3000   # Block direct frontend access
ufw deny 3002   # Block direct backend access
ufw deny 5000   # Block direct analytics access
```

### Environment Security

- Use secrets management (AWS Secrets Manager, Azure Key Vault)
- Rotate secrets regularly
- Use least privilege access
- Enable audit logging

## 🔧 Troubleshooting

### Common Issues

1. **Service not starting**:
   ```bash
   docker-compose logs service-name
   ```

2. **Database connection issues**:
   ```bash
   docker-compose exec backend npm run health-check
   ```

3. **Memory issues**:
   ```bash
   docker stats
   ```

### Performance Optimization

- Use CDN for static assets
- Enable gzip compression
- Optimize database queries
- Implement caching strategies
- Use connection pooling

## 📈 Scaling

### Horizontal Scaling

```bash
# Scale backend services
docker-compose up -d --scale backend=3

# Use load balancer
# Configure Nginx upstream
```

### Vertical Scaling

```yaml
# docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
```

## 🔄 Backup and Recovery

### Database Backup

```bash
# Automated backup script
docker-compose exec db pg_dump -U postgres medtrack > backup_$(date +%Y%m%d_%H%M%S).sql
```

### Disaster Recovery

1. Regular automated backups
2. Test restore procedures
3. Document recovery steps
4. Monitor backup integrity
