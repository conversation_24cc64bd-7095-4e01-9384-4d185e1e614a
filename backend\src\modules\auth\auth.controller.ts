import { Controller, Post, Get, Body, UseGuards, Req, Headers, UnauthorizedException, HttpCode, HttpStatus, BadRequestException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './jwt-auth.guard';
import { RegisterDto } from '../../dto/auth/register.dto';
import { LoginDto } from '../../dto/auth/login.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiProduces,
  ApiHeader,
  ApiSecurity
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { Request } from 'express';
import { JwtService } from '@nestjs/jwt';
import { UserRole } from '../../entities/user.entity';

@ApiTags('auth')
@Controller('auth')
@UseGuards(ThrottlerGuard)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User login',
    description: 'Authenticate user with email/username and password. Returns JWT access token and refresh token.'
  })
  @ApiBody({
    type: LoginDto,
    description: 'User login credentials',
    examples: {
      'student-login': {
        summary: 'Student Login',
        description: 'Example login for a student user',
        value: {
          email: '<EMAIL>',
          password: 'password123',
          deviceName: 'Chrome Browser',
          deviceType: 'web'
        }
      },
      'instructor-login': {
        summary: 'Instructor Login',
        description: 'Example login for an instructor',
        value: {
          username: 'dr.smith',
          password: 'securepass456',
          deviceName: 'Safari Browser',
          deviceType: 'web'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    schema: {
      type: 'object',
      properties: {
        accessToken: { type: 'string', description: 'JWT access token' },
        refreshToken: { type: 'string', description: 'JWT refresh token' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string', description: 'User ID' },
            email: { type: 'string', description: 'User email' },
            username: { type: 'string', description: 'Username' },
            name: { type: 'string', description: 'Full name' },
            role: { type: 'string', enum: ['student', 'instructor', 'admin'] },
            isEmailVerified: { type: 'boolean' },
            lastLoginAt: { type: 'string', format: 'date-time' }
          }
        },
        expiresIn: { type: 'number', description: 'Token expiration time in seconds' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  @ApiResponse({ status: 429, description: 'Too many login attempts' })
  @ApiResponse({ status: 423, description: 'Account locked due to too many failed attempts' })
  async login(@Body() loginDto: LoginDto) {
    const identifier = loginDto.email || loginDto.username || 'unknown';
    console.log(`[AUTH] Login attempt for user: ${identifier}`);
    try {
      // Convert LoginDto to LoginRequest with device information
      // Use email if provided, otherwise use username as email for lookup
      const loginRequest = {
        email: loginDto.email || loginDto.username || '',
        password: loginDto.password,
        deviceId: loginDto.deviceId,
        twoFactorToken: loginDto.twoFactorToken,
        deviceInfo: {
          name: loginDto.deviceName,
          type: loginDto.deviceType,
          os: loginDto.deviceOs,
          browser: loginDto.deviceBrowser,
          browserVersion: loginDto.deviceBrowserVersion,
          token: loginDto.deviceToken,
          userAgent: loginDto.userAgent,
          ipAddress: loginDto.ipAddress
        },
        rememberMe: loginDto.rememberMe
      };
      const result = await this.authService.login(loginRequest);
      console.log(`[AUTH] Login successful for user: ${identifier}`);
      return result;
    } catch (error) {
      console.error(`[AUTH] Login failed for user: ${identifier}`, error.message);
      throw error;
    }
  }
  @Post('register')
  async register(@Body() userDto: RegisterDto) {
    console.log(`[AUTH] Registration attempt for user: ${userDto.email}, name: ${userDto.name}, role: ${userDto.role}, username: ${userDto.username}`);
    try {
      // Convert RegisterDto to RegisterRequest
      const registerRequest = {
        email: userDto.email,
        password: userDto.password,
        firstName: userDto.name.split(' ')[0],
        lastName: userDto.name.split(' ').slice(1).join(' ') || undefined,
        username: userDto.username,
        role: userDto.role
      };
      const result = await this.authService.register(registerRequest);
      console.log(`[AUTH] Registration successful for user: ${userDto.email}`);
      return result;
    } catch (error) {
      console.error(`[AUTH] Registration failed for user: ${userDto.email}`, error.message);

      // Check if the error is an UnauthorizedException with a suggested username
      if (error instanceof UnauthorizedException) {
        const errorResponse = error.getResponse();

        // If the error response is an object with a suggestedUsername property
        if (typeof errorResponse === 'object' && errorResponse && 'suggestedUsername' in errorResponse) {
          // Pass through the error with the suggested username
          throw error;
        }

        // Handle other UnauthorizedException cases
        if (typeof errorResponse === 'string') {
          if (errorResponse.includes('email already exists')) {
            throw new UnauthorizedException(`Registration failed: Email ${userDto.email} is already in use`);
          } else if (errorResponse.includes('username already exists')) {
            throw new UnauthorizedException(`Registration failed: Username ${userDto.username} is already in use`);
          }
        }
      }

      // Handle database constraint errors
      if (error.message && error.message.includes('duplicate key value')) {
        if (error.message.includes('email') || error.message.includes('users_email_key')) {
          throw new UnauthorizedException(`Registration failed: Email ${userDto.email} is already in use`);
        } else if (error.message.includes('username') || error.message.includes('UQ_c0d176bcc1665dc7cb60482c817')) {
          throw new UnauthorizedException({
            message: `Registration failed: Username ${userDto.username} is already in use`,
            suggestedUsername: `${userDto.username}${Math.floor(Math.random() * 900) + 100}`
          });
        } else {
          throw new UnauthorizedException('Registration failed: A user with these details already exists');
        }
      }

      throw error;
    }
  }

  @UseGuards(JwtAuthGuard)
  @Post('protected')
  protectedRoute() {
    return { message: 'This is a protected route' };
  }

  @Get('session')
  async getSession(@Req() req: Request) {
    // Check if there's an authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { user: null };
    }

    try {
      // Extract the token
      const token = authHeader.split(' ')[1];
      // Validate the token and get user info
      const user = await this.authService.validateToken(token);
      return { user };
    } catch (error) {
      return { user: null };
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  async getProfile(@Req() req: Request) {
    console.log('[AUTH-CONTROLLER] Profile request received, user:', req.user);
    try {
      return req.user;
    } catch (error) {
      console.error('[AUTH-CONTROLLER] Error in getProfile:', error.message);
      throw error;
    }
  }

  @Get('test-auth')
  async testAuth() {
    return { message: 'This endpoint does not require authentication' };
  }

  @Get('test-token')
  async testToken(@Headers('authorization') authHeader: string) {
    console.log('[AUTH-CONTROLLER] Test token endpoint accessed, authHeader:', authHeader);

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { message: 'No token provided' };
    }

    const token = authHeader.substring(7);
    try {
      // Manually verify the token
      const jwtService = new JwtService({
        secret: 'medical-app-secure-jwt-secret-key-2025-updated-with-stronger-key-for-security'
      });

      const decoded = jwtService.verify(token);
      console.log('[AUTH-CONTROLLER] Decoded token:', decoded);

      return {
        message: 'Token is valid',
        decoded
      };
    } catch (error) {
      console.error('[AUTH-CONTROLLER] Error verifying token:', error.message);
      return {
        message: 'Invalid token',
        error: error.message
      };
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('test-protected')
  async testProtected(@Req() req: Request) {
    console.log('[AUTH-CONTROLLER] Test protected endpoint accessed, user:', req.user);
    return { message: 'This endpoint is protected and requires authentication', user: req.user };
  }

  @Get('create-test-users')
  async createTestUsers() {
    console.log('[AUTH-CONTROLLER] Creating test users');
    try {
      // Check if test users already exist
      const existingTestUser = await this.authService.findUserByEmail('<EMAIL>');
      const existingAdminUser = await this.authService.findUserByEmail('<EMAIL>');

      let testUser = null;
      let adminUser = null;

      // Create test user if it doesn't exist
      if (!existingTestUser) {
        testUser = await this.authService.register({
          email: '<EMAIL>',
          username: 'test',
          password: 'Test123!@#',
          firstName: 'Test',
          lastName: 'User',
          role: 'student'
        });
      } else {
        testUser = { user: existingTestUser };
      }

      // Create admin user if it doesn't exist
      if (!existingAdminUser) {
        adminUser = await this.authService.register({
          email: '<EMAIL>',
          username: 'admin',
          password: 'Admin123!@#',
          firstName: 'Admin',
          lastName: 'User',
          role: 'admin'
        });
      } else {
        adminUser = { user: existingAdminUser };
      }

      return {
        message: 'Test users created successfully',
        credentials: {
          testUser: {
            email: '<EMAIL>',
            username: 'test',
            password: 'Test123!@#'
          },
          adminUser: {
            email: '<EMAIL>',
            username: 'admin',
            password: 'Admin123!@#'
          }
        },
        testUser: testUser?.user,
        adminUser: adminUser?.user
      };
    } catch (error) {
      console.error('[AUTH-CONTROLLER] Error creating test users:', error.message);
      throw new BadRequestException({
        message: 'Error creating test users',
        error: error.message
      });
    }
  }
}
