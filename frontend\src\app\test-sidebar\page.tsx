'use client';

import React, { useState } from 'react';
import { Menu, X, Stethoscope } from 'lucide-react';

export default function TestSidebar() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <nav
        className={`
        fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
        w-64 bg-white border-r border-gray-200 flex flex-col
      `}
      >
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Stethoscope className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-xl font-bold">MedTrack Hub</h1>
              <p className="text-xs text-gray-500">Medical Education</p>
            </div>
          </div>

          <button
            type="button"
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-lg hover:bg-gray-100"
            aria-label="Close sidebar"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4">
          <div className="space-y-1 px-2">
            <a
              href="#"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md bg-blue-100 text-blue-700"
            >
              Dashboard
            </a>
            <a
              href="#"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-100"
            >
              Courses
            </a>
            <a
              href="#"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-100"
            >
              Schedule
            </a>
          </div>
        </nav>
      </nav>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between p-4">
            <button
              type="button"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              aria-label="Toggle sidebar"
            >
              <Menu className="h-6 w-6" />
            </button>

            <h1 className="text-lg font-semibold">Sidebar Test</h1>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">Sidebar Toggle Test</h2>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="mb-4">
                This is a test page to verify the sidebar toggle functionality.
              </p>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold mb-2">Current State</h3>
                    <p><strong>Sidebar:</strong> {sidebarOpen ? 'Open' : 'Closed'}</p>
                    <p><strong>Screen:</strong> <span className="lg:hidden">Mobile/Tablet</span><span className="hidden lg:inline">Desktop</span></p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold mb-2">Test Controls</h3>
                    <div className="space-y-2">
                      <button
                        onClick={() => setSidebarOpen(!sidebarOpen)}
                        className="w-full px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                      >
                        Toggle Sidebar
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Testing Instructions:</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-blue-600 mb-1">Mobile/Tablet (&lt; 1024px)</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                        <li>Sidebar should be hidden by default</li>
                        <li>Click hamburger menu (☰) to open sidebar</li>
                        <li>Sidebar slides in from left with dark overlay</li>
                        <li>Click X button or overlay to close</li>
                        <li>Sidebar should slide out to the left</li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium text-green-600 mb-1">Desktop (≥ 1024px)</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                        <li>Sidebar should be visible by default</li>
                        <li>Sidebar is positioned statically (not overlay)</li>
                        <li>Content area adjusts automatically</li>
                        <li>Collapse button (chevron) should be visible</li>
                        <li>Click collapse to minimize sidebar</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
