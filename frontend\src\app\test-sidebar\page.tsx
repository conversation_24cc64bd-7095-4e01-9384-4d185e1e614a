'use client';

import React, { useState } from 'react';
import { Menu, X, Stethoscope } from 'lucide-react';

export default function TestSidebar() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <nav className={`
        fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
        w-64 bg-white border-r border-gray-200 flex flex-col
      `}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Stethoscope className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-xl font-bold">MedTrack Hub</h1>
              <p className="text-xs text-gray-500">Medical Education</p>
            </div>
          </div>
          
          <button
            type="button"
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-lg hover:bg-gray-100"
            aria-label="Close sidebar"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4">
          <div className="space-y-1 px-2">
            <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-md bg-blue-100 text-blue-700">
              Dashboard
            </a>
            <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-100">
              Courses
            </a>
            <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-100">
              Schedule
            </a>
          </div>
        </nav>
      </nav>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between p-4">
            <button
              type="button"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              aria-label="Toggle sidebar"
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <h1 className="text-lg font-semibold">Sidebar Test</h1>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">Sidebar Toggle Test</h2>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="mb-4">
                This is a test page to verify the sidebar toggle functionality.
              </p>
              <div className="space-y-2">
                <p><strong>Current state:</strong> {sidebarOpen ? 'Open' : 'Closed'}</p>
                <p><strong>Instructions:</strong></p>
                <ul className="list-disc list-inside space-y-1 text-gray-600">
                  <li>Click the hamburger menu (☰) in the header to toggle the sidebar</li>
                  <li>On mobile, the sidebar should slide in from the left with an overlay</li>
                  <li>On desktop, the sidebar should be visible by default</li>
                  <li>Click the X button in the sidebar to close it on mobile</li>
                  <li>Click the overlay to close the sidebar on mobile</li>
                </ul>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
