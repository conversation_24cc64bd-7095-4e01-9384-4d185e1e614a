import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DeepPartial } from 'typeorm';
import { UserSecuritySettings } from '../../../entities/security.entity';
import { BaseRepository } from '../../../common/repositories/base.repository';

@Injectable()
export class UserSecuritySettingsRepository extends BaseRepository<UserSecuritySettings> {
  constructor(
    @InjectRepository(UserSecuritySettings)
    repository: Repository<UserSecuritySettings>,
  ) {
    super(repository);
  }

  create(data: DeepPartial<UserSecuritySettings>): UserSecuritySettings {
    return this.repository.create(data);
  }

  async save(
    entity: UserSecuritySettings | DeepPartial<UserSecuritySettings>,
  ): Promise<UserSecuritySettings> {
    const saved = await this.repository.save(entity);
    return Array.isArray(saved) ? saved[0] : saved;
  }
}
