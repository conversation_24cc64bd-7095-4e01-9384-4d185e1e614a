# 🚀 GitHub Setup and Deployment Guide

## 📋 **Step 1: Create GitHub Repository**

### **1.1 Create Repository on GitHub**
1. Go to [GitHub.com](https://github.com)
2. Click "New repository" or go to [github.com/new](https://github.com/new)
3. Fill in repository details:
   - **Repository name**: `medtrack-hub` (or your preferred name)
   - **Description**: `A comprehensive medical education and tracking platform`
   - **Visibility**: Choose Public or Private
   - **DO NOT** initialize with README, .gitignore, or license (we already have these)

### **1.2 Connect Local Repository to GitHub**
```bash
# Add GitHub remote (replace with your repository URL)
git remote add origin https://github.com/YOUR_USERNAME/medtrack-hub.git

# Verify remote was added
git remote -v

# Push to GitHub
git branch -M main
git push -u origin main
```

## 🔐 **Step 2: Configure GitHub Secrets**

### **2.1 Required GitHub Secrets**
Go to: `Your Repository → Settings → Secrets and variables → Actions → New repository secret`

Add these secrets one by one:

#### **Database Secrets**
```
POSTGRES_PASSWORD
Value: your_production_database_password_here
```

#### **JWT Secrets** (Generate with: `openssl rand -base64 48`)
```
JWT_SECRET
Value: your_64_character_jwt_secret_here

JWT_REFRESH_SECRET  
Value: your_64_character_refresh_secret_here
```

#### **Redis Secret**
```
REDIS_PASSWORD
Value: your_redis_password_here
```

#### **Email Configuration**
```
EMAIL_USER
Value: <EMAIL>

EMAIL_PASSWORD
Value: your_app_password_here
```

#### **AWS Configuration** (if using AWS)
```
AWS_ACCESS_KEY_ID
Value: your_aws_access_key

AWS_SECRET_ACCESS_KEY
Value: your_aws_secret_key

AWS_S3_BUCKET
Value: your_s3_bucket_name
```

#### **Docker Registry** (for CI/CD)
```
DOCKER_REGISTRY_USERNAME
Value: your_docker_hub_username

DOCKER_REGISTRY_PASSWORD
Value: your_docker_hub_password_or_token
```

### **2.2 Environment-Specific Secrets**
For different environments, you can create environment-specific secrets:

#### **Staging Environment**
```
STAGING_DATABASE_URL
STAGING_JWT_SECRET
STAGING_REDIS_PASSWORD
```

#### **Production Environment**
```
PRODUCTION_DATABASE_URL
PRODUCTION_JWT_SECRET
PRODUCTION_REDIS_PASSWORD
```

## 🐳 **Step 3: Build and Test Docker Containers**

### **3.1 Generate Local Environment Files**
```bash
# Generate secure secrets and environment files
chmod +x scripts/generate-secrets.sh
./scripts/generate-secrets.sh

# This creates:
# - .env (root directory)
# - frontend/.env.local
# - backend/python_analytics/.env
```

### **3.2 Build Docker Images Locally**
```bash
# Build all images using docker-compose
docker-compose build

# Or build individual services
docker build -t medtrack-frontend ./frontend
docker build -t medtrack-backend ./backend
docker build -t medtrack-analytics ./backend/python_analytics
```

### **3.3 Test Development Environment**
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Check service health
./scripts/health-check.sh

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

### **3.4 Test Production Environment**
```bash
# Start production environment
docker-compose up -d

# Check service health
./scripts/health-check.sh

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🔧 **Step 4: Configure CI/CD Pipeline**

### **4.1 GitHub Actions Workflow**
The CI/CD pipeline is already configured in `.github/workflows/ci.yml`. It will:

1. **Run Tests**: Frontend, Backend, and Analytics tests
2. **Security Scan**: Vulnerability scanning with Trivy
3. **Build Images**: Docker images for all services
4. **Deploy**: Automatic deployment to staging/production

### **4.2 Enable GitHub Actions**
1. Go to your repository on GitHub
2. Click "Actions" tab
3. If prompted, click "I understand my workflows, go ahead and enable them"
4. The workflow will run automatically on push to `main` or `develop` branches

### **4.3 Configure Deployment Environments**
1. Go to `Repository → Settings → Environments`
2. Create environments:
   - **staging**: For staging deployments
   - **production**: For production deployments
3. Add environment-specific secrets and protection rules

## 🌐 **Step 5: Production Deployment Options**

### **Option A: Docker Compose (Simple)**
```bash
# On your production server
git clone https://github.com/YOUR_USERNAME/medtrack-hub.git
cd medtrack-hub

# Create production environment file
cp .env.example .env.production
# Edit .env.production with your production values

# Deploy
docker-compose -f docker-compose.yml --env-file .env.production up -d
```

### **Option B: Docker Swarm (Scalable)**
```bash
# Initialize Docker Swarm
docker swarm init

# Create secrets
echo "your_db_password" | docker secret create db_password -
echo "your_jwt_secret" | docker secret create jwt_secret -

# Deploy stack
docker stack deploy -c docker-compose.yml medtrack
```

### **Option C: Kubernetes (Enterprise)**
```bash
# Create namespace
kubectl create namespace medtrack

# Create secrets
kubectl create secret generic medtrack-secrets \
  --from-literal=postgres-password=your_password \
  --from-literal=jwt-secret=your_jwt_secret \
  -n medtrack

# Deploy (requires k8s manifests)
kubectl apply -f k8s/ -n medtrack
```

### **Option D: Cloud Platforms**

#### **AWS ECS**
```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin YOUR_ECR_URI
docker build -t medtrack-frontend ./frontend
docker tag medtrack-frontend:latest YOUR_ECR_URI/medtrack-frontend:latest
docker push YOUR_ECR_URI/medtrack-frontend:latest
```

#### **Google Cloud Run**
```bash
# Build and deploy
gcloud run deploy medtrack-frontend \
  --image gcr.io/PROJECT_ID/medtrack-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

#### **Azure Container Instances**
```bash
# Deploy to Azure
az container create \
  --resource-group medtrack-rg \
  --name medtrack-app \
  --image your-registry/medtrack:latest \
  --environment-variables NODE_ENV=production
```

## 🔍 **Step 6: Monitoring and Health Checks**

### **6.1 Health Check Endpoints**
- **Frontend**: `https://yourdomain.com/api/health`
- **Backend**: `https://api.yourdomain.com/api/health`
- **Analytics**: `https://analytics.yourdomain.com/health`

### **6.2 Set Up Monitoring**
```bash
# Add to crontab for regular health checks
crontab -e

# Add this line (check every 5 minutes)
*/5 * * * * /path/to/medtrack-hub/scripts/health-check.sh
```

### **6.3 Log Monitoring**
```bash
# View real-time logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f frontend
docker-compose logs -f backend
docker-compose logs -f analytics
```

## 🔒 **Step 7: Security Best Practices**

### **7.1 Environment Security**
- ✅ Use different secrets for each environment
- ✅ Rotate secrets every 90 days
- ✅ Use strong, randomly generated passwords
- ✅ Never commit secrets to Git
- ✅ Use HTTPS in production
- ✅ Enable firewall rules

### **7.2 Container Security**
- ✅ Non-root users in containers
- ✅ Minimal base images (Alpine/slim)
- ✅ Regular security updates
- ✅ Vulnerability scanning
- ✅ Resource limits

### **7.3 Network Security**
```bash
# Example firewall rules (Ubuntu/Debian)
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 3000   # Block direct frontend access
ufw deny 3002   # Block direct backend access
ufw deny 5000   # Block direct analytics access
ufw enable
```

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Build Failures**
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker-compose build --no-cache
```

#### **Network Issues**
```bash
# Check Docker networks
docker network ls

# Recreate networks
docker-compose down
docker-compose up -d
```

#### **Permission Issues**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

#### **Secret Issues**
```bash
# Verify environment variables
docker-compose config

# Check secret loading
docker-compose exec backend env | grep JWT
```

## 📞 **Support**

If you encounter issues:

1. **Check Logs**: `docker-compose logs -f`
2. **Run Health Checks**: `./scripts/health-check.sh`
3. **Verify Configuration**: `docker-compose config`
4. **Check GitHub Issues**: Repository issues page
5. **Review Documentation**: `docs/` directory

---

**🎉 Your MedTrack Hub is now ready for production deployment!**
