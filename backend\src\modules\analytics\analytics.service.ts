import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UnitQuizScore } from './unit-quiz-score.entity';

@Injectable()
export class AnalyticsService {
  private readonly analyticsUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRepository(UnitQuizScore)
    private unitQuizScoreRepo: Repository<UnitQuizScore>,
  ) {
    const url = this.configService.get<string>('ANALYTICS_SERVICE_URL');
    if (!url) {
      throw new Error('ANALYTICS_SERVICE_URL is not defined');
    }
    this.analyticsUrl = url;
  }

  async getLearningPatterns(userId: string) {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(
          `${this.analyticsUrl}/api/analytics/learning-patterns/${userId}`,
        ),
      );
      return data;
    } catch (error) {
      throw new HttpException(
        'Failed to fetch learning patterns',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getRecommendations(userId: string) {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(
          `${this.analyticsUrl}/api/analytics/recommendations/${userId}`,
        ),
      );
      return data;
    } catch (error) {
      throw new HttpException(
        'Failed to fetch recommendations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getPerformanceMetrics(userId: string) {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(
          `${this.analyticsUrl}/api/analytics/performance/${userId}`,
        ),
      );
      return data;
    } catch (error) {
      throw new HttpException(
        'Failed to fetch performance metrics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getPeerBenchmarks(userId: string) {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(
          `${this.analyticsUrl}/api/analytics/benchmarks/${userId}`,
        ),
      );
      return data;
    } catch (error) {
      throw new HttpException(
        'Failed to fetch peer benchmarks',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async trackEvent(
    userId: string,
    eventType: string,
    data: any,
  ): Promise<void> {
    await firstValueFrom(
      this.httpService.post(`${this.analyticsUrl}/events`, {
        userId,
        eventType,
        data,
        timestamp: new Date(),
      }),
    );
  }

  async trackPageView(
    userId: string,
    page: string,
    metadata: any,
  ): Promise<void> {
    await firstValueFrom(
      this.httpService.post(`${this.analyticsUrl}/pageviews`, {
        userId,
        page,
        metadata,
        timestamp: new Date(),
      }),
    );
  }

  async trackError(userId: string, error: Error, context: any): Promise<void> {
    await firstValueFrom(
      this.httpService.post(`${this.analyticsUrl}/errors`, {
        userId,
        error: {
          message: error.message,
          stack: error.stack,
        },
        context,
        timestamp: new Date(),
      }),
    );
  }

  async trackPerformance(userId: string, metrics: any): Promise<void> {
    await firstValueFrom(
      this.httpService.post(`${this.analyticsUrl}/performance`, {
        userId,
        metrics,
        timestamp: new Date(),
      }),
    );
  }

  async recordUnitQuizScore(userId: string, unitId: string, score: number) {
    // TODO: Store or update the user's unit quiz score in the database
    // Example: await this.unitQuizScoreRepo.save({ userId, unitId, score });
  }

  async getUnitCompletionRate(unitId: string) {
    // TODO: Query DB for all users who passed the unit quiz (score >= 70)
    // Example: const passed = await this.unitQuizScoreRepo.count({ where: { unitId, score: MoreThanOrEqual(70) } });
    // const total = await this.unitQuizScoreRepo.count({ where: { unitId } });
    // return total > 0 ? Math.round((passed / total) * 100) : 0;
  }

  async unitLevelGapAnalysis(userId: string, unitId: string) {
    // TODO: Analyze which topics/questions in the unit are most often missed by this user
    // Example: Aggregate incorrect answers by topic for this user's attempts
  }
}
