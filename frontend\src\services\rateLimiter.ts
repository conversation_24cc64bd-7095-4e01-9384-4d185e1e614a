interface RateLimitConfig {
  maxRequests: number;
  timeWindow: number; // in milliseconds
}

interface RateLimitState {
  requests: number;
  resetTime: number;
}

class RateLimiter {
  private static instance: RateLimiter;
  private limits: Map<string, RateLimitConfig>;
  private states: Map<string, RateLimitState>;

  private constructor() {
    this.limits = new Map();
    this.states = new Map();
  }

  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  setLimit(endpoint: string, config: RateLimitConfig): void {
    this.limits.set(endpoint, config);
  }

  async checkLimit(endpoint: string): Promise<boolean> {
    const config = this.limits.get(endpoint);
    if (!config) return true; // No limit set for this endpoint

    const now = Date.now();
    const state = this.states.get(endpoint) || { requests: 0, resetTime: now + config.timeWindow };

    // Reset if time window has passed
    if (now > state.resetTime) {
      state.requests = 0;
      state.resetTime = now + config.timeWindow;
    }

    // Check if limit is exceeded
    if (state.requests >= config.maxRequests) {
      return false;
    }

    // Increment request count
    state.requests++;
    this.states.set(endpoint, state);
    return true;
  }

  getRemainingRequests(endpoint: string): number {
    const config = this.limits.get(endpoint);
    if (!config) return Infinity;

    const state = this.states.get(endpoint);
    if (!state) return config.maxRequests;

    return Math.max(0, config.maxRequests - state.requests);
  }

  getResetTime(endpoint: string): number {
    const state = this.states.get(endpoint);
    return state?.resetTime || Date.now();
  }

  reset(endpoint: string): void {
    this.states.delete(endpoint);
  }
}

export const rateLimiter = RateLimiter.getInstance();

// Default rate limits
rateLimiter.setLimit('auth/login', { maxRequests: 5, timeWindow: 60000 }); // 5 requests per minute
rateLimiter.setLimit('auth/register', { maxRequests: 3, timeWindow: 3600000 }); // 3 requests per hour
rateLimiter.setLimit('auth/forgot-password', { maxRequests: 3, timeWindow: 3600000 }); // 3 requests per hour
rateLimiter.setLimit('api/*', { maxRequests: 100, timeWindow: 60000 }); // 100 requests per minute for general API
