const { Client } = require('pg');
require('dotenv').config();

async function fixDatabaseSchema() {
  const client = new Client({
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT) || 5432,
    user: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
  });

  try {
    console.log('Connecting to PostgreSQL...');
    await client.connect();
    console.log('✅ Connected to PostgreSQL');

    // Create the UserRole enum type if it doesn't exist
    console.log('Creating UserRole enum type...');
    await client.query(`
      DO $$ BEGIN
        CREATE TYPE "user_role_enum" AS ENUM ('student', 'teacher', 'admin');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✅ UserRole enum type created/verified');

    // Drop and recreate the users table with proper schema
    console.log('Recreating users table...');
    await client.query('DROP TABLE IF EXISTS "user" CASCADE');
    
    await client.query(`
      CREATE TABLE "user" (
        "id" SERIAL PRIMARY KEY,
        "username" varchar UNIQUE NOT NULL,
        "email" varchar UNIQUE NOT NULL,
        "password_hash" varchar NOT NULL,
        "first_name" varchar,
        "last_name" varchar,
        "bio" varchar,
        "phone_number" varchar,
        "role" "user_role_enum" NOT NULL DEFAULT 'student',
        "is_locked" boolean NOT NULL DEFAULT false,
        "locked_until" timestamp,
        "failed_login_attempts" integer NOT NULL DEFAULT 0,
        "last_login" timestamp,
        "created_at" timestamp NOT NULL DEFAULT now(),
        "updated_at" timestamp NOT NULL DEFAULT now()
      )
    `);
    console.log('✅ Users table created successfully');

    // Create a test user
    console.log('Creating test user...');
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('test', 10);
    
    await client.query(`
      INSERT INTO "user" (username, email, password_hash, first_name, last_name, role)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (username) DO NOTHING
    `, ['test', '<EMAIL>', hashedPassword, 'Test', 'User', 'student']);
    
    console.log('✅ Test user created successfully');
    console.log('✅ Database schema fixed successfully');

  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
  } finally {
    await client.end();
    console.log('✅ Database connection closed');
  }
}

fixDatabaseSchema();
