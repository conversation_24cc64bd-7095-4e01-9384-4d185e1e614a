import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FeedbackService } from './feedback.service';
import { FeedbackController } from './feedback.controller';
import { Feedback } from '../entities/feedback.entity';
import { User } from '../entities/user.entity';
import { Material } from '../entities/materials.entity';
import { Unit } from '../entities/unit.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Feedback, User, Material, Unit])],
  providers: [FeedbackService],
  controllers: [FeedbackController],
})
export class FeedbackModule {}
