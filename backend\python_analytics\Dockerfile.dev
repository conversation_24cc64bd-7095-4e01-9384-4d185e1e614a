# Development Dockerfile for Python Analytics
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies (including TensorFlow requirements)
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    python3-dev \
    libhdf5-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies including dev tools
RUN pip install --no-cache-dir --upgrade pip setuptools wheel \
    && pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir debugpy uvicorn[standard] watchfiles

# Copy source code
COPY . .

# Set environment variables for development
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PORT=5000
ENV PYTHONPATH=/app

# Expose the port and debug port
EXPOSE 5000 5678

# Start the development server with hot reloading
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5000", "--reload", "--reload-dir", "/app"]
