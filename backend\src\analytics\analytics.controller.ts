import { <PERSON>, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../modules/auth/jwt-auth.guard';
import { AnalyticsService } from './analytics.service';
import { RedisService } from '../redis/redis.service';

@Controller('analytics')
@UseGuards(JwtAuthGuard)
export class AnalyticsController {
  constructor(
    private readonly analyticsService: AnalyticsService,
    private readonly redisService: RedisService,
  ) {}

  @Post('events')
  async trackEvent(
    @Body()
    eventData: {
      user_id: string;
      event_type: string;
      data: any;
      timestamp: string;
    },
  ) {
    return this.analyticsService.trackEvent(eventData);
  }

  @Get('performance/:userId')
  async getPerformanceMetrics(@Param('userId') userId: string) {
    const cacheKey = `performance:${userId}`;
    const cachedData = await this.redisService.get(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    const metrics =
      await this.analyticsService.calculatePerformanceMetrics(userId);
    await this.redisService.set(cacheKey, JSON.stringify(metrics), 300); // Cache for 5 minutes
    return metrics;
  }

  @Get('predictions/:userId')
  async getPerformancePredictions(@Param('userId') userId: string) {
    const cacheKey = `predictions:${userId}`;
    const cachedData = await this.redisService.get(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    const predictions = await this.analyticsService.generatePredictions(userId);
    await this.redisService.set(cacheKey, JSON.stringify(predictions), 3600); // Cache for 1 hour
    return predictions;
  }

  @Get('study-patterns/:userId')
  async getStudyPatterns(@Param('userId') userId: string) {
    return this.analyticsService.analyzeStudyPatterns(userId);
  }

  @Post('events/batch')
  async batchTrackEvents(
    @Body()
    events: Array<{
      user_id: string;
      event_type: string;
      data: any;
      timestamp: string;
    }>,
  ) {
    return this.analyticsService.batchTrackEvents(events);
  }

  @Get('recommendations/:userId')
  async getStudyRecommendations(@Param('userId') userId: string) {
    const cacheKey = `recommendations:${userId}`;
    const cachedData = await this.redisService.get(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    const recommendations =
      await this.analyticsService.generateRecommendations(userId);
    await this.redisService.set(
      cacheKey,
      JSON.stringify(recommendations),
      1800,
    ); // Cache for 30 minutes
    return recommendations;
  }
}
