import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Unit } from '../../entities/unit.entity';

@Injectable()
export class UnitsService {
  constructor(
    @InjectRepository(Unit)
    private unitsRepository: Repository<Unit>,
  ) {}

  async findAll(): Promise<Unit[]> {
    return this.unitsRepository.find({
      relations: ['materials', 'progress'],
      order: {
        order_index: 'ASC',
      },
    });
  }

  async findOne(id: string): Promise<Unit> {
    const unit = await this.unitsRepository.findOne({
      where: { id },
      relations: ['materials', 'progress'],
    });

    if (!unit) {
      throw new NotFoundException(`Unit with ID ${id} not found`);
    }
    return unit;
  }

  async create(unitData: Partial<Unit>): Promise<Unit> {
    const newUnit = this.unitsRepository.create(unitData);
    return this.unitsRepository.save(newUnit);
  }

  async update(id: string, unitData: Partial<Unit>): Promise<Unit> {
    await this.unitsRepository.update(id, unitData);
    return this.findOne(id);
  }

  async delete(id: string): Promise<void> {
    const unit = await this.findOne(id);
    await this.unitsRepository.remove(unit);
  }
}
