import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Courses | MedTrack Hub',
  description: 'Browse and enroll in medical courses designed for healthcare professionals',
  keywords: ['medical courses', 'healthcare education', 'medical training', 'online learning'],
  openGraph: {
    title: 'Courses | MedTrack Hub',
    description: 'Browse and enroll in medical courses designed for healthcare professionals',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Courses | MedTrack Hub',
    description: 'Browse and enroll in medical courses designed for healthcare professionals',
  },
};

export default function CoursesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
