export const materialFixture = {
  title: 'Test Material',
  description: 'Test Description',
  type: 'document',
};

export const materialFixtureWithFileUrl2 = {
  title: 'Test Material',
  description: 'Test Description',
  type: 'test',
  file_url: 'http://example.com/test.pdf',
};

export const materialFixtureWithId = {
  id: 1,
  title: 'Test Material',
  description: 'Test Description',
  type: 'test',
  file_url: 'http://example.com/test.pdf',
};

export const materialFixtureWithIdAndProgress = {
  id: 1,
  title: 'Test Material',
  description: 'Test Description',
  type: 'test',
  file_url: 'http://example.com/test.pdf',
  progress: {
    id: 1,
    quality: 5,
    interval: 1,
    nextReviewDate: new Date(),
    easeFactor: 2.5,
  },
};

export const materialFixtureWithIdAndProgressAndUser = {
  id: 1,
  title: 'Test Material',
  description: 'Test Description',
  type: 'test',
  file_url: 'http://example.com/test.pdf',
  progress: {
    id: 1,
    quality: 5,
    interval: 1,
    nextReviewDate: new Date(),
    easeFactor: 2.5,
    userId: 1,
  },
};

export const materialFixtureWithIdAndProgressAndUserAndMaterial = {
  id: 1,
  title: 'Test Material',
  description: 'Test Description',
  type: 'test',
  file_url: 'http://example.com/test.pdf',
  progress: {
    id: 1,
    quality: 5,
    interval: 1,
    nextReviewDate: new Date(),
    easeFactor: 2.5,
    userId: 1,
    materialId: 1,
  },
};

export const materialFixtureWithIdAndProgressAndUserAndMaterialAndDate = {
  id: 1,
  title: 'Test Material',
  description: 'Test Description',
  type: 'test',
  file_url: 'http://example.com/test.pdf',
  progress: {
    id: 1,
    quality: 5,
    interval: 1,
    nextReviewDate: new Date('2023-01-01T00:00:00Z'),
    easeFactor: 2.5,
    userId: 1,
    materialId: 1,
  },
};

export const materialFixtureWithIdAndProgressAndUserAndMaterialAndDateAndQuality =
  {
    id: 1,
    title: 'Test Material',
    description: 'Test Description',
    type: 'test',
    file_url: 'http://example.com/test.pdf',
    progress: {
      id: 1,
      quality: 5,
      interval: 1,
      nextReviewDate: new Date('2023-01-01T00:00:00Z'),
      easeFactor: 2.5,
      userId: 1,
      materialId: 1,
    },
  };

export const materialFixtureWithFileUrl = {
  title: 'Test Material',
  description: 'Test Description',
  type: 'document',
  file_url: 'http://example.com/file.pdf',
};
