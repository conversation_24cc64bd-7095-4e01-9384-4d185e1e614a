import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class PasswordValidationPipe implements PipeTransform {
  transform(value: any) {
    if (!value.password) {
      return value;
    }

    const passwordRegex = /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/;
    const passwordErrors = [];

    if (value.password.length < 8) {
      passwordErrors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(value.password)) {
      passwordErrors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(value.password)) {
      passwordErrors.push('Password must contain at least one lowercase letter');
    }
    if (!/[0-9]/.test(value.password)) {
      passwordErrors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*]/.test(value.password)) {
      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
    }

    if (!passwordRegex.test(value.password)) {
      throw new BadRequestException({
        message: 'Password does not meet security requirements',
        details: passwordErrors
      });
    }
    return value;
  }
}
