-- Setup script for MedTrack Hub database
-- Run this as the postgres superuser

-- Create the medical user
CREATE USER medical WITH PASSWORD 'AU110s/6081/2021MT';

-- Create the database
CREATE DATABASE medical_tracker OWNER medical;

-- Grant necessary privileges
GRANT ALL PRIVILEGES ON DATABASE medical_tracker TO medical;

-- Connect to the database and grant schema privileges
\c medical_tracker;
GRANT ALL ON SCHEMA public TO medical;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO medical;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO medical;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO medical;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO medical;

-- Verify the setup
\du medical;
\l medical_tracker;
