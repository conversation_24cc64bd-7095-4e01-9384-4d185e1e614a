'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import {
  BarChart3,
  BookOpen,
  Calendar,
  Target,
  Award,
  Users,
  Bot,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Stethoscope,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ResponsiveSidebarProps {
  className?: string;
}

export function ResponsiveSidebar({ className }: ResponsiveSidebarProps) {
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const { user } = useAuth();

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: BarChart3 },
    { name: 'Courses', href: '/courses', icon: BookO<PERSON> },
    { name: 'AI Tutor', href: '/chat', icon: Bo<PERSON> },
    { name: 'Schedule', href: '/schedule', icon: Calendar },
    { name: 'Goals', href: '/goals', icon: Target },
    { name: 'Achievements', href: '/achievements', icon: Award },
    { name: 'Study Groups', href: '/study-groups', icon: Users },
  ];

  const isActive = (path: string) => pathname === path;

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileOpen(false);
  }, [pathname]);

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="sm"
        className="lg:hidden fixed top-4 left-4 z-50 p-2"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
      >
        {isMobileOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Mobile overlay */}
      {isMobileOpen && (
        <div
          className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed lg:static inset-y-0 left-0 z-50 flex flex-col bg-white border-r border-gray-200 transition-all duration-300',
          // Mobile styles
          'lg:translate-x-0',
          isMobileOpen ? 'translate-x-0' : '-translate-x-full',
          // Desktop styles
          isCollapsed ? 'lg:w-16' : 'lg:w-64',
          // Mobile width
          'w-64',
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <Link
            href="/"
            className={cn(
              'flex items-center space-x-2 text-xl font-bold text-blue-600 transition-opacity',
              isCollapsed && 'lg:opacity-0 lg:pointer-events-none'
            )}
          >
            <Stethoscope className="h-6 w-6 flex-shrink-0" />
            <span className="truncate">MedTrack Hub</span>
          </Link>

          {/* Desktop collapse button */}
          <Button
            variant="ghost"
            size="sm"
            className="hidden lg:flex p-1"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>

          {/* Mobile close button */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden p-1"
            onClick={() => setIsMobileOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
          {navigation.map(item => {
            const Icon = item.icon;
            const active = isActive(item.href);

            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors group',
                  active
                    ? 'bg-blue-50 text-blue-600 border border-blue-200'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                  isCollapsed && 'lg:justify-center lg:px-2'
                )}
                title={isCollapsed ? item.name : undefined}
              >
                <Icon
                  className={cn(
                    'h-5 w-5 flex-shrink-0',
                    active ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500',
                    !isCollapsed && 'mr-3'
                  )}
                />
                <span
                  className={cn(
                    'truncate transition-opacity',
                    isCollapsed && 'lg:opacity-0 lg:w-0 lg:overflow-hidden'
                  )}
                >
                  {item.name}
                </span>
              </Link>
            );
          })}
        </nav>

        {/* User info */}
        {user && (
          <div className="border-t border-gray-200 p-4">
            <div
              className={cn(
                'flex items-center space-x-3',
                isCollapsed && 'lg:justify-center lg:space-x-0'
              )}
            >
              <div className="flex-shrink-0">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">
                    {user.name?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
              </div>
              <div
                className={cn(
                  'flex-1 min-w-0 transition-opacity',
                  isCollapsed && 'lg:opacity-0 lg:w-0 lg:overflow-hidden'
                )}
              >
                <p className="text-sm font-medium text-gray-900 truncate">{user.name}</p>
                <p className="text-xs text-gray-500 truncate">Medical Student</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
