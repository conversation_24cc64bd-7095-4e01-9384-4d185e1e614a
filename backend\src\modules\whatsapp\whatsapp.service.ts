import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { User } from '../../entities/user.entity';
import { TopicProgress } from '../../entities/topic-progress.entity';

@Injectable()
export class WhatsAppService {
  private readonly apiUrl: string;
  private readonly apiToken: string;

  constructor(private configService: ConfigService) {
    const apiUrl = this.configService.get<string>('WHATSAPP_API_URL');
    const apiToken = this.configService.get<string>('WHATSAPP_API_TOKEN');

    if (!apiUrl || !apiToken) {
      throw new Error('WhatsApp API configuration is missing');
    }

    this.apiUrl = apiUrl;
    this.apiToken = apiToken;
  }

  async sendWelcomeMessage(user: User): Promise<void> {
    const message = `Welcome to our platform! We're excited to have you on board.`;
    return this.sendMessage(user.phone_number, message);
  }

  async sendStudyReminder(user: User): Promise<void> {
    const message = `Don't forget to study today! Your learning streak is at ${user.streak_days} days.`;
    return this.sendMessage(user.phone_number, message);
  }

  async sendAchievementNotification(
    user: User,
    achievement: string,
  ): Promise<void> {
    const message = `Congratulations! You've earned a new achievement: ${achievement}`;
    return this.sendMessage(user.phone_number, message);
  }

  async sendWeeklyDigest(user: User, digest: any): Promise<void> {
    const message = `Your weekly learning summary:\n${JSON.stringify(digest, null, 2)}`;
    return this.sendMessage(user.phone_number, message);
  }

  async sendCustomNotification(user: User, message: string): Promise<void> {
    return this.sendMessage(user.phone_number, message);
  }

  async sendProgressUpdate(user: User, progress: TopicProgress) {
    const message = `📊 Progress Update\n\nTopic: ${progress.topic.title}\nCompletion: ${progress.completion_percentage}%\nTime Spent: ${progress.time_spent_minutes} minutes\n\nKeep going! 🌟`;

    return this.sendMessage(user.phone_number, message);
  }

  async sendStreakAlert(user: User) {
    const message = `🔥 Streak Alert!\n\nYou've maintained your study streak for ${user.streak_days} days!\n\nDon't break the chain! 💪`;

    return this.sendMessage(user.phone_number, message);
  }

  async sendGroupStudyInvite(
    user: User,
    groupName: string,
    inviteLink: string,
  ) {
    const message = `👥 Study Group Invitation\n\nYou've been invited to join ${groupName}!\n\nClick here to join: ${inviteLink}\n\nStudy together, achieve more! 📚`;

    return this.sendMessage(user.phone_number, message);
  }

  async sendDailyDigest(user: User, stats: any) {
    const message = `📈 Daily Study Digest\n\nTopics Completed: ${stats.completedTopics}\nTime Studied: ${stats.totalMinutes} minutes\nStreak: ${user.streak_days} days\n\nGreat job today! 🎉`;

    return this.sendMessage(user.phone_number, message);
  }

  private async sendMessage(phone_number: string, message: string) {
    try {
      const response = await axios.post(
        `${this.apiUrl}/messages`,
        {
          messaging_product: 'whatsapp',
          to: phone_number,
          type: 'text',
          text: { body: message },
        },
        {
          headers: {
            Authorization: `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error) {
      console.error('WhatsApp API Error:', error);
      throw new Error('Failed to send WhatsApp message');
    }
  }

  async handleIncomingMessage(message: any) {
    // Handle incoming WhatsApp messages
    // This could be used for interactive features like:
    // - Quick study session start/end
    // - Progress checks
    // - Topic selection
    // - Group study coordination

    const { from, text } = message;

    // Example: Handle "start study" command
    if (text.toLowerCase().includes('start study')) {
      // Logic to start a study session
      return this.handleStartStudyCommand(from);
    }

    // Example: Handle "check progress" command
    if (text.toLowerCase().includes('check progress')) {
      // Logic to check progress
      return this.handleCheckProgressCommand(from);
    }

    return null;
  }

  private async handleStartStudyCommand(phone_number: string) {
    // Implementation for starting study session via WhatsApp
    // This would integrate with the StudyService
  }

  private async handleCheckProgressCommand(phone_number: string) {
    // Implementation for checking progress via WhatsApp
    // This would integrate with the StudyService
  }
}
