import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Material, MaterialType } from '../../entities/materials.entity';
import * as AWS from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { MaterialShare } from '../../entities/material_shares.entity';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { Progress } from '../../entities/progress.entity';
@Injectable()
export class MaterialsService {
  private s3: S3Client;

  constructor(
    @InjectRepository(Material)
    private materialsRepository: Repository<Material>,
    @InjectRepository(MaterialShare)
    private materialShareRepository: Repository<MaterialShare>,
    private configService: ConfigService,
  ) {
    this.s3 = new S3Client({
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID')!,
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        )!,
      },
      region: this.configService.get<string>('AWS_REGION'),
    });
  }

  async create(material: Partial<Material>): Promise<Material> {
    const newMaterial = this.materialsRepository.create(material);
    return this.materialsRepository.save(newMaterial);
  }

  async findAll(): Promise<Material[]> {
    return this.materialsRepository.find({ relations: ['uploadedBy', 'unit'] });
  }

  async findOne(id: string): Promise<Material> {
    const material = await this.materialsRepository.findOne({
      where: { id },
      relations: ['uploadedBy', 'unit'],
    });
    if (!material) {
      throw new NotFoundException(`Material with ID ${id} not found`);
    }
    return material;
  }

  async remove(id: string): Promise<void> {
    const result = await this.materialsRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Material with ID ${id} not found`);
    }
  }

  async uploadFile(
    file: Express.Multer.File,
    userId: string,
    unitId: string,
    title: string,
    description?: string,
    type?: string,
  ): Promise<Material> {
    if (!file) throw new Error('No file uploaded');

    // Validate file type
    const allowedMimeTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'application/zip',
      'application/x-rar-compressed',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      console.error(`[MATERIALS-SERVICE] Invalid file type: ${file.mimetype}`);
      throw new Error(
        `Invalid file type. Allowed types: PDF, images, Office documents, text, zip, and rar files.`,
      );
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      console.error(`[MATERIALS-SERVICE] File too large: ${file.size} bytes`);
      throw new Error(`File too large. Maximum size is 10MB.`);
    }

    const bucket = this.configService.get<string>('AWS_S3_BUCKET');
    if (!bucket) throw new Error('AWS S3 bucket not configured');

    // Sanitize filename to prevent path traversal attacks
    const sanitizedFilename = file.originalname.replace(/[^\w\s.-]/g, '');
    const fileKey = `materials/${uuidv4()}-${sanitizedFilename}`;

    console.log(
      `[MATERIALS-SERVICE] Uploading file: ${sanitizedFilename}, size: ${file.size} bytes, type: ${file.mimetype}`,
    );

    const command = new PutObjectCommand({
      Bucket: bucket,
      Key: fileKey,
      Body: file.buffer,
      ContentType: file.mimetype,
      // Add additional security headers
      Metadata: {
        'x-amz-meta-original-filename': sanitizedFilename,
        'x-amz-meta-upload-date': new Date().toISOString(),
        'x-amz-meta-user-id': userId,
      },
    });

    const uploadResult = await this.s3.send(command);
    const fileUrl = `https://${bucket}.s3.${this.configService.get<string>('AWS_REGION')}.amazonaws.com/${fileKey}`;

    console.log(`[MATERIALS-SERVICE] File uploaded successfully: ${fileUrl}`);

    const material = this.materialsRepository.create({
      title: title || sanitizedFilename,
      description,
      type: (type as MaterialType) || MaterialType.ARTICLE,
      file_url: fileUrl,
      author: { id: userId },
      unit: { id: unitId },
    });

    return this.materialsRepository.save(material);
  }

  async shareMaterial(
    materialId: string,
    sharedByUserId: string,
    sharedWithUserId: string,
  ): Promise<MaterialShare> {
    const share = this.materialShareRepository.create({
      material: { id: materialId },
      shared_by_user: { id: sharedByUserId },
      shared_with_user: { id: sharedWithUserId },
    });
    return this.materialShareRepository.save(share);
  }

  async findSharedMaterials(userId: string): Promise<MaterialShare[]> {
    return this.materialShareRepository.find({
      where: { shared_with_user: { id: userId } },
      relations: ['material', 'shared_by_user'],
    });
  }

  async findProgressByMaterialId(materialId: string): Promise<Progress[]> {
    return this.materialsRepository
      .findOne({
        where: { id: materialId },
        relations: ['progress'],
      })
      .then((material: Material | null) => material?.progress || []);
  }

  async findProgressByUserId(userId: string): Promise<Progress[]> {
    return this.materialsRepository
      .findOne({
        where: { user: { id: userId } },
        relations: ['progress'],
      })
      .then((material: Material | null) => material?.progress || []);
  }
}
