import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import * as path from 'path';

export const TestConfig = {
  database: {
    type: 'sqlite' as const,
    database: ':memory:',
    entities: [path.join(__dirname, '../src/entities/*.entity{.ts,.js}')],
    synchronize: true,
  },
  jwt: {
    secret: 'test-secret',
    signOptions: { expiresIn: '1h' },
  },
};

export const TestModuleConfig = {
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => TestConfig],
    }),
    TypeOrmModule.forRoot(TestConfig.database),
    JwtModule.register(TestConfig.jwt),
  ],
};
export const TestModule = {
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => TestConfig],
    }),
    TypeOrmModule.forRoot(TestConfig.database),
    JwtModule.register(TestConfig.jwt),
  ],
};

export const TestModuleWithEntities = {
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => TestConfig],
    }),
    TypeOrmModule.forRoot(TestConfig.database),
    JwtModule.register(TestConfig.jwt),
  ],
};

export const TestModuleWithEntitiesAndProviders = {
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => TestConfig],
    }),
    TypeOrmModule.forRoot(TestConfig.database),
    JwtModule.register(TestConfig.jwt),
  ],
};

export const AdditionalTestConfig = {
  database: {
    type: 'sqlite',
    database: ':memory:',
    entities: ['src/**/*.entity.ts'],
    synchronize: true,
  },
  jwt: {
    secret: 'test-secret',
    signOptions: {
      expiresIn: '1h',
    },
  },
  currentDate: new Date().toISOString(),
};
export const ExtendedTestConfig = {
  database: {
    type: 'sqlite',
    database: ':memory:',
    entities: ['src/**/*.entity{.ts,.js}'],
    synchronize: true,
  },
  jwt: {
    secret: 'test-secret',
    signOptions: { expiresIn: '1d' },
  },
  currentDate: new Date().toISOString(),
};

export const BaseTestConfig = {
  database: {
    type: 'sqlite',
    database: 'test.db',
    entities: ['src/**/*.entity.ts'],
    synchronize: true,
  },
  jwt: {
    secret: 'test-secret',
    signOptions: {
      expiresIn: '1d',
    },
  },
  user: {
    username: 'test-user',
  },
  currentDate: new Date().toISOString(),
};

export const ExtendedTestConfigWithUser = {
  database: {
    type: 'sqlite',
    database: 'test.db',
    entities: ['src/**/*.entity.ts'],
    synchronize: true,
  },
  jwt: {
    secret: 'test-secret',
    signOptions: {
      expiresIn: '1d',
    },
  },
  user: {
    username: 'test-user',
  },
  currentDate: new Date().toISOString(),
};

export const ExtendedTestConfigWithEntities = {
  database: {
    type: 'sqlite',
    database: 'test.db',
    entities: ['src/**/*.entity{.ts,.js}'],
    synchronize: true,
  },
  jwt: {
    secret: 'test-secret',
    signOptions: { expiresIn: '1d' },
  },
  user: {
    username: 'test-user',
  },
  currentDate: new Date().toISOString(),
};

export const MemoryTestConfig = {
  database: {
    type: 'sqlite',
    database: ':memory:',
    entities: ['src/**/*.entity.ts'],
    synchronize: true,
  },
  jwt: {
    secret: 'test-secret',
    signOptions: {
      expiresIn: '1h',
    },
  },
  user: {
    username: 'testuser',
  },
  currentDate: new Date().toISOString(),
};
