const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

async function testEndToEnd() {
  console.log('🚀 Starting End-to-End Authentication Tests...\n');

  try {
    // Test 1: Check if server is running
    console.log('📡 Test 1: Checking if backend server is running...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/auth/test-user`, {
        timeout: 5000
      });
      console.log('✅ Backend server is running');
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ Backend server is not running on port 3002');
        console.log('Please start the backend server first');
        return;
      } else {
        console.log('⚠️ Server is running but test-user endpoint may not be available');
      }
    }

    // Test 2: Test user registration
    console.log('\n🔐 Test 2: Testing user registration...');
    const registerData = {
      username: 'testuser2',
      email: '<EMAIL>',
      password: 'testpassword123',
      firstName: 'Test',
      lastName: 'User2',
      role: 'student'
    };

    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, registerData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ Registration successful:', {
        status: registerResponse.status,
        message: registerResponse.data.message,
        user: registerResponse.data.user ? {
          id: registerResponse.data.user.id,
          username: registerResponse.data.user.username,
          email: registerResponse.data.user.email,
          role: registerResponse.data.user.role
        } : 'No user data'
      });
    } catch (error) {
      if (error.response) {
        console.log('⚠️ Registration failed (expected if user exists):', {
          status: error.response.status,
          message: error.response.data.message || error.response.data
        });
      } else {
        console.log('❌ Registration request failed:', error.message);
        return;
      }
    }

    // Test 3: Test login with username
    console.log('\n🔑 Test 3: Testing login with username...');
    const loginData = {
      email: 'test', // Using username in email field (as frontend does)
      password: 'test'
    };

    try {
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, loginData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ Login successful:', {
        status: loginResponse.status,
        message: loginResponse.data.message,
        user: loginResponse.data.user ? {
          id: loginResponse.data.user.id,
          username: loginResponse.data.user.username,
          email: loginResponse.data.user.email,
          role: loginResponse.data.user.role
        } : 'No user data',
        hasToken: !!loginResponse.data.access_token
      });

      // Store token for further tests
      const token = loginResponse.data.access_token;

      // Test 4: Test protected endpoint with token
      if (token) {
        console.log('\n🛡️ Test 4: Testing protected endpoint with token...');
        try {
          const profileResponse = await axios.get(`${BASE_URL}/users/profile`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            timeout: 10000
          });
          
          console.log('✅ Protected endpoint access successful:', {
            status: profileResponse.status,
            user: profileResponse.data ? {
              id: profileResponse.data.id,
              username: profileResponse.data.username,
              email: profileResponse.data.email,
              role: profileResponse.data.role
            } : 'No user data'
          });
        } catch (error) {
          if (error.response) {
            console.log('❌ Protected endpoint access failed:', {
              status: error.response.status,
              message: error.response.data.message || error.response.data
            });
          } else {
            console.log('❌ Protected endpoint request failed:', error.message);
          }
        }
      }

    } catch (error) {
      if (error.response) {
        console.log('❌ Login failed:', {
          status: error.response.status,
          message: error.response.data.message || error.response.data
        });
      } else {
        console.log('❌ Login request failed:', error.message);
        return;
      }
    }

    // Test 5: Test login with email
    console.log('\n📧 Test 5: Testing login with email...');
    const emailLoginData = {
      email: '<EMAIL>',
      password: 'test'
    };

    try {
      const emailLoginResponse = await axios.post(`${BASE_URL}/auth/login`, emailLoginData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ Email login successful:', {
        status: emailLoginResponse.status,
        message: emailLoginResponse.data.message,
        hasToken: !!emailLoginResponse.data.access_token
      });
    } catch (error) {
      if (error.response) {
        console.log('❌ Email login failed:', {
          status: error.response.status,
          message: error.response.data.message || error.response.data
        });
      } else {
        console.log('❌ Email login request failed:', error.message);
      }
    }

    // Test 6: Test invalid login
    console.log('\n❌ Test 6: Testing invalid login...');
    const invalidLoginData = {
      email: 'test',
      password: 'wrongpassword'
    };

    try {
      const invalidLoginResponse = await axios.post(`${BASE_URL}/auth/login`, invalidLoginData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('⚠️ Invalid login unexpectedly succeeded:', invalidLoginResponse.data);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Invalid login correctly rejected:', {
          status: error.response.status,
          message: error.response.data.message || error.response.data
        });
      } else {
        console.log('❌ Unexpected error during invalid login test:', error.message);
      }
    }

    console.log('\n✅ End-to-End Authentication Tests Completed!');

  } catch (error) {
    console.error('❌ Unexpected error during E2E tests:', error.message);
  }
}

// Check if axios is available
if (typeof axios === 'undefined') {
  console.log('❌ axios is not available. Installing...');
  const { execSync } = require('child_process');
  try {
    execSync('npm install axios', { stdio: 'inherit' });
    console.log('✅ axios installed successfully');
  } catch (error) {
    console.log('❌ Failed to install axios:', error.message);
    process.exit(1);
  }
}

testEndToEnd();
