// Mock API service for testing when the backend is not available

// Mock materials data
const mockMaterials = [
  {
    id: '1',
    title: 'Anatomy Lecture Notes',
    description: 'Comprehensive notes on human anatomy systems.',
    type: 'pdf',
    author: 'Dr. <PERSON>',
    uploadDate: '2025-04-01T10:30:00Z',
    size: '2.4 MB',
    url: '/mock-files/anatomy.pdf',
  },
  {
    id: '2',
    title: 'Physiology Diagrams',
    description: 'Visual diagrams of physiological processes.',
    type: 'image',
    author: 'Dr. <PERSON>',
    uploadDate: '2025-04-02T14:15:00Z',
    size: '5.7 MB',
    url: '/mock-files/physiology.jpg',
  },
  {
    id: '3',
    title: 'Clinical Case Studies',
    description: 'Collection of interesting clinical cases for analysis.',
    type: 'doc',
    author: 'Dr. <PERSON>',
    uploadDate: '2025-04-03T09:45:00Z',
    size: '1.2 MB',
    url: '/mock-files/cases.docx',
  },
];

// Mock users data
const mockUsers = [
  {
    id: '1',
    username: 'test',
    email: '<EMAIL>',
    role: 'user',
  },
  {
    id: '2',
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
  },
];

// Mock units data
const mockUnits = [
  {
    id: '1',
    title: 'Introduction to Anatomy',
    description: 'Basic concepts of human anatomy',
    materialId: '1',
    order: 1,
  },
  {
    id: '2',
    title: 'Skeletal System',
    description: 'Structure and function of the skeletal system',
    materialId: '1',
    order: 2,
  },
];

// Mock progress data
const mockProgress = [
  {
    id: '1',
    userId: '1',
    unitId: '1',
    completed: true,
    completedAt: '2025-04-05T15:30:00Z',
  },
  {
    id: '2',
    userId: '1',
    unitId: '2',
    completed: false,
    completedAt: null,
  },
];

// Mock API service
const mockApiService = {
  // Materials endpoints
  getMaterials: async () => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockMaterials];
  },

  getMaterialById: async (id: string) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const material = mockMaterials.find(m => m.id === id);
    if (!material) {
      throw new Error('Material not found');
    }
    return { ...material };
  },

  createMaterial: async (data: any) => {
    await new Promise(resolve => setTimeout(resolve, 700));
    const newMaterial = {
      id: `${mockMaterials.length + 1}`,
      ...data,
      uploadDate: new Date().toISOString(),
      size: '1.0 MB',
      url: '/mock-files/new-material.pdf',
    };
    mockMaterials.push(newMaterial);
    return { ...newMaterial };
  },

  deleteMaterial: async (id: string) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const index = mockMaterials.findIndex(m => m.id === id);
    if (index === -1) {
      throw new Error('Material not found');
    }
    const deleted = mockMaterials.splice(index, 1)[0];
    return { success: true, deleted };
  },

  // Users endpoints
  getUsers: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockUsers.map(user => ({ ...user, password: undefined }));
  },

  createUser: async (data: any) => {
    await new Promise(resolve => setTimeout(resolve, 700));
    const newUser = {
      id: `${mockUsers.length + 1}`,
      ...data,
      role: data.role || 'user',
    };
    mockUsers.push(newUser);
    return { ...newUser, password: undefined };
  },

  // Auth endpoints
  login: async (credentials: { email: string; password: string; rememberMe?: boolean }) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const user = mockUsers.find(u => u.email === credentials.email);
    if (!user) {
      throw new Error('User not found');
    }

    // In a real app, you would check the password here
    // Set token expiration based on rememberMe flag
    const tokenExpiration = credentials.rememberMe ? '30d' : '1d';
    console.log(
      `Setting token expiration to ${tokenExpiration} based on rememberMe: ${credentials.rememberMe}`
    );

    return {
      access_token: 'mock-jwt-token',
      refresh_token: 'mock-refresh-token',
      token_expiration: tokenExpiration,
      user: { ...user, password: undefined },
    };
  },

  forgotPassword: async (email: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const user = mockUsers.find(u => u.email === email);
    if (!user) {
      throw new Error('User not found');
    }

    return {
      message: 'Password reset email sent',
      // In development mode only
      resetToken: 'mock-reset-token',
    };
  },

  resetPassword: async (token: string, newPassword: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    // In a real app, you would validate the token and update the password
    if (token !== 'mock-reset-token') {
      throw new Error('Invalid or expired reset token');
    }

    return {
      message: 'Password reset successful',
    };
  },

  getProfile: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return { ...mockUsers[0], password: undefined };
  },

  // Units endpoints
  getUnits: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockUnits];
  },

  getUnitById: async (id: string) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const unit = mockUnits.find(u => u.id === id);
    if (!unit) {
      throw new Error('Unit not found');
    }
    return { ...unit };
  },

  createUnit: async (data: any) => {
    await new Promise(resolve => setTimeout(resolve, 700));
    const newUnit = {
      id: `${mockUnits.length + 1}`,
      ...data,
      order: mockUnits.filter(u => u.materialId === data.materialId).length + 1,
    };
    mockUnits.push(newUnit);
    return { ...newUnit };
  },

  // Progress endpoints
  getUserProgress: async (userId: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockProgress.filter(p => p.userId === userId);
  },

  updateProgress: async (unitId: string, data: { completed: boolean }) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const progress = mockProgress.find(p => p.unitId === unitId);
    if (!progress) {
      throw new Error('Progress not found');
    }
    progress.completed = data.completed;
    progress.completedAt = data.completed ? new Date().toISOString() : null;
    return { ...progress };
  },
};

export default mockApiService;
