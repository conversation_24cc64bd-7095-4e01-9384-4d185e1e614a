{"root": true, "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}]}, "parserOptions": {"project": "./tsconfig.json", "ecmaVersion": 2022, "sourceType": "module"}}