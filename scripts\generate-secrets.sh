#!/bin/bash
# Generate secure secrets for MedTrack Hub

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════╗"
    echo "║       MedTrack Hub Secrets           ║"
    echo "║      Secure Secret Generator         ║"
    echo "╚══════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to generate secure random string
generate_secret() {
    local length=${1:-32}
    if command -v openssl >/dev/null 2>&1; then
        openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
    elif command -v python3 >/dev/null 2>&1; then
        python3 -c "import secrets; print(secrets.token_urlsafe($length)[:$length])"
    else
        # Fallback using /dev/urandom
        head -c $length /dev/urandom | base64 | tr -d "=+/" | cut -c1-$length
    fi
}

# Function to generate strong password
generate_password() {
    local length=${1:-16}
    if command -v openssl >/dev/null 2>&1; then
        openssl rand -base64 $length | tr -d "=+/" | head -c $length
    else
        head -c $length /dev/urandom | base64 | tr -d "=+/" | head -c $length
    fi
}

# Function to create .env file
create_env_file() {
    local env_file="$1"
    local environment="$2"
    
    print_status "Creating $env_file for $environment environment..."
    
    # Generate secrets
    JWT_SECRET=$(generate_secret 64)
    JWT_REFRESH_SECRET=$(generate_secret 64)
    DB_PASSWORD=$(generate_password 20)
    REDIS_PASSWORD=$(generate_password 16)
    SESSION_SECRET=$(generate_secret 32)
    ENCRYPTION_KEY=$(generate_secret 32)
    
    cat > "$env_file" << EOF
# MedTrack Hub Environment Configuration
# Generated on $(date)
# Environment: $environment

# ===========================================
# APPLICATION CONFIGURATION
# ===========================================
NODE_ENV=$environment
PORT=3002

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=$DB_PASSWORD
POSTGRES_DB=medtrack
DATABASE_URL=postgresql://postgres:$DB_PASSWORD@localhost:5432/medtrack

# ===========================================
# REDIS CONFIGURATION
# ===========================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=$REDIS_PASSWORD
REDIS_URL=redis://:$REDIS_PASSWORD@localhost:6379

# ===========================================
# JWT SECRETS
# ===========================================
JWT_SECRET=$JWT_SECRET
JWT_REFRESH_SECRET=$JWT_REFRESH_SECRET
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# ===========================================
# SECURITY
# ===========================================
SESSION_SECRET=$SESSION_SECRET
ENCRYPTION_KEY=$ENCRYPTION_KEY
BCRYPT_ROUNDS=12

# ===========================================
# TYPEORM CONFIGURATION
# ===========================================
TYPEORM_SYNC=true
TYPEORM_LOGGING=true

# ===========================================
# CORS CONFIGURATION
# ===========================================
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# ===========================================
# RATE LIMITING
# ===========================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# ===========================================
# EMAIL CONFIGURATION (Update with your values)
# ===========================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
EMAIL_FROM=<EMAIL>

# ===========================================
# AWS CONFIGURATION (Update with your values)
# ===========================================
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_S3_BUCKET=your_s3_bucket_name_here

# ===========================================
# ANALYTICS SERVICE
# ===========================================
ANALYTICS_SERVICE_URL=http://localhost:5000

# ===========================================
# FEATURE FLAGS
# ===========================================
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_FILE_UPLOAD=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_TWO_FACTOR_AUTH=false

# ===========================================
# FRONTEND URLS
# ===========================================
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_ANALYTICS_API_URL=http://localhost:5000
EOF

    print_success "Created $env_file"
}

# Function to create frontend .env.local
create_frontend_env() {
    local env_file="frontend/.env.local"
    
    print_status "Creating $env_file..."
    
    cat > "$env_file" << EOF
# Frontend Environment Variables
# Generated on $(date)

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_ANALYTICS_API_URL=http://localhost:5000

# App Configuration
NEXT_PUBLIC_APP_NAME=MedTrack Hub
NEXT_PUBLIC_APP_VERSION=1.0.0

# Feature Flags (these are public)
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
EOF

    print_success "Created $env_file"
}

# Function to create backend python analytics .env
create_analytics_env() {
    local env_file="backend/python_analytics/.env"
    local db_password="$1"
    local jwt_secret="$2"
    
    print_status "Creating $env_file..."
    
    cat > "$env_file" << EOF
# Python Analytics Environment Variables
# Generated on $(date)

# Database Configuration
DATABASE_URL=postgresql://postgres:$db_password@localhost:5432/medtrack

# JWT Configuration
JWT_SECRET=$jwt_secret

# Application Configuration
PORT=5000
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# Environment
ENVIRONMENT=development
EOF

    print_success "Created $env_file"
}

# Function to display secrets summary
display_secrets_summary() {
    echo
    print_success "🔐 Secrets Generated Successfully!"
    echo
    print_warning "📋 IMPORTANT: Save these credentials securely!"
    echo
    echo -e "${YELLOW}Database Password:${NC} $DB_PASSWORD"
    echo -e "${YELLOW}Redis Password:${NC} $REDIS_PASSWORD"
    echo -e "${YELLOW}JWT Secret:${NC} ${JWT_SECRET:0:20}..."
    echo -e "${YELLOW}JWT Refresh Secret:${NC} ${JWT_REFRESH_SECRET:0:20}..."
    echo
    print_warning "⚠️  Update the following in your .env files:"
    echo "   - EMAIL_USER and EMAIL_PASSWORD (for email functionality)"
    echo "   - AWS credentials (if using AWS services)"
    echo "   - Any other service-specific credentials"
    echo
    print_status "📁 Files created:"
    echo "   - .env (root directory)"
    echo "   - frontend/.env.local"
    echo "   - backend/python_analytics/.env"
    echo
    print_warning "🚨 NEVER commit these .env files to Git!"
}

# Main function
main() {
    print_header
    
    # Check if .env already exists
    if [ -f ".env" ]; then
        print_warning ".env file already exists!"
        read -p "Do you want to overwrite it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Skipping .env creation"
            exit 0
        fi
    fi
    
    # Create environment files
    create_env_file ".env" "development"
    
    # Extract passwords for other files
    DB_PASSWORD=$(grep "POSTGRES_PASSWORD=" .env | cut -d'=' -f2)
    JWT_SECRET=$(grep "JWT_SECRET=" .env | cut -d'=' -f2)
    
    create_frontend_env
    create_analytics_env "$DB_PASSWORD" "$JWT_SECRET"
    
    # Display summary
    display_secrets_summary
    
    print_success "🎉 Setup complete! You can now start the development environment."
    echo
    print_status "Next steps:"
    echo "1. Review and update .env files with your specific values"
    echo "2. Start the development environment: docker-compose -f docker-compose.dev.yml up -d"
    echo "3. Run health checks: ./scripts/health-check.sh"
}

# Run main function
main "$@"
