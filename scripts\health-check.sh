#!/bin/bash
# Health check script for all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default ports
FRONTEND_PORT=${FRONTEND_PORT:-3000}
BACKEND_PORT=${BACKEND_PORT:-3002}
ANALYTICS_PORT=${ANALYTICS_PORT:-5000}
POSTGRES_PORT=${POSTGRES_PORT:-5432}
REDIS_PORT=${REDIS_PORT:-6379}

# Function to check service health
check_service() {
    local service_name=$1
    local url=$2
    local timeout=${3:-10}
    
    echo -n "Checking $service_name... "
    
    if curl -f -s --max-time $timeout "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Healthy${NC}"
        return 0
    else
        echo -e "${RED}✗ Unhealthy${NC}"
        return 1
    fi
}

# Function to check database connection
check_database() {
    echo -n "Checking PostgreSQL... "
    
    if pg_isready -h localhost -p $POSTGRES_PORT -U ${POSTGRES_USER:-postgres} > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Healthy${NC}"
        return 0
    else
        echo -e "${RED}✗ Unhealthy${NC}"
        return 1
    fi
}

# Function to check Redis connection
check_redis() {
    echo -n "Checking Redis... "
    
    if redis-cli -h localhost -p $REDIS_PORT ping > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Healthy${NC}"
        return 0
    else
        echo -e "${RED}✗ Unhealthy${NC}"
        return 1
    fi
}

# Main health check
echo -e "${YELLOW}=== MedTrack Hub Health Check ===${NC}"
echo

# Check all services
failed=0

check_service "Frontend" "http://localhost:$FRONTEND_PORT/api/health" || failed=$((failed + 1))
check_service "Backend API" "http://localhost:$BACKEND_PORT/api/health" || failed=$((failed + 1))
check_service "Analytics API" "http://localhost:$ANALYTICS_PORT/health" || failed=$((failed + 1))
check_database || failed=$((failed + 1))
check_redis || failed=$((failed + 1))

echo

if [ $failed -eq 0 ]; then
    echo -e "${GREEN}All services are healthy! ✓${NC}"
    exit 0
else
    echo -e "${RED}$failed service(s) are unhealthy! ✗${NC}"
    exit 1
fi
