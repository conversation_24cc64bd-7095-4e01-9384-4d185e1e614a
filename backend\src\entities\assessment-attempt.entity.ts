import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Assessment } from './assessment.entity';
import { User } from './user.entity';
import { AssessmentAnswer } from './assessment-answer.entity';

export enum AttemptStatus {
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  ABANDONED = 'abandoned',
  TIME_EXPIRED = 'time_expired',
  SUBMITTED = 'submitted',
}

@Entity('assessment_attempts')
export class AssessmentAttempt {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'int' })
  attempt_number: number;

  @Column({
    type: 'enum',
    enum: AttemptStatus,
    default: AttemptStatus.IN_PROGRESS,
  })
  status: AttemptStatus;

  @Column({ type: 'timestamp' })
  started_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  completed_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  submitted_at: Date;

  @Column({ type: 'int', default: 0 })
  time_spent_seconds: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  score: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  percentage: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  grade: string; // A, B, C, D, F

  @Column({ type: 'boolean', default: false })
  passed: boolean;

  @Column({ type: 'int', default: 0 })
  correct_answers: number;

  @Column({ type: 'int', default: 0 })
  incorrect_answers: number;

  @Column({ type: 'int', default: 0 })
  unanswered_questions: number;

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    time_per_question?: number[];
    difficulty_breakdown?: {
      easy: { correct: number; total: number };
      medium: { correct: number; total: number };
      hard: { correct: number; total: number };
    };
    category_breakdown?: {
      [category: string]: { correct: number; total: number };
    };
    confidence_levels?: number[];
    review_flags?: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  adaptive_data: {
    difficulty_progression?: string[];
    ability_estimate?: number;
    standard_error?: number;
    theta_estimates?: number[];
    item_difficulties?: number[];
  };

  @Column({ type: 'text', nullable: true })
  feedback: string;

  @Column({ type: 'jsonb', nullable: true })
  recommendations: {
    study_areas?: string[];
    difficulty_level?: string;
    next_topics?: string[];
    remediation_resources?: string[];
  };

  @Column({ type: 'uuid' })
  assessment_id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @ManyToOne(() => Assessment, (assessment) => assessment.attempts, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'assessment_id' })
  assessment: Assessment;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => AssessmentAnswer, (answer) => answer.attempt, {
    cascade: true,
  })
  answers: AssessmentAnswer[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
