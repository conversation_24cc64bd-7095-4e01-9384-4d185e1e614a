__version__ = "1.0.0"

"""
MedTrack Hub Analytics Service

Advanced analytics and machine learning service for medical education platform.
Provides performance prediction, learning pattern analysis, and personalized recommendations.
"""

from .analytics.performance_prediction import predict_performance
from .analytics.learning_patterns import analyze_learning_patterns
from .analytics.recommendations import generate_recommendations
from .analytics.data_processor import process_user_data

__all__ = [
    "predict_performance",
    "analyze_learning_patterns", 
    "generate_recommendations",
    "process_user_data"
]
