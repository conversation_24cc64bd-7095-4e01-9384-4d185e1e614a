const { Client } = require('pg');
require('dotenv').config({ path: './backend/.env' });

async function testDatabaseConnection() {
    const client = new Client({
        host: process.env.POSTGRES_HOST || 'localhost',
        port: parseInt(process.env.POSTGRES_PORT) || 5432,
        user: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_DB,
    });

    try {
        console.log('Attempting to connect to PostgreSQL...');
        console.log(`Host: ${client.host}:${client.port}`);
        console.log(`Database: ${client.database}`);
        console.log(`User: ${client.user}`);

        await client.connect();
        console.log('✅ Successfully connected to PostgreSQL!');

        // Test a simple query
        const result = await client.query('SELECT version()');
        console.log('📊 PostgreSQL Version:', result.rows[0].version);

        // Check if database exists and list tables
        const tablesResult = await client.query(`
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
        `);

        console.log('📋 Tables in database:');
        if (tablesResult.rows.length === 0) {
            console.log('   No tables found (database might be empty - this is normal for new setup)');
        } else {
            tablesResult.rows.forEach(row => {
                console.log(`   - ${row.table_name}`);
            });
        }

    } catch (error) {
        console.error('❌ Database connection failed:');
        console.error('Error:', error.message);

        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Suggestions:');
            console.log('   - Make sure PostgreSQL service is running');
            console.log('   - Check if the port 5432 is correct');
            console.log('   - Verify the host is accessible');
        } else if (error.code === '28P01') {
            console.log('\n💡 Suggestions:');
            console.log('   - Check username and password');
            console.log('   - Verify user has access to the database');
        } else if (error.code === '3D000') {
            console.log('\n💡 Suggestions:');
            console.log('   - Database does not exist, you may need to create it');
            console.log('   - Run: createdb -U medical medical_tracker');
        }
    } finally {
        await client.end();
    }
}

testDatabaseConnection();
