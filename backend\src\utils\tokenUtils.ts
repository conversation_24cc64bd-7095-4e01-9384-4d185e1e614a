import * as jwt from 'jsonwebtoken';
import { SignOptions } from 'jsonwebtoken';

export const generateAccessToken = (userId: string, email: string) => {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET must be defined in environment variables');
  }
  const options: SignOptions = {
    expiresIn: process.env.JWT_EXPIRATION
      ? parseInt(process.env.JWT_EXPIRATION)
      : '1h',
  };
  return jwt.sign({ userId, email }, jwtSecret, options);
};

export const generateRefreshToken = (userId: string) => {
  const refreshSecret =
    process.env.REFRESH_TOKEN_SECRET ||
    'medical-app-secure-refresh-token-secret-key-2025';
  const options: SignOptions = {
    expiresIn: '7d',
  };
  return jwt.sign({ userId }, refreshSecret, options);
};
