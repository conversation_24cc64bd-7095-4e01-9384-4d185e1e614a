import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import Anthropic from '@anthropic-ai/sdk';
import { ChatMessage, MessageRole } from './entities/chat-message.entity';
import { ChatSession } from './entities/chat-session.entity';
import { User } from '../../entities/user.entity';

export interface ChatRequest {
  message: string;
  sessionId?: string;
  topic?: string;
}

export interface ChatResponse {
  message: string;
  sessionId: string;
  messageId: string;
  tokensUsed?: number;
}

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);
  private readonly anthropic: Anthropic;

  constructor(
    @InjectRepository(ChatMessage)
    private readonly messageRepository: Repository<ChatMessage>,
    @InjectRepository(ChatSession)
    private readonly sessionRepository: Repository<ChatSession>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
  ) {
    const apiKey = this.configService.get<string>('ai.claude.apiKey');
    if (!apiKey) {
      this.logger.warn('Claude API key not configured. Chat functionality will be disabled.');
      return;
    }

    this.anthropic = new Anthropic({
      apiKey,
    });
  }

  async chat(userId: string, request: ChatRequest): Promise<ChatResponse> {
    if (!this.anthropic) {
      throw new BadRequestException('AI chat is not configured');
    }

    try {
      // Get or create session
      let session = await this.getOrCreateSession(userId, request.sessionId, request.topic);

      // Save user message
      const userMessage = await this.saveMessage(
        session.id,
        userId,
        request.message,
        MessageRole.USER
      );

      // Get conversation history
      const messages = await this.getConversationHistory(session.id);

      // Generate AI response
      const aiResponse = await this.generateResponse(messages, request.topic);

      // Save AI message
      const aiMessage = await this.saveMessage(
        session.id,
        userId,
        aiResponse.content,
        MessageRole.ASSISTANT,
        { tokensUsed: aiResponse.tokensUsed }
      );

      return {
        message: aiResponse.content,
        sessionId: session.id,
        messageId: aiMessage.id,
        tokensUsed: aiResponse.tokensUsed,
      };
    } catch (error) {
      this.logger.error('Error in chat service:', error);
      throw new BadRequestException('Failed to generate response');
    }
  }

  private async getOrCreateSession(
    userId: string,
    sessionId?: string,
    topic?: string
  ): Promise<ChatSession> {
    if (sessionId) {
      const session = await this.sessionRepository.findOne({
        where: { id: sessionId, user_id: userId },
      });
      if (session) return session;
    }

    // Create new session
    const session = this.sessionRepository.create({
      title: this.generateSessionTitle(topic),
      topic: topic || 'general',
      user_id: userId,
      context: this.getTopicContext(topic),
    });

    return await this.sessionRepository.save(session);
  }

  private async saveMessage(
    sessionId: string,
    userId: string,
    content: string,
    role: MessageRole,
    metadata?: Record<string, any>
  ): Promise<ChatMessage> {
    const message = this.messageRepository.create({
      content,
      role,
      session_id: sessionId,
      user_id: userId,
      metadata,
    });

    return await this.messageRepository.save(message);
  }

  private async getConversationHistory(sessionId: string): Promise<ChatMessage[]> {
    return await this.messageRepository.find({
      where: { session_id: sessionId },
      order: { created_at: 'ASC' },
      take: 20, // Limit to last 20 messages
    });
  }

  private async generateResponse(
    messages: ChatMessage[],
    topic?: string
  ): Promise<{ content: string; tokensUsed: number }> {
    const systemPrompt = this.getSystemPrompt(topic);

    const anthropicMessages = messages.map(msg => ({
      role: msg.role === MessageRole.ASSISTANT ? 'assistant' as const : 'user' as const,
      content: msg.content,
    }));

    const response = await this.anthropic.messages.create({
      model: this.configService.get<string>('ai.claude.model') || 'claude-3-5-sonnet-20241022',
      max_tokens: this.configService.get<number>('ai.claude.maxTokens') || 4096,
      temperature: this.configService.get<number>('ai.claude.temperature') || 0.7,
      system: systemPrompt,
      messages: anthropicMessages,
    });

    return {
      content: response.content[0].type === 'text' ? response.content[0].text : '',
      tokensUsed: response.usage.input_tokens + response.usage.output_tokens,
    };
  }

  private getSystemPrompt(topic?: string): string {
    const basePrompt = `You are MedBot, an AI tutor for medical students using MedTrack Hub. You are knowledgeable, encouraging, and focused on helping students learn medicine effectively.

Key guidelines:
- Provide clear, accurate medical information
- Break down complex concepts into understandable parts
- Use medical terminology appropriately but explain when needed
- Encourage active learning and critical thinking
- Suggest study strategies and mnemonics when helpful
- Always emphasize the importance of clinical correlation
- If unsure about something, acknowledge limitations

Remember: You're a study assistant, not a replacement for clinical judgment or medical advice.`;

    const topicPrompts = {
      anatomy: `Focus on anatomical structures, relationships, and clinical correlations. Use spatial descriptions and suggest visualization techniques.`,
      physiology: `Emphasize mechanisms, processes, and how body systems work together. Use analogies to explain complex processes.`,
      pharmacology: `Focus on drug mechanisms, interactions, side effects, and clinical applications. Emphasize safety and contraindications.`,
      pathology: `Explain disease processes, causes, and manifestations. Connect pathophysiology to clinical presentations.`,
      clinical: `Focus on clinical reasoning, differential diagnosis, and evidence-based medicine. Encourage systematic thinking.`,
    };

    if (topic && topicPrompts[topic as keyof typeof topicPrompts]) {
      return `${basePrompt}\n\nSpecial focus for ${topic}: ${topicPrompts[topic as keyof typeof topicPrompts]}`;
    }

    return basePrompt;
  }

  private generateSessionTitle(topic?: string): string {
    const topicTitles = {
      anatomy: 'Anatomy Study Session',
      physiology: 'Physiology Discussion',
      pharmacology: 'Pharmacology Review',
      pathology: 'Pathology Study',
      clinical: 'Clinical Reasoning',
    };

    return topicTitles[topic as keyof typeof topicTitles] || `Study Session - ${new Date().toLocaleDateString()}`;
  }

  private getTopicContext(topic?: string): string {
    const contexts = {
      anatomy: 'Studying anatomical structures and relationships',
      physiology: 'Learning about body functions and mechanisms',
      pharmacology: 'Understanding drugs and their effects',
      pathology: 'Exploring disease processes',
      clinical: 'Developing clinical reasoning skills',
    };

    return contexts[topic as keyof typeof contexts] || 'General medical education discussion';
  }

  async getUserSessions(userId: string): Promise<ChatSession[]> {
    return await this.sessionRepository.find({
      where: { user_id: userId, is_active: true },
      order: { updated_at: 'DESC' },
      take: 10,
    });
  }

  async getSessionMessages(sessionId: string, userId: string): Promise<ChatMessage[]> {
    // Verify session belongs to user
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId, user_id: userId },
    });

    if (!session) {
      throw new BadRequestException('Session not found');
    }

    return await this.messageRepository.find({
      where: { session_id: sessionId },
      order: { created_at: 'ASC' },
    });
  }

  async deleteSession(sessionId: string, userId: string): Promise<void> {
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId, user_id: userId },
    });

    if (!session) {
      throw new BadRequestException('Session not found');
    }

    await this.sessionRepository.update(sessionId, { is_active: false });
  }
}
