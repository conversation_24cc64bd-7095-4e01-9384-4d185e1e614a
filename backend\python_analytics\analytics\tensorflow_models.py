"""
TensorFlow-based machine learning models for medical education analytics
"""
import numpy as np
import pandas as pd
import tensorflow as tf
from typing import Dict, Any, List, Tuple
import logging
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import os
import pickle

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformancePredictionModel:
    """
    TensorFlow model for predicting student performance
    """
    
    def __init__(self, model_path: str = "models/performance_model"):
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        
    def create_model(self, input_shape: int) -> tf.keras.Model:
        """Create a neural network model for performance prediction"""
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(128, activation='relu', input_shape=(input_shape,)),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        return model
    
    def prepare_features(self, user_data: Dict[str, Any]) -> np.ndarray:
        """Prepare features for the model"""
        try:
            features = []
            
            # Study time features
            study_time = user_data.get("study_time", [])
            if study_time:
                avg_study_time = np.mean([s.get("total_duration", 0) for s in study_time])
                study_consistency = len(study_time) / 30  # Consistency over 30 days
                features.extend([avg_study_time, study_consistency])
            else:
                features.extend([0, 0])
            
            # Quiz performance features
            quiz_perf = user_data.get("quiz_performance", {})
            accuracy = quiz_perf.get("accuracy", 0.5)
            total_questions = quiz_perf.get("total_questions", 0)
            features.extend([accuracy, total_questions])
            
            # Progress features
            progress = user_data.get("progress", {})
            completion_rate = progress.get("completion_rate", 0)
            features.append(completion_rate)
            
            # Time-based features
            import datetime
            now = datetime.datetime.now()
            day_of_week = now.weekday() / 6.0  # Normalize to 0-1
            hour_of_day = now.hour / 23.0  # Normalize to 0-1
            features.extend([day_of_week, hour_of_day])
            
            # Engagement features
            engagement = user_data.get("engagement", {})
            login_frequency = engagement.get("login_frequency", 0)
            session_duration = engagement.get("avg_session_duration", 0)
            features.extend([login_frequency, session_duration])
            
            # Pad or truncate to fixed size (10 features)
            while len(features) < 10:
                features.append(0)
            features = features[:10]
            
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error preparing features: {str(e)}")
            return np.zeros(10, dtype=np.float32)
    
    def train(self, training_data: List[Dict[str, Any]], labels: List[float]) -> Dict[str, Any]:
        """Train the model with provided data"""
        try:
            # Prepare features
            X = np.array([self.prepare_features(data) for data in training_data])
            y = np.array(labels)
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42
            )
            
            # Create model
            self.model = self.create_model(X_train.shape[1])
            
            # Train model
            history = self.model.fit(
                X_train, y_train,
                epochs=50,
                batch_size=32,
                validation_data=(X_test, y_test),
                verbose=0,
                callbacks=[
                    tf.keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True)
                ]
            )
            
            # Evaluate model
            test_loss, test_accuracy, test_precision, test_recall = self.model.evaluate(
                X_test, y_test, verbose=0
            )
            
            self.is_trained = True
            
            # Save model and scaler
            self.save_model()
            
            return {
                "training_accuracy": float(history.history['accuracy'][-1]),
                "validation_accuracy": float(history.history['val_accuracy'][-1]),
                "test_accuracy": float(test_accuracy),
                "test_precision": float(test_precision),
                "test_recall": float(test_recall),
                "epochs_trained": len(history.history['accuracy'])
            }
            
        except Exception as e:
            logger.error(f"Error training model: {str(e)}")
            raise
    
    def predict(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make predictions for a user"""
        try:
            if not self.is_trained and not self.load_model():
                # If no trained model, use fallback prediction
                return self._fallback_prediction(user_data)
            
            # Prepare features
            features = self.prepare_features(user_data)
            features_scaled = self.scaler.transform(features.reshape(1, -1))
            
            # Make prediction
            prediction = self.model.predict(features_scaled, verbose=0)[0][0]
            
            # Calculate confidence based on model certainty
            confidence = abs(prediction - 0.5) * 2  # Distance from uncertain (0.5)
            
            return {
                "predicted_success_rate": float(prediction),
                "confidence": float(confidence),
                "model_used": "tensorflow",
                "features_used": features.tolist()
            }
            
        except Exception as e:
            logger.error(f"Error making prediction: {str(e)}")
            return self._fallback_prediction(user_data)
    
    def _fallback_prediction(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback prediction when TensorFlow model is not available"""
        quiz_perf = user_data.get("quiz_performance", {})
        accuracy = quiz_perf.get("accuracy", 0.5)
        
        # Simple heuristic based on quiz performance
        prediction = min(max(accuracy * 1.1, 0.0), 1.0)
        
        return {
            "predicted_success_rate": prediction,
            "confidence": 0.6,
            "model_used": "fallback",
            "features_used": []
        }
    
    def save_model(self):
        """Save the trained model and scaler"""
        try:
            os.makedirs(self.model_path, exist_ok=True)
            
            # Save TensorFlow model
            self.model.save(os.path.join(self.model_path, "model.h5"))
            
            # Save scaler
            with open(os.path.join(self.model_path, "scaler.pkl"), "wb") as f:
                pickle.dump(self.scaler, f)
                
            logger.info(f"Model saved to {self.model_path}")
            
        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")
    
    def load_model(self) -> bool:
        """Load a previously trained model"""
        try:
            model_file = os.path.join(self.model_path, "model.h5")
            scaler_file = os.path.join(self.model_path, "scaler.pkl")
            
            if os.path.exists(model_file) and os.path.exists(scaler_file):
                self.model = tf.keras.models.load_model(model_file)
                
                with open(scaler_file, "rb") as f:
                    self.scaler = pickle.load(f)
                
                self.is_trained = True
                logger.info(f"Model loaded from {self.model_path}")
                return True
            else:
                logger.warning(f"No saved model found at {self.model_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return False

class LearningPatternAnalyzer:
    """
    TensorFlow-based learning pattern analysis
    """
    
    def __init__(self):
        self.autoencoder = None
        
    def create_autoencoder(self, input_dim: int) -> tf.keras.Model:
        """Create an autoencoder for pattern detection"""
        encoder = tf.keras.Sequential([
            tf.keras.layers.Dense(64, activation='relu', input_shape=(input_dim,)),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(16, activation='relu')
        ])
        
        decoder = tf.keras.Sequential([
            tf.keras.layers.Dense(32, activation='relu', input_shape=(16,)),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dense(input_dim, activation='sigmoid')
        ])
        
        autoencoder = tf.keras.Sequential([encoder, decoder])
        autoencoder.compile(optimizer='adam', loss='mse')
        
        return autoencoder
    
    def analyze_patterns(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze learning patterns using TensorFlow"""
        try:
            # For now, return statistical analysis
            # TODO: Implement full TensorFlow-based pattern analysis
            study_times = user_data.get("study_time", [])
            
            if not study_times:
                return {"patterns": "insufficient_data"}
            
            # Extract time patterns
            durations = [s.get("total_duration", 0) for s in study_times]
            avg_duration = np.mean(durations)
            std_duration = np.std(durations)
            
            return {
                "average_study_duration": float(avg_duration),
                "study_consistency": float(1.0 / (1.0 + std_duration)),
                "pattern_type": "regular" if std_duration < avg_duration * 0.5 else "irregular",
                "model_used": "tensorflow_autoencoder"
            }
            
        except Exception as e:
            logger.error(f"Error analyzing patterns: {str(e)}")
            return {"patterns": "analysis_error", "error": str(e)}

# Global model instances
performance_model = PerformancePredictionModel()
pattern_analyzer = LearningPatternAnalyzer()
