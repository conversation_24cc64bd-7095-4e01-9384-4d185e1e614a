import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Request } from 'express';
import { UnitQuizService } from './unit-quiz.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('quiz/unit')
@UseGuards(JwtAuthGuard)
export class UnitQuizController {
  constructor(private readonly unitQuizService: UnitQuizService) {}

  @Get(':unitId')
  async getQuiz(@Param('unitId') unitId: string) {
    return this.unitQuizService.getQuizForUser(unitId);
  }

  @Get(':unitId/eligibility')
  async getEligibility(@Param('unitId') unitId: string, @Req() req: Request) {
    return {
      eligible: await this.unitQuizService.validateAttempt(
        (req.user as any).id,
        unitId,
      ),
    };
  }

  @Post(':unitId/submit')
  async submitQuiz(
    @Param('unitId') unitId: string,
    @Req() req: Request,
    @Body() body: { answers: Record<string, string> },
  ) {
    return this.unitQuizService.scoreQuiz(
      (req.user as any).id,
      unitId,
      body.answers,
    );
  }
}
