const { Client } = require('pg');
const Redis = require('ioredis');
const axios = require('axios');
require('dotenv').config({ path: './.env' });

// Configuration
const config = {
  postgres: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT) || 5432,
    user: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
  },
  redis: {
    host: 'localhost',
    port: 6379,
  },
  backend: {
    url: 'http://localhost:3002',
  },
  frontend: {
    url: 'http://localhost:3000',
  }
};

// Test data
const testUser = {
  email: '<EMAIL>',
  username: 'testuser',
  name: 'Test User',
  password: 'test123',
  role: 'student'
};

async function testPostgreSQL() {
  console.log('\n🔍 Testing PostgreSQL Connection...');
  const client = new Client(config.postgres);
  
  try {
    await client.connect();
    console.log('✅ PostgreSQL connection successful');
    
    const result = await client.query('SELECT version()');
    console.log('📊 PostgreSQL Version:', result.rows[0].version.split(' ')[0] + ' ' + result.rows[0].version.split(' ')[1]);
    
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log('📋 Database Tables:');
    tablesResult.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });
    
    return true;
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error.message);
    return false;
  } finally {
    await client.end();
  }
}

async function testRedis() {
  console.log('\n🔍 Testing Redis Connection...');
  const redis = new Redis(config.redis);
  
  try {
    const pong = await redis.ping();
    console.log('✅ Redis connection successful');
    console.log('📡 Ping response:', pong);
    
    // Test basic operations
    await redis.set('test:setup', 'Hello Redis!');
    const value = await redis.get('test:setup');
    console.log('📝 Set/Get test:', value);
    
    // Test with expiration
    await redis.setex('test:expiring', 60, 'This will expire in 60 seconds');
    const ttl = await redis.ttl('test:expiring');
    console.log('⏰ TTL test:', ttl, 'seconds remaining');
    
    // Get Redis info
    const info = await redis.info('server');
    const lines = info.split('\r\n');
    const version = lines.find(line => line.startsWith('redis_version:'));
    if (version) {
      console.log('🔧 Redis version:', version.split(':')[1]);
    }
    
    // Clean up test keys
    await redis.del('test:setup', 'test:expiring');
    console.log('🧹 Cleaned up test keys');
    
    return true;
  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    return false;
  } finally {
    redis.disconnect();
  }
}

async function testBackendHealth() {
  console.log('\n🔍 Testing Backend Health...');
  
  try {
    const response = await axios.get(`${config.backend.url}/health`, {
      timeout: 5000
    });
    console.log('✅ Backend health check successful');
    console.log('📊 Status:', response.data.status);
    console.log('📊 Database:', response.data.database ? '✅ Connected' : '❌ Disconnected');
    console.log('📊 Redis:', response.data.redis ? '✅ Connected' : '❌ Disconnected');
    return true;
  } catch (error) {
    console.error('❌ Backend health check failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Backend server is not running. Start it with: cd backend && npm run start:dev');
    }
    return false;
  }
}

async function testAuthEndpoints() {
  console.log('\n🔍 Testing Authentication Endpoints...');
  
  try {
    // Test registration
    console.log('📝 Testing registration...');
    const registerResponse = await axios.post(`${config.backend.url}/v1/auth/register`, testUser, {
      timeout: 10000,
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Registration successful');
    console.log('📊 User ID:', registerResponse.data.user?.id);
    console.log('📊 Access Token:', registerResponse.data.accessToken ? '✅ Present' : '❌ Missing');
    
    // Test login
    console.log('🔐 Testing login...');
    const loginResponse = await axios.post(`${config.backend.url}/v1/auth/login`, {
      email: testUser.email,
      password: testUser.password
    }, {
      timeout: 10000,
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Login successful');
    console.log('📊 User ID:', loginResponse.data.user?.id);
    console.log('📊 Access Token:', loginResponse.data.accessToken ? '✅ Present' : '❌ Missing');
    
    // Test protected endpoint
    const token = loginResponse.data.accessToken;
    if (token) {
      console.log('🔒 Testing protected endpoint...');
      const protectedResponse = await axios.get(`${config.backend.url}/v1/auth/protected`, {
        headers: { 'Authorization': `Bearer ${token}` },
        timeout: 5000
      });
      console.log('✅ Protected endpoint access successful');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Auth endpoint test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testFrontendConnection() {
  console.log('\n🔍 Testing Frontend Connection...');
  
  try {
    const response = await axios.get(config.frontend.url, {
      timeout: 5000
    });
    console.log('✅ Frontend connection successful');
    console.log('📊 Status:', response.status);
    return true;
  } catch (error) {
    console.error('❌ Frontend connection failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Frontend server is not running. Start it with: cd frontend && npm run dev');
    }
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Complete Setup Test...');
  console.log('=' * 50);
  
  const results = {
    postgres: await testPostgreSQL(),
    redis: await testRedis(),
    backend: await testBackendHealth(),
    auth: false,
    frontend: await testFrontendConnection()
  };
  
  // Only test auth if backend is running
  if (results.backend) {
    results.auth = await testAuthEndpoints();
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log('=' * 30);
  console.log(`PostgreSQL: ${results.postgres ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Redis: ${results.redis ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Backend: ${results.backend ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Auth Endpoints: ${results.auth ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Frontend: ${results.frontend ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (!allPassed) {
    console.log('\n💡 Next Steps:');
    if (!results.postgres) console.log('   - Check PostgreSQL service and credentials');
    if (!results.redis) console.log('   - Start Redis: docker run -d --name redis-medical -p 6379:6379 redis:7-alpine');
    if (!results.backend) console.log('   - Start backend: cd backend && npm run start:dev');
    if (!results.frontend) console.log('   - Start frontend: cd frontend && npm run dev');
    if (!results.auth) console.log('   - Check auth endpoint configuration and database schema');
  }
  
  return allPassed;
}

// Run the tests
runAllTests().catch(console.error);
