import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { User } from './user.entity';
import { Quiz } from './quiz.entity';

@Entity('quiz_attempts')
export class QuizAttempt {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => Quiz)
  quiz: Quiz;

  @Column('float')
  score: number;

  @Column('jsonb')
  responses: {
    questionId: string;
    isCorrect: boolean;
    responseTime: number;
  }[];

  @Column('jsonb')
  metadata: {
    duration: number;
    startTime: Date;
    endTime: Date;
    deviceInfo: any;
  };

  @Column({ type: 'boolean', default: false })
  synced: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
