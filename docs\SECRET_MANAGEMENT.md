# 🔐 Comprehensive Secret Management Guide

This document provides enterprise-grade best practices for managing secrets and sensitive configuration in MedTrack Hub across all environments.

## 📋 Overview

Proper secret management is the foundation of application security. This guide covers secure handling of secrets from development to production, ensuring your MedTrack Hub deployment remains secure and compliant.

## 🌍 Environment Strategy

### 🛠️ **Development Environment**
- **Purpose**: Local development and testing
- **Security Level**: Medium (secure but developer-friendly)
- **Secret Storage**: Local `.env` files (never committed)
- **Access**: Individual developers only

### 🧪 **Staging Environment**
- **Purpose**: Pre-production testing and QA
- **Security Level**: High (mirrors production)
- **Secret Storage**: Environment variables or secret management service
- **Access**: QA team and senior developers

### 🚀 **Production Environment**
- **Purpose**: Live application serving real users
- **Security Level**: Maximum (enterprise-grade)
- **Secret Storage**: Secure secret management service
- **Access**: DevOps team and system administrators only

## 🔑 Secret Categories & Requirements

### 🗄️ **Database Credentials**
```bash
POSTGRES_PASSWORD=     # 20+ characters, alphanumeric + symbols
DATABASE_URL=          # Complete connection string with credentials
POSTGRES_HOST=         # Database server hostname
POSTGRES_USER=         # Database username
POSTGRES_DB=           # Database name
```

### 🔒 **Authentication & Security**
```bash
JWT_SECRET=            # 64+ characters, base64 encoded
JWT_REFRESH_SECRET=    # 64+ characters, base64 encoded, different from JWT_SECRET
SESSION_SECRET=        # 32+ characters for session encryption
ENCRYPTION_KEY=        # 32+ characters for data encryption
```

### ⚡ **Cache & Performance**
```bash
REDIS_PASSWORD=        # 16+ characters for Redis authentication
REDIS_URL=             # Complete Redis connection string
```

### 📧 **External Services**
```bash
EMAIL_USER=            # Email service username/email
EMAIL_PASSWORD=        # App-specific password (not regular password)
AWS_ACCESS_KEY_ID=     # AWS credentials (if using AWS services)
AWS_SECRET_ACCESS_KEY= # AWS secret key
```

### 🌐 **Application Configuration**
```bash
NODE_ENV=              # Environment: development/staging/production
FRONTEND_DOMAIN=       # Your application domain
NEXT_PUBLIC_API_URL=   # Public API endpoint
```

## 🛡️ Security Best Practices

### ✅ **MUST DO**
1. **Generate cryptographically secure secrets**
   ```bash
   # JWT secrets (64+ characters)
   openssl rand -base64 48

   # Database passwords (20+ characters)
   openssl rand -base64 15

   # General secrets (32+ characters)
   openssl rand -base64 24
   ```

2. **Use different secrets for each environment**
   - Development secrets ≠ Staging secrets ≠ Production secrets
   - Never reuse secrets across environments

3. **Implement secret rotation schedule**
   - Production: Every 90 days
   - Staging: Every 6 months
   - Development: Every year or when compromised

4. **Validate secret strength**
   ```bash
   # Check secret length and complexity
   node scripts/validate-env.js
   ```

### ❌ **NEVER DO**
1. **Commit secrets to version control**
   - No secrets in `.env` files committed to Git
   - No secrets in source code comments
   - No secrets in configuration files

2. **Use weak or predictable secrets**
   - No dictionary words
   - No personal information
   - No sequential patterns

3. **Share secrets insecurely**
   - No secrets in email or chat
   - No secrets in documentation
   - No secrets in error messages or logs

## 🔧 Implementation Guide

### 🏠 **Local Development Setup**

1. **Generate secure environment files**:
   ```bash
   # Run the automated setup script
   chmod +x scripts/generate-secrets.sh
   ./scripts/generate-secrets.sh

   # This creates:
   # - .env (root directory)
   # - frontend/.env.local
   # - backend/python_analytics/.env
   ```

2. **Verify generated secrets**:
   ```bash
   # Check that all required variables are set
   node scripts/validate-env.js

   # Test database connection
   node test-db-connection.js
   ```

3. **Start development environment**:
   ```bash
   # Start all services
   docker-compose -f docker-compose.dev.yml up -d

   # Check service health
   ./scripts/health-check.sh
   ```

### 🐙 **GitHub Repository Secrets**

Configure these secrets in your GitHub repository for CI/CD:

**Go to**: Repository → Settings → Secrets and variables → Actions

**Required Secrets**:
```
JWT_SECRET                    # 64-character JWT signing secret
JWT_REFRESH_SECRET           # 64-character refresh token secret
POSTGRES_PASSWORD            # 20-character database password
REDIS_PASSWORD              # 16-character Redis password
NODE_ENV                    # production
POSTGRES_HOST               # db (for Docker)
POSTGRES_USER               # postgres
POSTGRES_DB                 # medtrack
NEXT_PUBLIC_API_URL         # https://your-domain.com/api
NEXT_PUBLIC_ANALYTICS_API_URL # https://your-domain.com/analytics
```

### 🚀 **Production Deployment**

1. **Use environment variables**:
   ```bash
   # Set in your production environment
   export JWT_SECRET="your-production-jwt-secret"
   export POSTGRES_PASSWORD="your-production-db-password"
   # ... other secrets
   ```

2. **Consider secret management services**:
   - **AWS Secrets Manager**
   - **Azure Key Vault**
   - **Google Secret Manager**
   - **HashiCorp Vault**

3. **Implement monitoring**:
   ```bash
   # Monitor secret access and rotation
   # Set up alerts for secret expiration
   # Log secret usage (without exposing values)
   ```

## 🔍 Security Validation

### 📊 **Secret Strength Requirements**
- **Minimum Length**: 16 characters
- **Recommended Length**: 32+ characters
- **JWT Secrets**: 64+ characters
- **Character Set**: Alphanumeric + symbols
- **Entropy**: High randomness (use cryptographic generators)

### ✅ **Security Checklist**
```bash
# Run this checklist before deployment
□ All secrets use environment variables
□ No hardcoded secrets in source code
□ .env files are in .gitignore
□ Production secrets are unique and strong
□ Secrets are rotated on schedule
□ Access to secrets is logged and monitored
□ Backup and recovery procedures are tested
□ Secret validation passes all checks
```

### 🧪 **Testing Secret Security**
```bash
# Validate environment configuration
node scripts/validate-env.js

# Test database connection with secrets
node test-db-connection.js

# Test Redis connection with secrets
node test-redis-connection.js

# Run complete setup test
node test-complete-setup.js
```

## 🚨 Emergency Procedures

### 🔥 **Secret Compromise Response**
1. **Immediate Actions** (within 1 hour):
   ```bash
   # 1. Rotate compromised secret immediately
   openssl rand -base64 48 > new_secret.txt

   # 2. Update all environments
   # 3. Restart affected services
   docker-compose restart

   # 4. Revoke old secret access
   ```

2. **Investigation** (within 24 hours):
   - Review access logs
   - Identify compromise source
   - Document incident
   - Implement additional security measures

3. **Recovery** (within 48 hours):
   - Verify new secrets are working
   - Monitor for any issues
   - Update documentation
   - Conduct security review

### 🔄 **Secret Rotation Procedure**
```bash
# 1. Generate new secret
NEW_SECRET=$(openssl rand -base64 48)

# 2. Update environment variables
export JWT_SECRET="$NEW_SECRET"

# 3. Update GitHub secrets
# Go to repository settings and update

# 4. Restart services
docker-compose restart

# 5. Verify functionality
./scripts/health-check.sh

# 6. Document rotation
echo "$(date): JWT_SECRET rotated" >> secret-rotation.log
```

## 📞 Support & Troubleshooting

### 🐛 **Common Issues**

**❌ "Secret not found" errors**:
```bash
# Check environment variables are set
env | grep -E "(JWT|POSTGRES|REDIS)"

# Verify .env file exists and is readable
ls -la .env
```

**❌ "Invalid secret format" errors**:
```bash
# Validate secret format and length
node scripts/validate-env.js

# Regenerate secrets if needed
./scripts/generate-secrets.sh
```

**❌ Database connection failures**:
```bash
# Test database connection
node test-db-connection.js

# Check PostgreSQL password
echo $POSTGRES_PASSWORD
```

### 🔧 **Debug Commands**
```bash
# Check all environment variables
printenv | sort

# Validate configuration
node scripts/validate-env.js

# Test service connections
./scripts/health-check.sh

# Check Docker container status
docker-compose ps
```

## 🚨 **IMMEDIATE SETUP STEPS**

1. **Generate secure secrets**:
   ```bash
   # Run the automated setup script
   chmod +x scripts/generate-secrets.sh
   ./scripts/generate-secrets.sh
   ```

2. **Verify environment files**:
   ```bash
   # Check that .env files were created
   ls -la .env frontend/.env.local backend/python_analytics/.env
   ```

3. **Test configuration**:
   ```bash
   # Validate all environment variables
   node scripts/validate-env.js
   ```

4. **Start development environment**:
   ```bash
   # Build and start containers
   docker-compose -f docker-compose.dev.yml up -d --build

   # Check service health
   ./scripts/health-check.sh
   ```

---

**🎯 Next Steps**: After implementing secret management, proceed to [GitHub Setup Guide](./GITHUB_SETUP.md) for CI/CD configuration.

**Remember: Security is not optional!** 🔐
