import { jwtDecode } from 'jwt-decode';
import Cookies from 'js-cookie';
import axios from 'axios';
import { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  RegisterResponse,
  ApiResponse,
  ApiError
} from '@/types/api';
import { User } from '@/types/auth';

interface TokenPayload {
  sub: string;
  email: string;
  role: string;
  exp: number;
}

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  username: string;
}

class AuthService {
  private static instance: AuthService;
  private refreshPromise: Promise<AuthTokens> | null = null;
  private readonly TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly CSRF_TOKEN_KEY = 'csrf_token';
  private axiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002',
    timeout: 30000,
    withCredentials: true,
  });

  private constructor() {}

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  private isTokenExpired(token: string): boolean {
    try {
      const decoded = jwtDecode<TokenPayload>(token);
      return decoded.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }

  private setSecureCookie(key: string, value: string, expires: number = 7): void {
    Cookies.set(key, value, {
      expires,
      secure: true,
      sameSite: 'strict',
      path: '/'
    });
  }

  private getSecureCookie(key: string): string | undefined {
    return Cookies.get(key);
  }

  private removeSecureCookie(key: string): void {
    Cookies.remove(key, { path: '/' });
  }

  private generateCSRFToken(): string {
    const token = crypto.randomUUID();
    this.setSecureCookie(this.CSRF_TOKEN_KEY, token);
    return token;
  }

  private validateCSRFToken(token: string): boolean {
    const storedToken = this.getSecureCookie(this.CSRF_TOKEN_KEY);
    return storedToken === token;
  }

  private async refreshTokens(): Promise<AuthTokens> {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = new Promise(async (resolve, reject) => {
      try {
        const refreshToken = this.getSecureCookie(this.REFRESH_TOKEN_KEY);
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const csrfToken = this.generateCSRFToken();
        const response = await this.axiosInstance.post<ApiResponse<AuthTokens>>('/auth/refresh',
          { refreshToken },
          { headers: { 'X-CSRF-Token': csrfToken } }
        );

        const { accessToken, refreshToken: newRefreshToken } = response.data.data;

        this.setSecureCookie(this.TOKEN_KEY, accessToken);
        this.setSecureCookie(this.REFRESH_TOKEN_KEY, newRefreshToken);

        resolve({ accessToken, refreshToken: newRefreshToken });
      } catch (error) {
        this.removeSecureCookie(this.TOKEN_KEY);
        this.removeSecureCookie(this.REFRESH_TOKEN_KEY);
        reject(error);
      } finally {
        this.refreshPromise = null;
      }
    });

    return this.refreshPromise;
  }

  async getAccessToken(): Promise<string> {
    const accessToken = this.getSecureCookie(this.TOKEN_KEY);
    if (!accessToken) {
      throw new Error('No access token available');
    }

    if (this.isTokenExpired(accessToken)) {
      const { accessToken: newAccessToken } = await this.refreshTokens();
      return newAccessToken;
    }

    return accessToken;
  }

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const csrfToken = this.generateCSRFToken();
    const response = await this.axiosInstance.post<ApiResponse<LoginResponse>>('/auth/login',
      credentials,
      { headers: { 'X-CSRF-Token': csrfToken } }
    );

    const { user, accessToken, refreshToken } = response.data.data;

    this.setSecureCookie(this.TOKEN_KEY, accessToken);
    this.setSecureCookie(this.REFRESH_TOKEN_KEY, refreshToken);

    return { user, accessToken, refreshToken };
  }

  async logout(): Promise<void> {
    try {
      const refreshToken = this.getSecureCookie(this.REFRESH_TOKEN_KEY);
      if (refreshToken) {
        const csrfToken = this.generateCSRFToken();
        await this.axiosInstance.post<ApiResponse<void>>('/auth/logout',
          { refreshToken },
          { headers: { 'X-CSRF-Token': csrfToken } }
        );
      }
    } finally {
      this.removeSecureCookie(this.TOKEN_KEY);
      this.removeSecureCookie(this.REFRESH_TOKEN_KEY);
      this.removeSecureCookie(this.CSRF_TOKEN_KEY);
    }
  }

  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    const csrfToken = this.generateCSRFToken();
    const response = await this.axiosInstance.post<ApiResponse<RegisterResponse>>('/auth/register',
      userData,
      { headers: { 'X-CSRF-Token': csrfToken } }
    );

    const { user, accessToken, refreshToken } = response.data.data;

    this.setSecureCookie(this.TOKEN_KEY, accessToken);
    this.setSecureCookie(this.REFRESH_TOKEN_KEY, refreshToken);

    return { user, accessToken, refreshToken };
  }

  async forgotPassword(email: string): Promise<void> {
    const csrfToken = this.generateCSRFToken();
    await this.axiosInstance.post<ApiResponse<void>>('/auth/forgot-password',
      { email },
      { headers: { 'X-CSRF-Token': csrfToken } }
    );
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    const csrfToken = this.generateCSRFToken();
    await this.axiosInstance.post<ApiResponse<void>>(`/auth/reset-password/${token}`,
      { password: newPassword },
      { headers: { 'X-CSRF-Token': csrfToken } }
    );
  }

  async verifyEmail(token: string): Promise<void> {
    const csrfToken = this.generateCSRFToken();
    await this.axiosInstance.get<ApiResponse<void>>(`/auth/verify-email/${token}`, {
      headers: { 'X-CSRF-Token': csrfToken }
    });
  }

  async getCurrentUser(): Promise<User> {
    const csrfToken = this.generateCSRFToken();
    const response = await this.axiosInstance.get<ApiResponse<User>>('/auth/me', {
      headers: { 'X-CSRF-Token': csrfToken }
    });
    return response.data.data;
  }

  hasRole(requiredRoles: string | string[]): boolean {
    const accessToken = this.getSecureCookie(this.TOKEN_KEY);
    if (!accessToken) return false;

    try {
      const decoded = jwtDecode<TokenPayload>(accessToken);
      const userRole = decoded.role;

      if (Array.isArray(requiredRoles)) {
        return requiredRoles.includes(userRole);
      }

      return userRole === requiredRoles;
    } catch {
      return false;
    }
  }

  async getUserRole(): Promise<string | null> {
    const accessToken = this.getSecureCookie(this.TOKEN_KEY);
    if (!accessToken) return null;

    try {
      const decoded = jwtDecode<TokenPayload>(accessToken);
      return decoded.role;
    } catch {
      return null;
    }
  }

  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  isInstructor(): boolean {
    return this.hasRole('instructor');
  }

  isStudent(): boolean {
    return this.hasRole('student');
  }
}

export const authService = AuthService.getInstance();
