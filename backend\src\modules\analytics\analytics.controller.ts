import { Controller, Get, UseGuards, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AnalyticsService } from './analytics.service';

@ApiTags('analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard)
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('learning-patterns/:userId')
  @ApiOperation({ summary: 'Get learning patterns for a user' })
  @ApiResponse({
    status: 200,
    description: 'Learning patterns retrieved successfully',
  })
  async getLearningPatterns(@Param('userId') userId: string) {
    return this.analyticsService.getLearningPatterns(userId);
  }

  @Get('recommendations/:userId')
  @ApiOperation({ summary: 'Get personalized recommendations for a user' })
  @ApiResponse({
    status: 200,
    description: 'Recommendations retrieved successfully',
  })
  async getRecommendations(@Param('userId') userId: string) {
    return this.analyticsService.getRecommendations(userId);
  }

  @Get('performance/:userId')
  @ApiOperation({ summary: 'Get performance metrics for a user' })
  @ApiResponse({
    status: 200,
    description: 'Performance metrics retrieved successfully',
  })
  async getPerformanceMetrics(@Param('userId') userId: string) {
    return this.analyticsService.getPerformanceMetrics(userId);
  }

  @Get('benchmarks/:userId')
  @ApiOperation({ summary: 'Get peer benchmarks for a user' })
  @ApiResponse({
    status: 200,
    description: 'Peer benchmarks retrieved successfully',
  })
  async getPeerBenchmarks(@Param('userId') userId: string) {
    return this.analyticsService.getPeerBenchmarks(userId);
  }
}
