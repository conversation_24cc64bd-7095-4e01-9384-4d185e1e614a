# PowerShell script to set up the PostgreSQL database for MedTrack Hub
# Run this script as Administrator or with appropriate PostgreSQL permissions

Write-Host "Setting up PostgreSQL database for MedTrack Hub..." -ForegroundColor Green

# Check if PostgreSQL is running
$pgStatus = pg_isready -h localhost -p 5432
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: PostgreSQL is not running on localhost:5432" -ForegroundColor Red
    Write-Host "Please start PostgreSQL service and try again." -ForegroundColor Yellow
    exit 1
}

Write-Host "PostgreSQL is running. Setting up database..." -ForegroundColor Green

# Run the SQL setup script
Write-Host "Please enter the PostgreSQL superuser (postgres) password when prompted." -ForegroundColor Yellow
psql -h localhost -p 5432 -U postgres -f setup-database.sql

if ($LASTEXITCODE -eq 0) {
    Write-Host "Database setup completed successfully!" -ForegroundColor Green
    Write-Host "Testing connection..." -ForegroundColor Yellow
    
    # Test the connection
    $env:PGPASSWORD = "AU110s/6081/2021MT"
    psql -h localhost -p 5432 -U medical -d medical_tracker -c "SELECT version();"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Connection test successful! Database is ready." -ForegroundColor Green
    } else {
        Write-Host "Connection test failed. Please check the setup." -ForegroundColor Red
    }
} else {
    Write-Host "Database setup failed. Please check the error messages above." -ForegroundColor Red
}

Write-Host "Setup script completed." -ForegroundColor Blue
