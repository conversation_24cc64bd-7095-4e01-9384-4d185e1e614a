const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

function generateSecureSecret(length = 32) {
  return crypto.randomBytes(length).toString('base64');
}

function generateSecrets() {
  const secrets = {
    JWT_SECRET: generateSecureSecret(32),
    JWT_REFRESH_SECRET: generateSecureSecret(32),
    POSTGRES_PASSWORD: generateSecureSecret(24),
    REDIS_PASSWORD: generateSecureSecret(24)
  };

  // Generate secrets for different environments
  const environments = ['development', 'staging', 'production'];
  
  environments.forEach(env => {
    const envFile = path.join(process.cwd(), `.env.${env}`);
    let envContent = '';

    // Read existing env file if it exists
    if (fs.existsSync(envFile)) {
      envContent = fs.readFileSync(envFile, 'utf8');
    }

    // Update secrets in env content
    Object.entries(secrets).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      const newLine = `${key}=${value}`;
      
      if (regex.test(envContent)) {
        envContent = envContent.replace(regex, newLine);
      } else {
        envContent += `\n${newLine}`;
      }
    });

    // Write updated content back to file
    fs.writeFileSync(envFile, envContent.trim() + '\n');
    console.log(`Updated secrets in ${envFile}`);
  });

  console.log('\nGenerated new secrets. Please update these in your secure secret management system for production.');
  console.log('\nWarning: Never commit .env files containing real secrets to version control!');
}

if (require.main === module) {
  generateSecrets();
}

module.exports = { generateSecureSecret };
