/**
 * Cross-Module Integration Tests
 *
 * This file contains tests for how different modules communicate with each other.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import api from '../services/api';
import mockApiService from '../services/mockApiService';

// Mock the API service
vi.mock('../services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock the session
vi.mock('next-auth/react', () => ({
  useSession: vi.fn(() => ({
    data: {
      user: {
        id: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        accessToken: 'test-token',
      },
    },
    status: 'authenticated',
  })),
}));

describe('Cross-Module Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Materials and Units Integration', () => {
    it('should fetch materials and their units', async () => {
      // Mock API responses
      (api.get as any).mockImplementation(url => {
        if (url === '/materials') {
          return Promise.resolve({
            data: [
              { id: '1', title: 'Material 1' },
              { id: '2', title: 'Material 2' },
            ],
          });
        } else if (url === '/units') {
          return Promise.resolve({
            data: [
              { id: '1', title: 'Unit 1', materialId: '1' },
              { id: '2', title: 'Unit 2', materialId: '1' },
              { id: '3', title: 'Unit 3', materialId: '2' },
            ],
          });
        }
        return Promise.reject(new Error('Unexpected URL'));
      });

      // Fetch materials
      const materialsResponse = await api.get('/materials');
      const materials = materialsResponse.data;

      // Fetch units
      const unitsResponse = await api.get('/units');
      const units = unitsResponse.data;

      // Group units by material
      const materialUnits = materials.map(material => ({
        ...material,
        units: units.filter(unit => unit.materialId === material.id),
      }));

      expect(api.get).toHaveBeenCalledWith('/materials');
      expect(api.get).toHaveBeenCalledWith('/units');
      expect(materialUnits.length).toBe(2);
      expect(materialUnits[0].units.length).toBe(2);
      expect(materialUnits[1].units.length).toBe(1);
    });
  });

  describe('Auth and Progress Integration', () => {
    it('should fetch user progress for authenticated user', async () => {
      // Mock API responses
      (api.get as any).mockImplementation(url => {
        if (url === '/auth/profile') {
          return Promise.resolve({
            data: {
              id: 'test-user-id',
              name: 'Test User',
              email: '<EMAIL>',
            },
          });
        } else if (url === '/progress/user/test-user-id') {
          return Promise.resolve({
            data: [
              { id: '1', userId: 'test-user-id', unitId: '1', completed: true },
              { id: '2', userId: 'test-user-id', unitId: '2', completed: false },
            ],
          });
        }
        return Promise.reject(new Error('Unexpected URL'));
      });

      // Get user profile
      const profileResponse = await api.get('/auth/profile');
      const user = profileResponse.data;

      // Get user progress
      const progressResponse = await api.get(`/progress/user/${user.id}`);
      const progress = progressResponse.data;

      expect(api.get).toHaveBeenCalledWith('/auth/profile');
      expect(api.get).toHaveBeenCalledWith('/progress/user/test-user-id');
      expect(user.id).toBe('test-user-id');
      expect(progress.length).toBe(2);
      expect(progress[0].userId).toBe('test-user-id');
    });
  });

  describe('Materials and Quiz Integration', () => {
    it('should fetch material, its units, and quizzes', async () => {
      // Mock API responses
      (api.get as any).mockImplementation(url => {
        if (url === '/materials/1') {
          return Promise.resolve({
            data: { id: '1', title: 'Material 1' },
          });
        } else if (url === '/units') {
          return Promise.resolve({
            data: [
              { id: '1', title: 'Unit 1', materialId: '1' },
              { id: '2', title: 'Unit 2', materialId: '1' },
            ],
          });
        } else if (url === '/quiz/unit/1') {
          return Promise.resolve({
            data: {
              id: '1',
              unitId: '1',
              questions: [{ id: '1', text: 'Question 1' }],
            },
          });
        }
        return Promise.reject(new Error('Unexpected URL'));
      });

      // Fetch material
      const materialResponse = await api.get('/materials/1');
      const material = materialResponse.data;

      // Fetch units for material
      const unitsResponse = await api.get('/units');
      const units = unitsResponse.data.filter(unit => unit.materialId === material.id);

      // Fetch quiz for first unit
      const quizResponse = await api.get(`/quiz/unit/${units[0].id}`);
      const quiz = quizResponse.data;

      expect(api.get).toHaveBeenCalledWith('/materials/1');
      expect(api.get).toHaveBeenCalledWith('/units');
      expect(api.get).toHaveBeenCalledWith('/quiz/unit/1');
      expect(material.id).toBe('1');
      expect(units.length).toBe(2);
      expect(quiz.unitId).toBe('1');
      expect(quiz.questions.length).toBe(1);
    });
  });

  describe('Quiz and Progress Integration', () => {
    it('should submit quiz and update progress', async () => {
      // Mock API responses
      (api.post as any).mockImplementation((url, data) => {
        if (url === '/quiz/submit') {
          return Promise.resolve({
            data: {
              score: 80,
              totalQuestions: 5,
              correctAnswers: 4,
              unitId: data.unitId,
              userId: data.userId,
            },
          });
        } else if (url === `/progress/${data.unitId}`) {
          return Promise.resolve({
            data: {
              id: '1',
              userId: data.userId,
              unitId: data.unitId,
              completed: data.completed,
              completedAt: new Date().toISOString(),
            },
          });
        }
        return Promise.reject(new Error('Unexpected URL'));
      });

      // Submit quiz
      const quizSubmission = {
        unitId: '1',
        userId: 'test-user-id',
        answers: [
          { questionId: '1', selectedOptionId: '2' },
          { questionId: '2', selectedOptionId: '3' },
        ],
      };

      const quizResponse = await api.post('/quiz/submit', quizSubmission);
      const quizResult = quizResponse.data;

      // Update progress based on quiz result
      const progressData = {
        userId: quizSubmission.userId,
        unitId: quizSubmission.unitId,
        completed: quizResult.score >= 70, // Pass if score is 70% or higher
      };

      const progressResponse = await api.post(`/progress/${progressData.unitId}`, progressData);
      const progress = progressResponse.data;

      expect(api.post).toHaveBeenCalledWith('/quiz/submit', quizSubmission);
      expect(api.post).toHaveBeenCalledWith(`/progress/${progressData.unitId}`, progressData);
      expect(quizResult.score).toBe(80);
      expect(progress.unitId).toBe('1');
      expect(progress.completed).toBe(true);
    });
  });

  describe('Materials and Notifications Integration', () => {
    it('should create notification when new material is added', async () => {
      // Mock API responses
      (api.post as any).mockImplementation((url, data) => {
        if (url === '/materials') {
          return Promise.resolve({
            data: {
              id: '3',
              title: data.title,
              description: data.description,
            },
          });
        } else if (url === '/notifications') {
          return Promise.resolve({
            data: {
              id: '1',
              title: data.title,
              message: data.message,
              type: data.type,
              date: new Date().toISOString(),
              read: false,
            },
          });
        }
        return Promise.reject(new Error('Unexpected URL'));
      });

      // Create new material
      const materialData = {
        title: 'New Material',
        description: 'This is a new material',
      };

      const materialResponse = await api.post('/materials', materialData);
      const material = materialResponse.data;

      // Create notification for new material
      const notificationData = {
        title: 'New Material Available',
        message: `A new material "${material.title}" has been added.`,
        type: 'info',
      };

      const notificationResponse = await api.post('/notifications', notificationData);
      const notification = notificationResponse.data;

      expect(api.post).toHaveBeenCalledWith('/materials', materialData);
      expect(api.post).toHaveBeenCalledWith('/notifications', notificationData);
      expect(material.title).toBe(materialData.title);
      expect(notification.title).toBe(notificationData.title);
      expect(notification.message).toContain(material.title);
    });
  });
});
