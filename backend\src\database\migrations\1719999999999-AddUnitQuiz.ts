import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableColumn,
  TableForeignKey,
} from 'typeorm';

export class AddUnitQuiz1719999999999 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create unit_quizzes table
    await queryRunner.createTable(
      new Table({
        name: 'unit_quizzes',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid',
          },
          { name: 'unitId', type: 'uuid' },
          { name: 'title', type: 'varchar' },
          { name: 'instructions', type: 'text' },
          { name: 'isPublished', type: 'boolean', default: false },
          { name: 'createdAt', type: 'timestamp', default: 'now()' },
          { name: 'updatedAt', type: 'timestamp', default: 'now()' },
        ],
      }),
    );

    // Add foreign key to unit_quizzes
    await queryRunner.createForeignKey(
      'unit_quizzes',
      new TableForeignKey({
        columnNames: ['unitId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'units',
        onDelete: 'CASCADE',
      }),
    );

    // Add unitQuizId to quiz_questions
    await queryRunner.addColumn(
      'quiz_questions',
      new TableColumn({
        name: 'unitQuizId',
        type: 'uuid',
        isNullable: true,
      }),
    );

    await queryRunner.createForeignKey(
      'quiz_questions',
      new TableForeignKey({
        columnNames: ['unitQuizId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'unit_quizzes',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key and column from quiz_questions
    const table = await queryRunner.getTable('quiz_questions');
    if (table) {
      const fk = table.foreignKeys.find(
        (fk: TableForeignKey) => fk.columnNames.indexOf('unitQuizId') !== -1,
      );
      if (fk) await queryRunner.dropForeignKey('quiz_questions', fk);
    }
    await queryRunner.dropColumn('quiz_questions', 'unitQuizId');

    // Drop unit_quizzes table
    await queryRunner.dropTable('unit_quizzes');
  }
}
