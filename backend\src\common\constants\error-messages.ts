export const ErrorMessages = {
  AUTH: {
    INVALID_CREDENTIALS: 'Invalid username or password',
    EMAIL_EXISTS: 'Email is already registered',
    USERNAME_EXISTS: 'Username is already taken',
    PASSWORD_REQUIREMENTS: {
      LENGTH: 'Password must be at least 8 characters long',
      UPPERCASE: 'Password must contain at least one uppercase letter',
      LOWERCASE: 'Password must contain at least one lowercase letter',
      NUMBER: 'Password must contain at least one number',
      SPECIAL: 'Password must contain at least one special character (!@#$%^&*)',
    },
    TOKEN_INVALID: 'Invalid or expired token',
    TOKEN_MISSING: 'Authentication token is required',
    UNAUTHORIZED: 'You are not authorized to perform this action',
  },
  VALIDATION: {
    REQUIRED: (field: string) => `${field} is required`,
    INVALID_FORMAT: (field: string) => `Invalid ${field} format`,
    MIN_LENGTH: (field: string, length: number) => `${field} must be at least ${length} characters`,
    MAX_LENGTH: (field: string, length: number) => `${field} must not exceed ${length} characters`,
  },
  SERVER: {
    INTERNAL_ERROR: 'An internal server error occurred',
    SERVICE_UNAVAILABLE: 'Service is temporarily unavailable',
    DATABASE_ERROR: 'Database operation failed',
  },
  USER: {
    NOT_FOUND: 'User not found',
    INACTIVE: 'User account is inactive',
    ALREADY_EXISTS: 'User already exists',
  },
} as const; 