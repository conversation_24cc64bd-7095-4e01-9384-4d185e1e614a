#!/bin/bash

# MedTrack Hub Docker Setup Script
# This script sets up the complete Docker environment for the MedTrack Hub application

set -e

echo "🚀 MedTrack Hub Docker Setup"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if .env file exists
check_env_file() {
    print_status "Checking environment configuration..."
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_warning "Please edit .env file with your actual configuration values"
        print_warning "Especially update passwords and secrets!"
    else
        print_success ".env file found"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    directories=(
        "nginx/ssl"
        "backend/uploads"
        "backend/logs"
        "frontend/logs"
        "monitoring/data"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        fi
    done
    
    print_success "All directories created"
}

# Generate self-signed SSL certificates for development
generate_ssl_certs() {
    print_status "Checking SSL certificates..."
    
    if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/key.pem" ]; then
        print_warning "SSL certificates not found. Generating self-signed certificates for development..."
        
        openssl req -x509 -newkey rsa:4096 -keyout nginx/ssl/key.pem -out nginx/ssl/cert.pem -days 365 -nodes \
            -subj "/C=US/ST=State/L=City/O=MedTrack Hub/CN=localhost" 2>/dev/null
        
        chmod 600 nginx/ssl/key.pem
        chmod 644 nginx/ssl/cert.pem
        
        print_success "Self-signed SSL certificates generated"
    else
        print_success "SSL certificates found"
    fi
}

# Build Docker images
build_images() {
    print_status "Building Docker images..."
    
    # Build backend
    print_status "Building backend image..."
    docker-compose build backend
    
    # Build frontend
    print_status "Building frontend image..."
    docker-compose build frontend
    
    # Build analytics
    print_status "Building analytics image..."
    docker-compose build analytics
    
    print_success "All images built successfully"
}

# Start services
start_services() {
    local mode=$1
    
    if [ "$mode" = "dev" ]; then
        print_status "Starting development environment..."
        docker-compose -f docker-compose.dev.yml up -d
    elif [ "$mode" = "monitoring" ]; then
        print_status "Starting monitoring services..."
        docker-compose -f docker-compose.monitoring.yml up -d
    else
        print_status "Starting production environment..."
        docker-compose up -d
    fi
    
    print_success "Services started successfully"
}

# Show service status
show_status() {
    print_status "Service Status:"
    docker-compose ps
    
    echo ""
    print_status "Available Services:"
    echo "🌐 Frontend: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:3002"
    echo "📊 Analytics: http://localhost:5000"
    echo "🗄️  Database: localhost:5432"
    echo "🔴 Redis: localhost:6379"
    echo "📈 Prometheus: http://localhost:9090"
    echo "📊 Grafana: http://localhost:3001"
    echo "🚨 AlertManager: http://localhost:9093"
}

# Main script logic
main() {
    local command=${1:-"start"}
    
    case $command in
        "start")
            check_docker
            check_env_file
            create_directories
            generate_ssl_certs
            build_images
            start_services "prod"
            show_status
            ;;
        "dev")
            check_docker
            check_env_file
            create_directories
            start_services "dev"
            show_status
            ;;
        "monitoring")
            check_docker
            check_env_file
            start_services "monitoring"
            show_status
            ;;
        "stop")
            print_status "Stopping all services..."
            docker-compose down
            docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
            docker-compose -f docker-compose.monitoring.yml down 2>/dev/null || true
            print_success "All services stopped"
            ;;
        "restart")
            $0 stop
            sleep 2
            $0 start
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "status")
            show_status
            ;;
        "clean")
            print_warning "This will remove all containers, images, and volumes!"
            read -p "Are you sure? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                docker-compose down -v --rmi all
                docker system prune -f
                print_success "Cleanup completed"
            fi
            ;;
        *)
            echo "Usage: $0 {start|dev|monitoring|stop|restart|logs|status|clean}"
            echo ""
            echo "Commands:"
            echo "  start      - Start production environment"
            echo "  dev        - Start development environment"
            echo "  monitoring - Start monitoring services only"
            echo "  stop       - Stop all services"
            echo "  restart    - Restart all services"
            echo "  logs       - Show logs"
            echo "  status     - Show service status"
            echo "  clean      - Remove all containers and images"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
