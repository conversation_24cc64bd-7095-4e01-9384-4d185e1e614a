import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useStudy } from '../../hooks/useStudy';
import { usePeerBenchmarking } from '../../hooks/usePeerBenchmarking';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Button,
  CircularProgress,
  useTheme,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/lab';
import { formatDistanceToNow } from 'date-fns';

interface StudyStats {
  totalStudyTime: number;
  totalSessions: number;
  currentStreak: number;
}

export const StudyDashboard: React.FC = () => {
  const { user } = useAuth();
  const { getStudyStats, getTopicsByCategory } = useStudy();
  const { getPeerStats } = usePeerBenchmarking();
  const theme = useTheme();

  const [stats, setStats] = useState<StudyStats | null>(null);
  const [topics, setTopics] = useState<any[]>([]);
  const [peerStats, setPeerStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadDashboard = async () => {
      try {
        const [statsData, topicsData] = await Promise.all([
          getStudyStats(),
          getTopicsByCategory('all'),
        ]);
        setStats(statsData);
        setTopics(topicsData);

        if (topicsData.length > 0) {
          const peerData = await getPeerStats(topicsData[0].id);
          setPeerStats(peerData);
        }
      } catch (error) {
        console.error('Error loading dashboard:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboard();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Stats Overview */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Study Streak
              </Typography>
              <Typography variant="h3" color="primary">
                {stats?.currentStreak || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                days
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total Study Time
              </Typography>
              <Typography variant="h3" color="primary">
                {Math.round((stats?.totalStudyTime || 0) / 60)}h
              </Typography>
              <Typography variant="body2" color="textSecondary">
                this week
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Study Sessions
              </Typography>
              <Typography variant="h3" color="primary">
                {stats?.totalSessions || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                completed
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Current Progress */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Progress
              </Typography>
              {topics.map(topic => (
                <Box key={topic.id} mb={2}>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body1">{topic.title}</Typography>
                    <Typography variant="body2" color="textSecondary">
                      {topic.completionPercentage}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={topic.completionPercentage}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: theme.palette.grey[200],
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                      },
                    }}
                  />
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Peer Comparison */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Peer Comparison
              </Typography>
              {peerStats && (
                <Box>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    You're in the top {Math.round(peerStats.percentile)}%
                  </Typography>
                  <Box mt={2}>
                    <Typography variant="body2">
                      Average Completion: {Math.round(peerStats.peerAverages.completionPercentage)}%
                    </Typography>
                    <Typography variant="body2">
                      Your Completion: {Math.round(peerStats.userProgress.completionPercentage)}%
                    </Typography>
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Study Timeline */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Activity
          </Typography>
          <Timeline>
            {topics.slice(0, 5).map(topic => (
              <TimelineItem key={topic.id}>
                <TimelineSeparator>
                  <TimelineDot color="primary" />
                  <TimelineConnector />
                </TimelineSeparator>
                <TimelineContent>
                  <Typography variant="body1">{topic.title}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    {formatDistanceToNow(new Date(topic.lastStudiedAt), { addSuffix: true })}
                  </Typography>
                </TimelineContent>
              </TimelineItem>
            ))}
          </Timeline>
        </CardContent>
      </Card>
    </Box>
  );
};
