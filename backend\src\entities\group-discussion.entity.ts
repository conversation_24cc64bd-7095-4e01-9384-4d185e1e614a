import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { StudyGroup } from './study-group.entity';
import { User } from './user.entity';
import { DiscussionMessage } from './discussion-message.entity';

export enum DiscussionType {
  GENERAL = 'general',
  QUESTION = 'question',
  CASE_STUDY = 'case_study',
  RESOURCE_SHARING = 'resource_sharing',
  ANNOUNCEMENT = 'announcement',
  POLL = 'poll',
}

export enum DiscussionStatus {
  ACTIVE = 'active',
  CLOSED = 'closed',
  PINNED = 'pinned',
  ARCHIVED = 'archived',
}

@Entity('group_discussions')
export class GroupDiscussion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: DiscussionType,
    default: DiscussionType.GENERAL,
  })
  type: DiscussionType;

  @Column({
    type: 'enum',
    enum: DiscussionStatus,
    default: DiscussionStatus.ACTIVE,
  })
  status: DiscussionStatus;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'int', default: 0 })
  message_count: number;

  @Column({ type: 'int', default: 0 })
  participant_count: number;

  @Column({ type: 'int', default: 0 })
  view_count: number;

  @Column({ type: 'int', default: 0 })
  like_count: number;

  @Column({ type: 'boolean', default: false })
  is_pinned: boolean;

  @Column({ type: 'boolean', default: false })
  is_solved: boolean;

  @Column({ type: 'timestamp', nullable: true })
  last_activity: Date;

  @Column({ type: 'uuid', nullable: true })
  last_message_by: string;

  @Column({ type: 'jsonb', nullable: true })
  poll_data: {
    question: string;
    options: {
      id: string;
      text: string;
      votes: number;
      voters: string[];
    }[];
    multiple_choice: boolean;
    expires_at?: Date;
    is_anonymous: boolean;
  };

  @Column({ type: 'jsonb', nullable: true })
  attachments: {
    id: string;
    name: string;
    type: 'image' | 'document' | 'video' | 'audio' | 'link';
    url: string;
    size?: number;
    uploaded_at: Date;
  }[];

  @Column({ type: 'uuid' })
  study_group_id: string;

  @Column({ type: 'uuid' })
  created_by: string;

  @ManyToOne(() => StudyGroup, (studyGroup) => studyGroup.discussions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'study_group_id' })
  study_group: StudyGroup;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => DiscussionMessage, (message) => message.discussion)
  messages: DiscussionMessage[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
