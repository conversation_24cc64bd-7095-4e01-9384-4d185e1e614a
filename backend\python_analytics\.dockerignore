# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.coverage.*
coverage.xml
*.cover
.hypothesis/
test_*.py

# Environment variables
.env*

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Git
.git
.gitignore
README.md
CHANGELOG.md

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Scripts
scripts

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Temporary files
tmp
temp