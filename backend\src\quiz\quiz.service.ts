import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QuizQuestion } from '../entities/quiz-question.entity';
import { UserResponse } from '../entities/user-response.entity';
import { UserFeaturesService } from '../modules/features/user-features.service';

@Injectable()
export class QuizService {
  constructor(
    @InjectRepository(QuizQuestion)
    private quizQuestionRepository: Repository<QuizQuestion>,
    @InjectRepository(UserResponse)
    private userResponseRepository: Repository<UserResponse>,
    private readonly userFeaturesService: UserFeaturesService, // Injected
  ) {}

  async getQuestionsByUnit(unitId: string): Promise<QuizQuestion[]> {
    return this.quizQuestionRepository.find({
      where: { unit: { id: unitId } },
    });
  }

  async submitAnswer(
    userId: string,
    questionId: string,
    answer: string,
  ): Promise<UserResponse> {
    const question = await this.quizQuestionRepository.findOne({
      where: { id: questionId },
    });

    if (!question) {
      throw new NotFoundException('Question not found');
    }

    const isCorrect = question.correct_answer === answer;

    const response = this.userResponseRepository.create({
      user: { id: userId },
      question: { id: questionId },
      selected_answer: answer,
      is_correct: isCorrect,
    });

    return this.userResponseRepository.save(response);
  }

  async getUserQuizResults(
    userId: string,
    unitId: string,
  ): Promise<{
    total: number;
    correct: number;
    percentage: number;
  }> {
    const responses = await this.userResponseRepository.find({
      where: {
        user: { id: userId },
        question: { unit: { id: unitId } },
      },
    });

    const total = responses.length;
    const correct = responses.filter((r: UserResponse) => r.is_correct).length;

    return {
      total,
      correct,
      percentage: total > 0 ? (correct / total) * 100 : 0,
    };
  }

  async getRapidReviewQuestions(
    userId: string,
    topics?: string[],
  ): Promise<QuizQuestion[]> {
    let reviewTopics = topics;
    if (!reviewTopics || reviewTopics.length === 0) {
      // Get weak areas from user features
      const profile =
        await this.userFeaturesService.getUserFeatureProfile(userId);
      reviewTopics = profile?.learningPattern.weakAreas || [];
    }
    if (!reviewTopics || reviewTopics.length === 0) {
      // Fallback: return random questions
      return this.quizQuestionRepository
        .createQueryBuilder('q')
        .orderBy('RANDOM()')
        .limit(10)
        .getMany();
    }
    // Fetch questions from the selected topics
    const questions = await this.quizQuestionRepository
      .createQueryBuilder('q')
      .where('q.topic IN (:...topics)', { topics: reviewTopics })
      .orderBy('RANDOM()')
      .limit(10)
      .getMany();
    return questions;
  }
}
