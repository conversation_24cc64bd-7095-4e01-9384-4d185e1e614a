import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../../src/app.module';
import { TestModuleConfig } from '../test.config';
import { AuthService } from '../../src/modules/auth/auth.service';
import { UserRole } from '../../src/entities/user.entity';

describe('API (e2e)', () => {
  let app: INestApplication;
  let authService: AuthService;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider('CONFIG')
      .useValue(TestModuleConfig)
      .compile();

    app = moduleFixture.createNestApplication();
    authService = moduleFixture.get<AuthService>(AuthService);
    await app.init();

    // Create test user and get token
    const testUser = await authService.register({
      email: '<EMAIL>',
      password: 'Test123!',
      name: 'testuser',
      role: UserRole.STUDENT,
    });
    const { accessToken } = await authService.login({
      email: '<EMAIL>',
      password: 'Test123!',
      rememberMe: false,
      deviceName: 'test-device',
      deviceId: 'test-id',
      deviceType: 'test',
      deviceToken: '',
      ipAddress: '127.0.0.1',
      userAgent: 'test-agent',
      deviceOs: 'test-os',
      deviceBrowser: 'test-browser',
      deviceBrowserVersion: 'test-version',
    });
    authToken = accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Auth', () => {
    it('/auth/login (POST)', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test123!',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.access_token).toBeDefined();
        });
    });
  });

  describe('Materials', () => {
    it('/materials (GET)', () => {
      return request(app.getHttpServer())
        .get('/materials')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('/materials (POST)', () => {
      return request(app.getHttpServer())
        .post('/materials')
        .set('Authorization', `Bearer ${authToken}`)
        .field('title', 'Test Material')
        .field('description', 'Test Description')
        .attach('file', 'test/fixtures/test.pdf')
        .expect(201);
    });
  });

  describe('Units', () => {
    it('/units (GET)', () => {
      return request(app.getHttpServer())
        .get('/units')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });
  });

  describe('Health', () => {
    it('/health (GET)', () => {
      return request(app.getHttpServer())
        .get('/health')
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('ok');
        });
    });
  });
});
