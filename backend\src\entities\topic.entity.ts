import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { User } from './user.entity';
import { TopicProgress } from './topic-progress.entity';
import { Unit } from './unit.entity';

@Entity()
export class Topic {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text')
  description: string;

  @Column()
  category: string; // e.g., 'Anatomy', 'Physiology', 'Pathology'

  @Column()
  difficulty: number; // 1-5 scale

  @Column()
  estimatedHours: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'jsonb', nullable: true })
  prerequisites: string[]; // Array of topic IDs that should be completed first

  @Column({ type: 'jsonb', nullable: true })
  resources: {
    type: string; // 'pdf', 'video', 'quiz'
    url: string;
    title: string;
  }[];

  @OneToMany(() => Unit, (unit: Unit) => unit.topic)
  units: Unit[];

  @OneToMany(() => TopicProgress, (progress: TopicProgress) => progress.topic)
  progress: TopicProgress[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
