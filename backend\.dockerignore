# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Testing
coverage
*.lcov
test-results
.nyc_output
test
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Environment variables
.env*

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Build outputs (keep dist for production builds)
.next
out

# Development files
*.tsbuildinfo
.eslintcache

# Git
.git
.gitignore
README.md
CHANGELOG.md

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Scripts
scripts

# Python analytics (not needed in Node.js container)
python_analytics

# Temporary files
tmp
temp
