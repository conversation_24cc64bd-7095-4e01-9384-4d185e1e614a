import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    const isAuth = !!token;
    const isAuthPage = req.nextUrl.pathname.startsWith('/auth');

    if (isAuthPage) {
      if (isAuth) {
        return NextResponse.redirect(new URL('/dashboard', req.url));
      }
      return null;
    }

    if (!isAuth) {
      let from = req.nextUrl.pathname;
      if (req.nextUrl.search) {
        from += req.nextUrl.search;
      }

      return NextResponse.redirect(
        new URL(`/auth/login?from=${encodeURIComponent(from)}`, req.url)
      );
    }
  },
  {
    callbacks: {
      authorized: ({ req, token }) => {
        // Public routes that don't require authentication
        if (
          req.nextUrl.pathname.startsWith('/auth/') ||
          req.nextUrl.pathname.startsWith('/api/auth/')
        ) {
          return true;
        }

        // Check if user is authenticated
        if (!token) {
          return false;
        }

        // Admin-only routes
        const adminRoutes = ['/settings/admin', '/users/manage'];
        if (adminRoutes.some(route => req.nextUrl.pathname.startsWith(route))) {
          return token.role === 'admin';
        }

        // Instructor-only routes
        const instructorRoutes = ['/courses/create', '/courses/edit'];
        if (instructorRoutes.some(route => req.nextUrl.pathname.startsWith(route))) {
          return ['admin', 'instructor'].includes(token.role as string);
        }

        // All other protected routes just need authentication
        return true;
      },
    },
  }
);

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/materials/:path*',
    '/courses/:path*',
    '/progress/:path*',
    '/schedule/:path*',
    '/quiz/:path*',
    '/upload/:path*',
    '/settings/:path*',
    '/profile/:path*',
    '/notifications/:path*',
    '/chat/:path*',
    '/auth/:path*',
  ],
};
