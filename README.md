# 🏥 MedTrack Hub

> A comprehensive medical education and tracking platform built with modern technologies

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-20+-green.svg)](https://nodejs.org/)
[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com/)

## 🚀 Quick Start

### Using Docker (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd medical

# Copy environment template
cp .env.example .env
# Edit .env with your configuration

# Start all services
docker-compose up -d

# Check service health
./scripts/health-check.sh
```

### Manual Setup

```bash
# Install dependencies
npm install

# Start development environment
npm run dev:all
```

## 📁 Project Structure

```
medical/
├── 📁 frontend/                 # Next.js 13+ App Router Frontend
│   ├── 📁 src/
│   │   ├── 📁 app/             # App Router pages
│   │   ├── 📁 components/      # Reusable UI components
│   │   ├── 📁 lib/            # Utilities and configurations
│   │   └── 📁 types/          # TypeScript type definitions
│   ├── 📄 Dockerfile          # Production Docker image
│   ├── 📄 Dockerfile.dev      # Development Docker image
│   └── 📄 package.json
│
├── 📁 backend/                  # NestJS Backend API
│   ├── 📁 src/
│   │   ├── 📁 auth/           # Authentication module
│   │   ├── 📁 users/          # User management
│   │   ├── 📁 database/       # Database configuration
│   │   ├── 📁 common/         # Shared utilities
│   │   └── 📁 config/         # Application configuration
│   ├── 📁 python_analytics/   # Python Analytics Service
│   │   ├── 📁 analytics/      # Core analytics modules
│   │   ├── 📁 auth/           # JWT authentication
│   │   ├── 📄 Dockerfile      # Production Docker image
│   │   └── 📄 requirements.txt
│   ├── 📄 Dockerfile          # Production Docker image
│   └── 📄 package.json
│
├── 📁 docs/                     # Documentation
├── 📁 scripts/                  # Development and deployment scripts
├── 📄 docker-compose.yml       # Production Docker Compose
├── 📄 docker-compose.dev.yml   # Development Docker Compose
├── 📄 .env.example             # Environment template
└── 📄 README.md                # This file
```

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 13+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **State Management**: Zustand
- **Authentication**: NextAuth.js

### Backend
- **Framework**: NestJS
- **Language**: TypeScript
- **Database**: PostgreSQL with TypeORM
- **Cache**: Redis
- **Authentication**: JWT with Passport
- **Documentation**: Swagger/OpenAPI

### Analytics
- **Framework**: FastAPI
- **Language**: Python 3.11+
- **ML Libraries**: TensorFlow, scikit-learn
- **Database**: PostgreSQL with SQLAlchemy
- **Authentication**: JWT

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Reverse Proxy**: Nginx (production)
- **Monitoring**: Health checks & logging
- **CI/CD**: GitHub Actions

## 🔧 Development

### Prerequisites

- **Node.js**: 20+ (LTS recommended)
- **Python**: 3.11+
- **Docker**: Latest stable version
- **PostgreSQL**: 15+ (if running locally)
- **Redis**: 7+ (if running locally)

### Environment Setup

1. **Copy environment template**:
   ```bash
   cp .env.example .env
   ```

2. **Configure your environment**:
   ```bash
   # Edit .env with your specific values
   # NEVER commit .env files to version control
   ```

3. **Generate secure secrets**:
   ```bash
   # Generate JWT secrets (32+ characters recommended)
   openssl rand -base64 32
   ```

### Development Commands

```bash
# Start all services in development mode
npm run dev:all

# Start individual services
npm run dev:frontend
npm run dev:backend
npm run dev:analytics

# Run tests
npm run test:all

# Build for production
npm run build:all

# Health check all services
npm run health-check
```

## 🐳 Docker Development

### Development Environment

```bash
# Start development environment with hot reloading
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop development environment
docker-compose -f docker-compose.dev.yml down
```

### Production Environment

```bash
# Build and start production environment
docker-compose up -d --build

# Scale services
docker-compose up -d --scale backend=3

# Monitor services
docker-compose ps
docker-compose logs -f
```

## 🔒 Security

- **Environment Variables**: Never commit `.env` files
- **Secrets Management**: Use secure secret generation
- **Authentication**: JWT with refresh tokens
- **Authorization**: Role-based access control
- **Database**: Parameterized queries to prevent SQL injection
- **CORS**: Configured for specific origins
- **Headers**: Security headers implemented
- **Docker**: Non-root users in containers

## 📊 Monitoring & Health Checks

### Health Check Endpoints

- **Frontend**: `GET /api/health`
- **Backend**: `GET /api/health`
- **Analytics**: `GET /health`

### Monitoring Commands

```bash
# Check all services
./scripts/health-check.sh

# Individual service checks
node frontend/scripts/health-check.js
node backend/scripts/health-check.js
python backend/python_analytics/scripts/health-check.py
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm run test:all

# Frontend tests
cd frontend && npm test

# Backend tests
cd backend && npm run test:all

# Analytics tests
cd backend/python_analytics && python -m pytest
```

### Test Coverage

```bash
# Generate coverage reports
npm run test:coverage
```

## 📚 API Documentation

- **Backend API**: `http://localhost:3002/api/docs` (Swagger)
- **Analytics API**: `http://localhost:5000/docs` (FastAPI docs)

## 🚀 Deployment

### Production Deployment

1. **Set up environment**:
   ```bash
   cp .env.example .env.production
   # Configure production values
   ```

2. **Deploy with Docker**:
   ```bash
   docker-compose -f docker-compose.yml up -d --build
   ```

3. **Verify deployment**:
   ```bash
   ./scripts/health-check.sh
   ```

### Environment-Specific Configurations

- **Development**: `docker-compose.dev.yml`
- **Production**: `docker-compose.yml`
- **CI/CD**: `.github/workflows/`

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Follow coding standards**: Run linting and tests
4. **Commit changes**: Use conventional commit messages
5. **Push to branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Development Guidelines

- Follow TypeScript/Python best practices
- Write tests for new features
- Update documentation
- Use conventional commit messages
- Ensure Docker builds pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `docs/` directory
- **Issues**: Create a GitHub issue
- **Discussions**: Use GitHub Discussions

## 🔗 Related Documentation

- [Frontend Documentation](frontend/README.md)
- [Backend Documentation](backend/README.md)
- [Analytics Documentation](backend/python_analytics/README.md)
- [Deployment Guide](docs/DEPLOYMENT.md)
- [Contributing Guide](docs/CONTRIBUTING.md)