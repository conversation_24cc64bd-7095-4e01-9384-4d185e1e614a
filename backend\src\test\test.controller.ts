import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../modules/auth/jwt-auth.guard';

// Define interfaces for module information
interface ModuleEndpoint {
  path: string;
  method: string;
  description: string;
}

interface ModuleInfo {
  name: string;
  description: string;
  endpoints: ModuleEndpoint[];
  dependencies: string[];
}

interface ModuleError {
  error: string;
}

@Controller('test')
export class TestController {
  @Get()
  getTestInfo() {
    return {
      status: 'ok',
      message: 'Test endpoint is working',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('modules')
  getModules() {
    return {
      modules: [
        {
          name: 'auth',
          status: 'active',
          endpoints: ['/auth/login', '/auth/profile'],
        },
        {
          name: 'materials',
          status: 'active',
          endpoints: ['/materials', '/materials/:id', '/materials/upload'],
        },
        {
          name: 'users',
          status: 'active',
          endpoints: ['/users', '/users/:id'],
        },
        {
          name: 'units',
          status: 'active',
          endpoints: ['/units', '/units/:id'],
        },
        {
          name: 'progress',
          status: 'active',
          endpoints: ['/progress/:unitId', '/progress/user/:userId'],
        },
        {
          name: 'quiz',
          status: 'active',
          endpoints: [
            '/quiz/unit/:unitId',
            '/quiz/submit',
            '/quiz/results/:userId/:unitId',
          ],
        },
        {
          name: 'notifications',
          status: 'active',
          endpoints: ['/notifications', '/notifications/:id'],
        },
      ],
    };
  }

  @Get('module/:name')
  getModuleInfo(@Param('name') name: string): ModuleInfo | ModuleError {
    const modules: Record<string, ModuleInfo> = {
      auth: {
        name: 'auth',
        description: 'Handles user authentication and authorization',
        endpoints: [
          {
            path: '/auth/login',
            method: 'POST',
            description: 'Authenticate user',
          },
          {
            path: '/auth/profile',
            method: 'GET',
            description: 'Get user profile',
          },
        ],
        dependencies: ['users'],
      },
      materials: {
        name: 'materials',
        description: 'Manages study materials',
        endpoints: [
          {
            path: '/materials',
            method: 'GET',
            description: 'Get all materials',
          },
          {
            path: '/materials/:id',
            method: 'GET',
            description: 'Get material by ID',
          },
          {
            path: '/materials',
            method: 'POST',
            description: 'Create new material',
          },
          {
            path: '/materials/:id',
            method: 'DELETE',
            description: 'Delete material',
          },
          {
            path: '/materials/upload',
            method: 'POST',
            description: 'Upload material file',
          },
        ],
        dependencies: ['units'],
      },
      users: {
        name: 'users',
        description: 'Manages user accounts',
        endpoints: [
          { path: '/users', method: 'GET', description: 'Get all users' },
          { path: '/users', method: 'POST', description: 'Create new user' },
        ],
        dependencies: [],
      },
      units: {
        name: 'units',
        description: 'Manages learning units within materials',
        endpoints: [
          { path: '/units', method: 'GET', description: 'Get all units' },
          { path: '/units/:id', method: 'GET', description: 'Get unit by ID' },
          { path: '/units', method: 'POST', description: 'Create new unit' },
        ],
        dependencies: ['materials', 'quiz'],
      },
      progress: {
        name: 'progress',
        description: 'Tracks user progress through units',
        endpoints: [
          {
            path: '/progress/:unitId',
            method: 'POST',
            description: 'Update unit progress',
          },
          {
            path: '/progress/user/:userId',
            method: 'GET',
            description: 'Get user progress',
          },
        ],
        dependencies: ['users', 'units'],
      },
      quiz: {
        name: 'quiz',
        description: 'Manages quizzes for units',
        endpoints: [
          {
            path: '/quiz/unit/:unitId',
            method: 'GET',
            description: 'Get quiz for unit',
          },
          {
            path: '/quiz/submit',
            method: 'POST',
            description: 'Submit quiz answers',
          },
          {
            path: '/quiz/results/:userId/:unitId',
            method: 'GET',
            description: 'Get quiz results',
          },
        ],
        dependencies: ['units', 'progress'],
      },
      notifications: {
        name: 'notifications',
        description: 'Manages user notifications',
        endpoints: [
          {
            path: '/notifications',
            method: 'GET',
            description: 'Get all notifications',
          },
          {
            path: '/notifications',
            method: 'POST',
            description: 'Create new notification',
          },
        ],
        dependencies: ['users'],
      },
    };

    return modules[name] || { error: 'Module not found' };
  }

  @UseGuards(JwtAuthGuard)
  @Get('auth-check')
  checkAuth() {
    return {
      authenticated: true,
      timestamp: new Date().toISOString(),
    };
  }

  @Post('echo')
  echo(@Body() body: any) {
    return {
      received: body,
      timestamp: new Date().toISOString(),
    };
  }
}
