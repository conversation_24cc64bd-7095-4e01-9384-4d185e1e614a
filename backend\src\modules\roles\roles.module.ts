import { Module, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from '../../entities/role.entity';
import { Permission } from '../../entities/permission.entity';
import { RolesController } from './controllers/roles.controller';
import { RoleInitializationService } from './services/role-initialization.service';

@Module({
  imports: [TypeOrmModule.forFeature([Role, Permission])],
  controllers: [RolesController],
  providers: [RoleInitializationService],
  exports: [RoleInitializationService],
})
export class RolesModule implements OnModuleInit {
  constructor(
    private readonly roleInitializationService: RoleInitializationService,
  ) {}

  async onModuleInit() {
    await this.roleInitializationService.initializeDefaultRoles();
  }
}
