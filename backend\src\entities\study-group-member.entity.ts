import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { StudyGroup } from './study-group.entity';
import { User } from './user.entity';

export enum MemberRole {
  OWNER = 'owner',
  MODERATOR = 'moderator',
  MEMBER = 'member',
}

export enum MemberStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
  PENDING = 'pending',
}

@Entity('study_group_members')
@Unique(['study_group_id', 'user_id'])
export class StudyGroupMember {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: MemberRole,
    default: MemberRole.MEMBER,
  })
  role: MemberRole;

  @Column({
    type: 'enum',
    enum: MemberStatus,
    default: MemberStatus.ACTIVE,
  })
  status: MemberStatus;

  @Column({ type: 'timestamp' })
  joined_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  last_active: Date;

  @Column({ type: 'int', default: 0 })
  contribution_score: number;

  @Column({ type: 'int', default: 0 })
  sessions_attended: number;

  @Column({ type: 'int', default: 0 })
  sessions_missed: number;

  @Column({ type: 'jsonb', nullable: true })
  preferences: {
    notifications_enabled?: boolean;
    reminder_frequency?: string;
    preferred_study_times?: string[];
    study_style?: string;
    expertise_areas?: string[];
    learning_goals?: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  statistics: {
    messages_sent: number;
    resources_shared: number;
    questions_asked: number;
    questions_answered: number;
    study_hours_logged: number;
    peer_ratings: {
      helpfulness: number;
      knowledge: number;
      collaboration: number;
      reliability: number;
    };
  };

  @Column({ type: 'text', nullable: true })
  bio: string;

  @Column({ type: 'simple-array', nullable: true })
  strengths: string[];

  @Column({ type: 'simple-array', nullable: true })
  areas_for_improvement: string[];

  @Column({ type: 'uuid' })
  study_group_id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @ManyToOne(() => StudyGroup, (studyGroup) => studyGroup.members, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'study_group_id' })
  study_group: StudyGroup;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
