# Root .dockerignore for monorepo builds

# Version control
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Node modules and dependencies
node_modules/
**/node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Build output
dist/
**/dist/
build/
**/build/

# Next.js build output
.next
**/next
out

# Coverage directory
coverage/
*.lcov
.nyc_output

# Python
__pycache__/
**/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python virtual environments
venv/
**/venv/
env/
ENV/
env.bak/
venv.bak/

# Test files and coverage
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environment variables
.env
.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local

# Archives
*.zip
*.tar
*.tar.gz
*.rar

# Docker files (exclude from build context)
Dockerfile*
docker-compose*.yml
.dockerignore

# Temporary folders
tmp/
temp/