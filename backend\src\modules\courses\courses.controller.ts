import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { Request as ExpressRequest } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import {
  CoursesService,
  CreateCourseDto,
  UpdateCourseDto,
  CourseSearchFilters,
} from './courses.service';
import { CourseDifficulty, CourseStatus } from '../../entities/course.entity';
import { EnrollmentStatus } from '../../entities/course-enrollment.entity';

@ApiTags('courses')
@Controller('courses')
export class CoursesController {
  constructor(private readonly coursesService: CoursesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new course' })
  @ApiResponse({ status: 201, description: 'Course created successfully' })
  async create(
    @Request() req: ExpressRequest & { user: any },
    @Body() createCourseDto: CreateCourseDto,
  ) {
    return await this.coursesService.create(createCourseDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all courses with filtering and pagination' })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in title, description, and tags',
  })
  @ApiQuery({
    name: 'category_id',
    required: false,
    description: 'Filter by category ID',
  })
  @ApiQuery({ name: 'difficulty', required: false, enum: CourseDifficulty })
  @ApiQuery({ name: 'status', required: false, enum: CourseStatus })
  @ApiQuery({ name: 'is_featured', required: false, type: Boolean })
  @ApiQuery({
    name: 'instructor_id',
    required: false,
    description: 'Filter by instructor ID',
  })
  @ApiQuery({ name: 'min_rating', required: false, type: Number })
  @ApiQuery({ name: 'max_price', required: false, type: Number })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20)',
  })
  @ApiQuery({
    name: 'sort_by',
    required: false,
    description: 'Sort field (default: created_at)',
  })
  @ApiQuery({
    name: 'sort_order',
    required: false,
    description: 'Sort order: ASC or DESC (default: DESC)',
  })
  async findAll(@Query() filters: CourseSearchFilters) {
    return await this.coursesService.findAll(filters);
  }

  @Get('my-courses')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user enrolled courses' })
  @ApiQuery({ name: 'status', required: false, enum: EnrollmentStatus })
  async getMyCourses(
    @Request() req: ExpressRequest & { user: any },
    @Query('status') status?: EnrollmentStatus,
  ) {
    return await this.coursesService.getEnrollments(req.user.id, status);
  }

  @Get('featured')
  @ApiOperation({ summary: 'Get featured courses' })
  async getFeatured() {
    return await this.coursesService.findAll({ is_featured: true, limit: 10 });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get course by ID' })
  @ApiResponse({ status: 200, description: 'Course found' })
  @ApiResponse({ status: 404, description: 'Course not found' })
  async findOne(
    @Param('id') id: string,
    @Request() req: ExpressRequest & { user: any },
  ) {
    const userId = req.user?.id;
    return await this.coursesService.findOne(id, userId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update course' })
  @ApiResponse({ status: 200, description: 'Course updated successfully' })
  @ApiResponse({
    status: 403,
    description: 'Only instructor can update course',
  })
  @ApiResponse({ status: 404, description: 'Course not found' })
  async update(
    @Param('id') id: string,
    @Body() updateCourseDto: UpdateCourseDto,
    @Request() req: ExpressRequest & { user: any },
  ) {
    return await this.coursesService.update(id, updateCourseDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete course' })
  @ApiResponse({ status: 204, description: 'Course deleted successfully' })
  @ApiResponse({
    status: 403,
    description: 'Only instructor can delete course',
  })
  @ApiResponse({ status: 404, description: 'Course not found' })
  async remove(
    @Param('id') id: string,
    @Request() req: ExpressRequest & { user: any },
  ) {
    await this.coursesService.remove(id, req.user.id);
  }

  @Post(':id/enroll')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enroll in a course' })
  @ApiResponse({ status: 201, description: 'Successfully enrolled in course' })
  @ApiResponse({
    status: 400,
    description: 'Already enrolled or course not available',
  })
  @ApiResponse({ status: 404, description: 'Course not found' })
  async enroll(
    @Param('id') courseId: string,
    @Request() req: ExpressRequest & { user: any },
  ) {
    return await this.coursesService.enroll(courseId, req.user.id);
  }

  @Delete(':id/enroll')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Unenroll from a course' })
  @ApiResponse({
    status: 204,
    description: 'Successfully unenrolled from course',
  })
  @ApiResponse({ status: 404, description: 'Enrollment not found' })
  async unenroll(
    @Param('id') courseId: string,
    @Request() req: ExpressRequest & { user: any },
  ) {
    await this.coursesService.unenroll(courseId, req.user.id);
  }

  @Post(':id/units/:unitId/complete')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Mark unit as completed and update course progress',
  })
  @ApiResponse({ status: 204, description: 'Unit marked as completed' })
  @ApiResponse({ status: 404, description: 'Course or enrollment not found' })
  async completeUnit(
    @Param('id') courseId: string,
    @Param('unitId') unitId: string,
    @Request() req: ExpressRequest & { user: any },
  ) {
    await this.coursesService.updateProgress(courseId, req.user.id, unitId);
  }
}
