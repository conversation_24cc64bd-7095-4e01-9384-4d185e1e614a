import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { GroupDiscussion } from './group-discussion.entity';
import { User } from './user.entity';

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  LINK = 'link',
  POLL_VOTE = 'poll_vote',
  SYSTEM = 'system',
}

@Entity('discussion_messages')
export class DiscussionMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: MessageType,
    default: MessageType.TEXT,
  })
  type: MessageType;

  @Column({ type: 'jsonb', nullable: true })
  attachments: {
    id: string;
    name: string;
    type: 'image' | 'document' | 'video' | 'audio' | 'link';
    url: string;
    size?: number;
    thumbnail_url?: string;
  }[];

  @Column({ type: 'int', default: 0 })
  like_count: number;

  @Column({ type: 'simple-array', nullable: true })
  liked_by: string[];

  @Column({ type: 'boolean', default: false })
  is_edited: boolean;

  @Column({ type: 'timestamp', nullable: true })
  edited_at: Date;

  @Column({ type: 'boolean', default: false })
  is_deleted: boolean;

  @Column({ type: 'timestamp', nullable: true })
  deleted_at: Date;

  @Column({ type: 'uuid', nullable: true })
  reply_to_id: string;

  @Column({ type: 'int', default: 0 })
  reply_count: number;

  @Column({ type: 'jsonb', nullable: true })
  mentions: {
    user_id: string;
    username: string;
    start_index: number;
    end_index: number;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  reactions: {
    emoji: string;
    count: number;
    users: string[];
  }[];

  @Column({ type: 'uuid' })
  discussion_id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @ManyToOne(() => GroupDiscussion, (discussion) => discussion.messages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'discussion_id' })
  discussion: GroupDiscussion;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => DiscussionMessage, { nullable: true })
  @JoinColumn({ name: 'reply_to_id' })
  reply_to: DiscussionMessage;

  @OneToMany(() => DiscussionMessage, (message) => message.reply_to)
  replies: DiscussionMessage[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
