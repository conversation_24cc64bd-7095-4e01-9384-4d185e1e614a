import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import { RolesGuard } from '../../../common/guards/roles.guards';
import { Roles } from '../../../common/decorators/roles.decorator';
import { RoleInitializationService } from '../services/role-initialization.service';
import { Role } from '../../../entities/role.entity';
import { Permission } from '../../../entities/permission.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Controller('roles')
@UseGuards(RolesGuard)
export class RolesController {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    private roleInitializationService: RoleInitializationService,
  ) {}

  @Get()
  @Roles('admin')
  async getAllRoles() {
    return this.roleRepository.find({
      relations: ['permissions'],
      order: {
        hierarchy_level: 'ASC',
      },
    });
  }

  @Get(':id')
  @Roles('admin')
  async getRole(@Param('id') id: string) {
    return this.roleRepository.findOne({
      where: { id },
      relations: ['permissions'],
    });
  }

  @Post()
  @Roles('admin')
  async createRole(@Body() roleData: Partial<Role>) {
    const role = this.roleRepository.create(roleData);
    return this.roleRepository.save(role);
  }

  @Put(':id')
  @Roles('admin')
  async updateRole(@Param('id') id: string, @Body() roleData: Partial<Role>) {
    await this.roleRepository.update(id, roleData);
    return this.roleRepository.findOne({
      where: { id },
      relations: ['permissions'],
    });
  }

  @Delete(':id')
  @Roles('admin')
  async deleteRole(@Param('id') id: string) {
    // Instead of deleting, deactivate the role
    await this.roleRepository.update(id, { is_active: false });
    return { message: 'Role deactivated successfully' };
  }

  @Post('initialize')
  @Roles('admin')
  async initializeRoles() {
    await this.roleInitializationService.initializeDefaultRoles();
    return { message: 'Default roles initialized successfully' };
  }

  @Get('permissions')
  @Roles('admin')
  async getAllPermissions() {
    return this.permissionRepository.find({
      order: {
        category: 'ASC',
        name: 'ASC',
      },
    });
  }

  @Post('permissions')
  @Roles('admin')
  async createPermission(@Body() permissionData: Partial<Permission>) {
    const permission = this.permissionRepository.create(permissionData);
    return this.permissionRepository.save(permission);
  }

  @Put('permissions/:id')
  @Roles('admin')
  async updatePermission(
    @Param('id') id: string,
    @Body() permissionData: Partial<Permission>,
  ) {
    await this.permissionRepository.update(id, permissionData);
    return this.permissionRepository.findOne({
      where: { id },
    });
  }

  @Delete('permissions/:id')
  @Roles('admin')
  async deletePermission(@Param('id') id: string) {
    // Instead of deleting, deactivate the permission
    await this.permissionRepository.update(id, { is_active: false });
    return { message: 'Permission deactivated successfully' };
  }
}
