import { useState, useEffect } from 'react';
import { Shield, Check, X, Save, RefreshCw } from 'lucide-react';
import { Role } from '../roles';
import api from '@/services/api';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

const UserPermissions: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch roles from the backend
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await api.get('/roles');
        setRoles(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch roles');
        setLoading(false);
      }
    };

    fetchRoles();
  }, []);

  const handleRoleSelect = (roleId: string) => {
    setSelectedRole(roleId);
    setIsEditing(false);
  };

  const handleSave = async () => {
    if (!selectedRole) return;

    try {
      const role = roles.find(r => r.id === selectedRole);
      if (!role) return;

      await api.put(`/roles/${selectedRole}`, role);
      setIsEditing(false);
    } catch (err) {
      setError('Failed to save role changes');
    }
  };

  const handleReset = () => {
    setIsEditing(false);
  };

  if (loading) {
    return <div>Loading roles...</div>;
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Role Permissions</h2>
        <div className="flex space-x-2">
          {isEditing && (
            <>
              <button
                onClick={handleSave}
                className="flex items-center px-4 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </button>
              <button
                onClick={handleReset}
                className="flex items-center px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reset
              </button>
            </>
          )}
        </div>
      </div>

      {/* Role Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">Select Role</label>
        <select
          value={selectedRole}
          onChange={e => handleRoleSelect(e.target.value)}
          className="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Choose a role</option>
          {roles.map(role => (
            <option key={role.id} value={role.id}>
              {role.name}
            </option>
          ))}
        </select>
      </div>

      {/* Permissions Grid */}
      {selectedRole && (
        <div className="space-y-6">
          {Object.entries(groupedPermissions).map(([category, permissions]) => (
            <div key={category} className="border rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 capitalize">
                {category} Permissions
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {permissions.map(permission => (
                  <div
                    key={permission.id}
                    className="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50"
                  >
                    <div className="flex-shrink-0">
                      <Shield className="h-5 w-5 text-gray-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900">{permission.name}</p>
                        <button
                          onClick={() => handlePermissionToggle(permission.id)}
                          className={`p-1 rounded-full ${
                            permissions[permission.id]
                              ? 'bg-green-100 text-green-600'
                              : 'bg-gray-100 text-gray-400'
                          }`}
                        >
                          {permissions[permission.id] ? (
                            <Check className="h-4 w-4" />
                          ) : (
                            <X className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">{permission.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {!selectedRole && (
        <div className="text-center py-12">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Role Selected</h3>
          <p className="text-gray-500">Please select a role to view and manage its permissions</p>
        </div>
      )}
    </div>
  );
};

export default UserPermissions;
