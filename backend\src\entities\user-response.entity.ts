import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { QuizQuestion } from './quiz-question.entity';

@Entity('user_responses')
export class UserResponse {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, (user: User) => user.quiz_responses)
  user: User;

  @ManyToOne(() => QuizQuestion)
  question: QuizQuestion;

  @Column('text')
  selected_answer: string;

  @Column()
  is_correct: boolean;

  @CreateDateColumn()
  created_at: Date;
}
