// src/app/MedTrackLayout.tsx
'use client';

import React, { useState, useEffect, ReactNode, Suspense } from 'react';
import { Stethoscope } from 'lucide-react';
import { useTheme } from './providers';
import '../app/globals.css';
import { SyncStatusBanner } from '@/components/SyncStatusBanner';
import { Toaster } from 'react-hot-toast';
import { logger } from '@/lib/logger';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { AppHeader } from '@/components/layout/AppHeader';
import MobileNav from '@/components/layout/MobileNav';
import MainNav from '@/components/layout/MainNav';

// Types
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  isAuthenticated: boolean;
}

interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  unread: boolean;
}

interface LayoutProps {
  children: ReactNode;
}

const MedTrackLayout: React.FC<LayoutProps> = ({ children }) => {
  // Theme hook
  const { theme, setTheme } = useTheme();

  // State management
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);
  const [userMenuOpen, setUserMenuOpen] = useState<boolean>(false);
  const [notificationsOpen, setNotificationsOpen] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [expandedSections, setExpandedSections] = useState<string[]>(['learning']);

  // Mock user data
  const [user] = useState<User>({
    id: '1',
    name: 'Dr. Sarah Johnson',
    email: '<EMAIL>',
    role: 'Medical Student',
    isAuthenticated: true,
  });

  // Mock notifications
  const notifications: Notification[] = [
    {
      id: '1',
      title: 'New Course Available',
      message: 'Advanced Cardiology module is now live',
      time: '5 min ago',
      unread: true,
    },
    {
      id: '2',
      title: 'Assignment Due',
      message: 'Anatomy quiz due tomorrow',
      time: '2 hours ago',
      unread: true,
    },
    {
      id: '3',
      title: 'Study Group Meeting',
      message: 'Cardiology study group starts in 1 hour',
      time: '1 day ago',
      unread: false,
    },
  ];

  // Theme toggle
  const toggleTheme = (): void => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  // Sidebar section toggle
  const toggleSection = (sectionId: string): void => {
    setExpandedSections((prev) =>
      prev.includes(sectionId)
        ? prev.filter((id) => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  // Handle outside clicks for menus
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.user-menu') && !target.closest('.user-menu-button')) {
        setUserMenuOpen(false);
      }
      if (!target.closest('.notifications-menu') && !target.closest('.notifications-button')) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        {/* Responsive Navigation */}
        <MobileNav />
        <MainNav />
        {/* Sidebar */}
        <AppSidebar
          sidebarOpen={sidebarOpen}
          sidebarCollapsed={sidebarCollapsed}
          setSidebarOpen={setSidebarOpen}
          theme={theme}
          user={user}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <AppHeader
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
            sidebarCollapsed={sidebarCollapsed}
            setSidebarCollapsed={setSidebarCollapsed}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            theme={theme}
            toggleTheme={toggleTheme}
            notificationsOpen={notificationsOpen}
            setNotificationsOpen={setNotificationsOpen}
            userMenuOpen={userMenuOpen}
            setUserMenuOpen={setUserMenuOpen}
            user={user}
            notifications={notifications}
          />

          {/* Main Content */}
          <main className="flex-1 overflow-auto">
            <div className="p-4 sm:p-6 lg:p-8">
              <Suspense fallback={<LoadingSpinner />}>
                {children}
              </Suspense>
            </div>
          </main>

          {/* Footer */}
          <footer className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex flex-col sm:flex-row justify-between items-center">
              <div className="flex items-center space-x-4">
                <Stethoscope className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium">MedTrack Hub</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  © 2025 All rights reserved
                </span>
              </div>
              <div className="flex items-center space-x-4 mt-2 sm:mt-0">
                <a href="#" className="text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                  Privacy Policy
                </a>
                <a href="#" className="text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                  Terms of Service
                </a>
                <a href="#" className="text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                  Support
                </a>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default MedTrackLayout;