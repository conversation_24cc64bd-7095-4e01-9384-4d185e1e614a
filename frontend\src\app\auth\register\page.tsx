'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Head from 'next/head';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>aEnvelope,
  <PERSON>a<PERSON>ock,
  <PERSON>a<PERSON><PERSON>ner,
  FaUserTag,
  FaCheck,
  FaTimes,
  FaInfoCircle,
  FaEye,
  FaEyeSlash,
} from 'react-icons/fa';
import { useAuth } from '@/contexts/AuthContext';
import dynamic from 'next/dynamic';
import { Suspense } from 'react';
import { AuthForm } from '@/components/auth/AuthForm';
import { FormField } from '@/components/auth/FormField';
import { Notification } from '@/components/auth/Notification';

interface FormData {
  email: string;
  username: string;
  name: string;
  password: string;
  confirmPassword: string;
  role: string;
  acceptTerms: boolean;
}

interface FormErrors {
  email?: string;
  username?: string;
  name?: string;
  password?: string;
  confirmPassword?: string;
  acceptTerms?: string;
}

// Helper function to generate a unique username
const generateUniqueUsername = (baseUsername: string): string => {
  // Add a random number between 100-999 to make it more likely to be unique
  return `${baseUsername}${Math.floor(Math.random() * 900) + 100}`;
};

// Helper function to generate a name from email
const generateNameFromEmail = (email: string): string => {
  // Get the part before @ and replace dots/underscores with spaces
  const namePart = email.split('@')[0].replace(/[._]/g, ' ');

  // Capitalize each word
  return namePart
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Error fallback component
function ErrorFallback({
  error,
  resetErrorBoundary,
}: {
  error: Error;
  resetErrorBoundary: () => void;
}) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FaTimes className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Something went wrong</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error.message}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={resetErrorBoundary}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Try again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// API configuration
const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002',
  timeout: 10000, // 10 seconds
  retryAttempts: 3,
};

// API client with retry logic
async function apiClient(endpoint: string, options: RequestInit = {}) {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt < API_CONFIG.retryAttempts; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);

      const response = await fetch(`${API_CONFIG.baseUrl}${endpoint}`, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'API request failed');
      }

      return await response.json();
    } catch (error) {
      lastError = error as Error;
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timed out');
      }
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }

  throw lastError || new Error('API request failed after multiple attempts');
}

// Security headers configuration
const securityHeaders = {
  'Content-Security-Policy':
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;",
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
};

// Analytics configuration
const ANALYTICS_CONFIG = {
  enabled: process.env.NODE_ENV === 'production',
  endpoint: process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT,
  appId: process.env.NEXT_PUBLIC_APP_ID,
};

// Performance monitoring
const performanceMetrics = {
  pageLoad: 0,
  formInteraction: 0,
  apiCalls: 0,
  errors: 0,
};

// Track performance metrics
const trackPerformance = (metric: keyof typeof performanceMetrics) => {
  if (ANALYTICS_CONFIG.enabled) {
    performanceMetrics[metric]++;

    // Send metrics to analytics endpoint
    if (ANALYTICS_CONFIG.endpoint) {
      fetch(ANALYTICS_CONFIG.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appId: ANALYTICS_CONFIG.appId,
          metric,
          value: performanceMetrics[metric],
          timestamp: new Date().toISOString(),
        }),
      }).catch(console.error);
    }
  }
};

// Track user interactions
const trackUserInteraction = (action: string, details?: Record<string, any>) => {
  if (ANALYTICS_CONFIG.enabled) {
    // Send interaction data to analytics endpoint
    if (ANALYTICS_CONFIG.endpoint) {
      fetch(ANALYTICS_CONFIG.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appId: ANALYTICS_CONFIG.appId,
          action,
          details,
          timestamp: new Date().toISOString(),
        }),
      }).catch(console.error);
    }
  }
};

const AuthLayout = dynamic(
  () =>
    import('@/components/auth/AuthLayout').then(
      mod => mod.default || (() => <div>Layout unavailable</div>)
    ),
  {
    ssr: false,
    loading: () => <div>Loading layout...</div>,
  }
);

export default function RegisterPage() {
  const router = useRouter();
  const { register } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (!formData.acceptTerms) {
      setError('You must accept the terms and conditions');
      return;
    }

    setIsLoading(true);

    try {
      await register(formData);
      router.push('/dashboard');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during registration');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  return (
    <AuthLayout title="Create Account" description="Join our medical education platform">
      {error && <Notification type="error" message={error} onClose={() => setError(null)} />}

      <AuthForm
        title="Sign Up"
        subtitle="Create your account to get started"
        onSubmit={handleSubmit}
        isLoading={isLoading}
        submitText="Create Account"
        footer={
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            Already have an account?{' '}
            <Link href="/auth/login" className="text-blue-600 hover:text-blue-700 font-medium">
              Sign in
            </Link>
          </p>
        }
      >
        <div className="space-y-4">
          <FormField
            label="Full Name"
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            placeholder="Enter your full name"
          />

          <FormField
            label="Email Address"
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            placeholder="Enter your email"
          />

          <FormField
            label="Password"
            type="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            required
            placeholder="Create a password"
            helperText="Must be at least 8 characters with uppercase, lowercase, number, and special character"
          />

          <FormField
            label="Confirm Password"
            type="password"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            required
            placeholder="Confirm your password"
          />

          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                type="checkbox"
                id="acceptTerms"
                name="acceptTerms"
                checked={formData.acceptTerms}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="acceptTerms" className="text-gray-600 dark:text-gray-300">
                I agree to the{' '}
                <Link href="/terms" className="text-blue-600 hover:text-blue-700 font-medium">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-blue-600 hover:text-blue-700 font-medium">
                  Privacy Policy
                </Link>
              </label>
            </div>
          </div>
        </div>
      </AuthForm>
    </AuthLayout>
  );
}
