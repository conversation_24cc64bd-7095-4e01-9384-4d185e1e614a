#!/usr/bin/env python3
"""
Python Analytics health check script
"""

import os
import sys
import requests
import json
from urllib.parse import urljoin

PORT = os.getenv('PORT', '5000')
TIMEOUT = 5

def health_check():
    """Perform health check on the analytics service"""
    try:
        base_url = f"http://localhost:{PORT}"
        health_url = urljoin(base_url, '/health')
        
        response = requests.get(health_url, timeout=TIMEOUT)
        
        if response.status_code == 200:
            try:
                health_data = response.json()
                if health_data.get('status') == 'ok':
                    print("Analytics service is healthy")
                    return True
                else:
                    print(f"Analytics health check failed: {health_data.get('message', 'Unknown error')}")
                    return False
            except json.JSONDecodeError:
                print("Analytics health check failed: Invalid JSON response")
                return False
        else:
            print(f"Analytics health check failed with status: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("Analytics health check timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("Analytics health check failed: Connection error")
        return False
    except Exception as e:
        print(f"Analytics health check failed: {str(e)}")
        return False

if __name__ == "__main__":
    if health_check():
        sys.exit(0)
    else:
        sys.exit(1)
