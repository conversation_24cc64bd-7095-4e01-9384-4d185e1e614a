/**
 * Materials Module Tests
 *
 * This file contains tests for the materials module.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import materialService from '../services/materialService';
import mockApiService from '../services/mockApiService';
import api from '../services/api';

// Mock the API service
vi.mock('../services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    delete: vi.fn(),
  },
}));

describe('Materials Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Get Materials', () => {
    it('should fetch materials from API when available', async () => {
      const mockMaterials = [
        { id: '1', title: 'Test Material 1' },
        { id: '2', title: 'Test Material 2' },
      ];

      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockMaterials });

      const materials = await materialService.getMaterials();

      expect(api.get).toHaveBeenCalledWith('/materials');
      expect(materials).toEqual(mockMaterials);
    });

    it('should fall back to mock API when real API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));

      // Spy on mock API
      const mockGetMaterials = vi.spyOn(mockApiService, 'getMaterials');
      mockGetMaterials.mockResolvedValueOnce([
        { id: '1', title: 'Mock Material 1' },
        { id: '2', title: 'Mock Material 2' },
      ]);

      const materials = await materialService.getMaterials();

      expect(api.get).toHaveBeenCalledWith('/materials');
      expect(mockGetMaterials).toHaveBeenCalled();
      expect(materials.length).toBeGreaterThan(0);
    });
  });

  describe('Get Material by ID', () => {
    it('should fetch a specific material from API when available', async () => {
      const mockMaterial = { id: '1', title: 'Test Material 1' };

      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockMaterial });

      const material = await materialService.getMaterialById('1');

      expect(api.get).toHaveBeenCalledWith('/materials/1');
      expect(material).toEqual(mockMaterial);
    });

    it('should fall back to mock API when real API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));

      // Spy on mock API
      const mockGetMaterialById = vi.spyOn(mockApiService, 'getMaterialById');
      mockGetMaterialById.mockResolvedValueOnce({ id: '1', title: 'Mock Material 1' });

      const material = await materialService.getMaterialById('1');

      expect(api.get).toHaveBeenCalledWith('/materials/1');
      expect(mockGetMaterialById).toHaveBeenCalledWith('1');
      expect(material.id).toBe('1');
    });
  });

  describe('Upload Material', () => {
    it('should upload material to API when available', async () => {
      const mockResponse = { id: '3', title: 'New Material' };
      const formData = new FormData();
      formData.append('title', 'New Material');
      formData.append('description', 'Test description');
      formData.append('file', new File(['test'], 'test.pdf', { type: 'application/pdf' }));

      // Mock successful API response
      (api.post as any).mockResolvedValueOnce({ data: mockResponse });

      const result = await materialService.uploadMaterial(formData);

      expect(api.post).toHaveBeenCalledWith('/materials/upload', formData, expect.any(Object));
      expect(result).toEqual(mockResponse);
    });

    it('should fall back to mock API when real API fails', async () => {
      const formData = new FormData();
      formData.append('title', 'New Material');
      formData.append('description', 'Test description');
      formData.append('file', new File(['test'], 'test.pdf', { type: 'application/pdf' }));

      // Mock API failure
      (api.post as any).mockRejectedValueOnce(new Error('API error'));

      // Spy on mock API
      const mockCreateMaterial = vi.spyOn(mockApiService, 'createMaterial');
      mockCreateMaterial.mockResolvedValueOnce({ id: '3', title: 'New Material' });

      const result = await materialService.uploadMaterial(formData);

      expect(api.post).toHaveBeenCalled();
      expect(mockCreateMaterial).toHaveBeenCalled();
      expect(result.title).toBe('New Material');
    });
  });

  describe('Delete Material', () => {
    it('should delete material from API when available', async () => {
      // Mock successful API response
      (api.delete as any).mockResolvedValueOnce({});

      await materialService.deleteMaterial('1');

      expect(api.delete).toHaveBeenCalledWith('/materials/1');
    });

    it('should fall back to mock API when real API fails', async () => {
      // Mock API failure
      (api.delete as any).mockRejectedValueOnce(new Error('API error'));

      // Spy on mock API
      const mockDeleteMaterial = vi.spyOn(mockApiService, 'deleteMaterial');
      mockDeleteMaterial.mockResolvedValueOnce({ success: true });

      await materialService.deleteMaterial('1');

      expect(api.delete).toHaveBeenCalledWith('/materials/1');
      expect(mockDeleteMaterial).toHaveBeenCalledWith('1');
    });
  });
});
