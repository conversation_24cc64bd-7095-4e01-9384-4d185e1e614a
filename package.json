{"name": "medtrack-hub", "version": "1.0.0", "description": "A comprehensive medical education and tracking platform", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:analytics\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run start:dev", "dev:analytics": "cd backend/python_analytics && python -m uvicorn main:app --reload --host 0.0.0.0 --port 5000", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\" \"npm run start:analytics\"", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && npm run start:prod", "start:analytics": "cd backend/python_analytics && python -m uvicorn main:app --host 0.0.0.0 --port 5000", "test": "npm run test:frontend && npm run test:backend && npm run test:analytics", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm run test:all", "test:analytics": "cd backend/python_analytics && python -m pytest", "test:coverage": "npm run test:frontend -- --coverage && npm run test:backend -- --coverage", "lint": "npm run lint:frontend && npm run lint:backend && npm run lint:analytics", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:analytics": "cd backend/python_analytics && python -m flake8 .", "format": "npm run format:frontend && npm run format:backend && npm run format:analytics", "format:frontend": "cd frontend && npm run format", "format:backend": "cd backend && npm run format", "format:analytics": "cd backend/python_analytics && python -m black .", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:prod": "docker-compose up -d --build", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "health-check": "bash scripts/health-check.sh", "setup": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "npm run clean:frontend && npm run clean:backend && npm run clean:analytics", "clean:frontend": "cd frontend && rm -rf .next node_modules", "clean:backend": "cd backend && rm -rf dist node_modules", "clean:analytics": "cd backend/python_analytics && rm -rf __pycache__ .pytest_cache", "version:patch": "npm version patch && npm run version:sync", "version:minor": "npm version minor && npm run version:sync", "version:major": "npm version major && npm run version:sync", "version:sync": "node scripts/sync-versions.js", "version:check": "node scripts/check-versions.js", "test:auth": "node test-frontend-auth.js", "test:e2e:auth": "cd backend && node test-e2e-auth.js"}, "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"dotenv": "^17.2.0", "pg": "^8.16.3"}, "engines": {"node": ">=20.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/medical.git"}, "keywords": ["medical", "education", "healthcare", "nextjs", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "typescript", "python"], "author": "Your Name <<EMAIL>>", "license": "MIT"}