// store/useProgressStore.ts
import { create } from 'zustand';
import { syncService } from '../services/syncService';

interface Progress {
  id: string;
  userId: string;
  courseId: string;
  unitId: string;
  status: 'not_started' | 'in_progress' | 'completed';
  score?: number;
  lastAccessed: Date;
}

interface ProgressState {
  progress: Progress[];
  isLoading: boolean;
  error: string | null;
  fetchUserProgress: () => Promise<void>;
  updateProgress: (progressData: Partial<Progress>) => Promise<void>;
  getProgressForCourse: (courseId: string) => Progress[];
  getCompletionPercentage: (courseId: string) => number;
}

export const useProgressStore = create<ProgressState>()((set, get) => ({
  progress: [],
  isLoading: false,
  error: null,

  fetchUserProgress: async () => {
    try {
      set({ isLoading: true, error: null });
      const userId = localStorage.getItem('userId'); // Get from auth store in real app
      if (!userId) throw new Error('User not authenticated');

      const progress = await syncService.getProgress(userId);
      set({ progress, isLoading: false });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch progress',
        isLoading: false,
      });
    }
  },

  updateProgress: async progressData => {
    try {
      set({ isLoading: true, error: null });
      const userId = localStorage.getItem('userId'); // Get from auth store in real app
      if (!userId) throw new Error('User not authenticated');

      const progressToSave = {
        id: progressData.id || crypto.randomUUID(),
        userId,
        ...progressData,
        lastAccessed: new Date(),
      };

      await syncService.saveProgress(progressToSave);

      const updatedProgress = get().progress.map(p =>
        p.id === progressToSave.id ? progressToSave : p
      );

      if (!updatedProgress.find(p => p.id === progressToSave.id)) {
        updatedProgress.push(progressToSave as Progress);
      }

      set({ progress: updatedProgress, isLoading: false });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to update progress',
        isLoading: false,
      });
    }
  },

  getProgressForCourse: courseId => {
    return get().progress.filter(p => p.courseId === courseId);
  },

  getCompletionPercentage: courseId => {
    const courseProgress = get().getProgressForCourse(courseId);
    if (courseProgress.length === 0) return 0;

    const completedUnits = courseProgress.filter(p => p.status === 'completed').length;
    return Math.round((completedUnits / courseProgress.length) * 100);
  },
}));
