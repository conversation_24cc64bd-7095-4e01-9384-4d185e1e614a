import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Course } from './course.entity';
import { Unit } from './unit.entity';
import { User } from './user.entity';
import { AssessmentAttempt } from './assessment-attempt.entity';
import { Question } from './question.entity';

export enum AssessmentType {
  QUIZ = 'quiz',
  UNIT_EXAM = 'unit_exam',
  MIDTERM = 'midterm',
  FINAL_EXAM = 'final_exam',
  PRACTICE_TEST = 'practice_test',
  ADAPTIVE_QUIZ = 'adaptive_quiz',
}

export enum AssessmentDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  MIXED = 'mixed',
}

export enum AssessmentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

@Entity('assessments')
export class Assessment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: AssessmentType,
    default: AssessmentType.QUIZ,
  })
  type: AssessmentType;

  @Column({
    type: 'enum',
    enum: AssessmentDifficulty,
    default: AssessmentDifficulty.MIXED,
  })
  difficulty: AssessmentDifficulty;

  @Column({
    type: 'enum',
    enum: AssessmentStatus,
    default: AssessmentStatus.DRAFT,
  })
  status: AssessmentStatus;

  @Column({ type: 'int', default: 10 })
  total_questions: number;

  @Column({ type: 'int', default: 30 })
  time_limit_minutes: number;

  @Column({ type: 'int', default: 1 })
  max_attempts: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 70 })
  passing_score: number;

  @Column({ type: 'boolean', default: false })
  randomize_questions: boolean;

  @Column({ type: 'boolean', default: false })
  randomize_options: boolean;

  @Column({ type: 'boolean', default: true })
  show_results_immediately: boolean;

  @Column({ type: 'boolean', default: false })
  show_correct_answers: boolean;

  @Column({ type: 'boolean', default: false })
  is_adaptive: boolean;

  @Column({ type: 'jsonb', nullable: true })
  adaptive_settings: {
    initial_difficulty?: AssessmentDifficulty;
    difficulty_adjustment_factor?: number;
    min_questions?: number;
    max_questions?: number;
    target_accuracy?: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  grading_settings: {
    partial_credit?: boolean;
    negative_marking?: boolean;
    negative_marking_factor?: number;
    bonus_points?: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  feedback_settings: {
    show_explanation?: boolean;
    show_references?: boolean;
    show_difficulty_level?: boolean;
    custom_feedback?: string;
  };

  @Column({ type: 'timestamp', nullable: true })
  available_from: Date;

  @Column({ type: 'timestamp', nullable: true })
  available_until: Date;

  @Column({ type: 'uuid', nullable: true })
  course_id: string;

  @Column({ type: 'uuid', nullable: true })
  unit_id: string;

  @Column({ type: 'uuid' })
  created_by: string;

  @ManyToOne(() => Course, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @ManyToOne(() => Unit, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'unit_id' })
  unit: Unit;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => Question, (question) => question.assessment)
  questions: Question[];

  @OneToMany(() => AssessmentAttempt, (attempt) => attempt.assessment)
  attempts: AssessmentAttempt[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
