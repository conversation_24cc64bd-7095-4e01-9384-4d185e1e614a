const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3002;

console.log('Starting simple test server...');

app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.post('/v1/auth/login', (req, res) => {
  console.log('Login request:', req.body);
  res.json({
    accessToken: 'test-token',
    refreshToken: 'test-refresh-token',
    user: {
      id: '1',
      email: req.body.email || req.body.username,
      firstName: 'Test',
      lastName: 'User',
      role: 'student',
      isEmailVerified: true
    }
  });
});

app.post('/v1/auth/register', (req, res) => {
  console.log('Register request:', req.body);
  res.status(201).json({
    accessToken: 'test-token',
    refreshToken: 'test-refresh-token',
    user: {
      id: '2',
      email: req.body.email,
      firstName: req.body.name?.split(' ')[0] || 'Test',
      lastName: req.body.name?.split(' ').slice(1).join(' ') || 'User',
      role: req.body.role || 'student',
      isEmailVerified: true
    }
  });
});

app.get('/v1/auth/protected', (req, res) => {
  console.log('Protected endpoint accessed');
  res.json({ message: 'Access granted', user: { id: '1', email: '<EMAIL>' } });
});

app.listen(PORT, () => {
  console.log(`🚀 Simple test server running on port ${PORT}`);
  console.log(`📊 Health: http://localhost:${PORT}/health`);
  console.log(`🔐 Auth: http://localhost:${PORT}/v1/auth/`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n👋 Server shutting down...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Server shutting down...');
  process.exit(0);
});
