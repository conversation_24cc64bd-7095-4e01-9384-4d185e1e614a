import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WeeklyDigestService } from './weekly-digest.service';
import { WeeklyDigestController } from './weekly-digest.controller';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';
import { CPDActivity } from '../../entities/cpd-tracking.entity';
import { WeeklyDigest } from '../../entities/weekly-digest.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Material, CPDActivity, WeeklyDigest]),
  ],
  providers: [WeeklyDigestService],
  controllers: [WeeklyDigestController],
  exports: [WeeklyDigestService],
})
export class WeeklyDigestModule {}
