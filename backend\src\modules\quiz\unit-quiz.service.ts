import {
  Injectable,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UnitQuiz } from '../../entities/unit-quiz.entity';
import { Unit } from '../../entities/unit.entity';
import { QuizQuestion } from '../../entities/quiz-question.entity';
// import { UserProgressService } from '../progress/user-progress.service';

@Injectable()
export class UnitQuizService {
  constructor(
    @InjectRepository(UnitQuiz) private unitQuizRepo: Repository<UnitQuiz>,
    @InjectRepository(Unit) private unitRepo: Repository<Unit>,
    @InjectRepository(QuizQuestion)
    private questionRepo: Repository<QuizQuestion>,
    // private userProgress: UserProgressService
  ) {}

  async generateQuiz(unitId: string): Promise<UnitQuiz> {
    // TODO: Compose questions from all topics in the unit
    throw new Error('Not implemented');
  }

  async validateAttempt(userId: string, unitId: string): Promise<boolean> {
    // TODO: Ensure user completed all topics
    throw new Error('Not implemented');
  }

  async getQuizForUser(unitId: string) {
    const quiz = await this.unitQuizRepo.findOne({
      where: { unit: { id: unitId }, isPublished: true },
      relations: ['questions'],
    });
    if (!quiz) throw new NotFoundException('Quiz not found');
    return {
      title: quiz.title,
      instructions: quiz.instructions,
      questions: quiz.questions.map((q) => ({
        id: q.id,
        text: q.text,
        options: q.options, // assuming options is an array
      })),
    };
  }

  async scoreQuiz(
    userId: string,
    unitId: string,
    answers: Record<string, string>,
  ) {
    const quiz = await this.unitQuizRepo.findOne({
      where: { unit: { id: unitId }, isPublished: true },
      relations: ['questions'],
    });
    if (!quiz) throw new NotFoundException('Quiz not found');
    let correct = 0;
    quiz.questions.forEach((q) => {
      if (answers[q.id] === q.correct_answer) correct++;
    });
    const score = Math.round((correct / quiz.questions.length) * 100);
    const passed = score >= 70;
    // Analytics & gamification hooks
    // if (this.analyticsService) await this.analyticsService.recordUnitQuizScore(userId, unitId, score);
    // if (this.gamificationService) await this.gamificationService.rewardForUnitQuiz(userId, unitId, passed);
    return {
      score,
      passed,
      feedback: passed ? 'Great job!' : 'Review the topics and try again.',
    };
  }
}
