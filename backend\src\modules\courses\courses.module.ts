import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CoursesService } from './courses.service';
import { CoursesController } from './courses.controller';
import { CourseCategoriesService } from './course-categories.service';
import { CourseCategoriesController } from './course-categories.controller';
import { Course } from '../../entities/course.entity';
import { CourseCategory } from '../../entities/course-category.entity';
import { CourseEnrollment } from '../../entities/course-enrollment.entity';
import { Unit } from '../../entities/unit.entity';
import { User } from '../../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Course,
      CourseCategory,
      CourseEnrollment,
      Unit,
      User,
    ]),
  ],
  controllers: [CoursesController, CourseCategoriesController],
  providers: [CoursesService, CourseCategoriesService],
  exports: [CoursesService, CourseCategoriesService],
})
export class CoursesModule {}
