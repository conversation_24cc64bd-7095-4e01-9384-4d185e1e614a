#!/bin/bash
# Deployment script for MedTrack Hub

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}
REGISTRY=${DOCKER_REGISTRY:-"your-registry.com"}
PROJECT_NAME="medtrack-hub"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to validate environment
validate_environment() {
    case $ENVIRONMENT in
        development|staging|production)
            print_status "Deploying to $ENVIRONMENT environment"
            ;;
        *)
            print_error "Invalid environment: $ENVIRONMENT"
            print_error "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is required for deployment"
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is required for deployment"
        exit 1
    fi
    
    if [ "$ENVIRONMENT" = "production" ] && [ ! -f ".env.production" ]; then
        print_error "Production environment file (.env.production) is required"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    # Frontend tests
    print_status "Running frontend tests..."
    cd frontend
    npm test -- --watchAll=false --coverage=false
    cd ..
    
    # Backend tests
    print_status "Running backend tests..."
    cd backend
    npm run test
    cd ..
    
    # Python analytics tests
    print_status "Running analytics tests..."
    cd backend/python_analytics
    source venv/bin/activate || source venv/Scripts/activate
    python -m pytest
    deactivate
    cd ../..
    
    print_success "All tests passed"
}

# Function to build Docker images
build_images() {
    print_status "Building Docker images..."
    
    # Build frontend image
    print_status "Building frontend image..."
    docker build -t $REGISTRY/$PROJECT_NAME-frontend:$VERSION ./frontend
    
    # Build backend image
    print_status "Building backend image..."
    docker build -t $REGISTRY/$PROJECT_NAME-backend:$VERSION ./backend
    
    # Build analytics image
    print_status "Building analytics image..."
    docker build -t $REGISTRY/$PROJECT_NAME-analytics:$VERSION ./backend/python_analytics
    
    print_success "Docker images built successfully"
}

# Function to push images to registry
push_images() {
    if [ "$ENVIRONMENT" = "production" ] || [ "$ENVIRONMENT" = "staging" ]; then
        print_status "Pushing images to registry..."
        
        docker push $REGISTRY/$PROJECT_NAME-frontend:$VERSION
        docker push $REGISTRY/$PROJECT_NAME-backend:$VERSION
        docker push $REGISTRY/$PROJECT_NAME-analytics:$VERSION
        
        print_success "Images pushed to registry"
    else
        print_status "Skipping image push for $ENVIRONMENT environment"
    fi
}

# Function to deploy with Docker Compose
deploy_with_compose() {
    print_status "Deploying with Docker Compose..."
    
    # Set environment file
    ENV_FILE=".env"
    if [ -f ".env.$ENVIRONMENT" ]; then
        ENV_FILE=".env.$ENVIRONMENT"
    fi
    
    # Set compose file
    COMPOSE_FILE="docker-compose.yml"
    if [ "$ENVIRONMENT" = "development" ]; then
        COMPOSE_FILE="docker-compose.dev.yml"
    fi
    
    # Deploy
    export VERSION=$VERSION
    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d --build
    
    print_success "Deployment completed"
}

# Function to run health checks
run_health_checks() {
    print_status "Running health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Run health check script
    if [ -f scripts/health-check.sh ]; then
        chmod +x scripts/health-check.sh
        ./scripts/health-check.sh
    else
        print_warning "Health check script not found"
    fi
}

# Function to run database migrations
run_migrations() {
    if [ "$ENVIRONMENT" != "development" ]; then
        print_status "Running database migrations..."
        
        # Wait for database to be ready
        sleep 10
        
        # Run migrations
        docker-compose exec backend npm run migration:run
        
        print_success "Database migrations completed"
    fi
}

# Function to backup database (production only)
backup_database() {
    if [ "$ENVIRONMENT" = "production" ]; then
        print_status "Creating database backup..."
        
        BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
        docker-compose exec db pg_dump -U postgres medtrack > $BACKUP_FILE
        
        print_success "Database backup created: $BACKUP_FILE"
    fi
}

# Function to rollback deployment
rollback() {
    print_warning "Rolling back deployment..."
    
    # Get previous version
    PREVIOUS_VERSION=${3:-"previous"}
    
    # Rollback images
    export VERSION=$PREVIOUS_VERSION
    docker-compose up -d
    
    print_success "Rollback completed"
}

# Function to show deployment status
show_status() {
    print_status "Deployment status:"
    docker-compose ps
    
    print_status "Service logs (last 50 lines):"
    docker-compose logs --tail=50
}

# Function to cleanup old images
cleanup() {
    print_status "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old images (keep last 3 versions)
    docker images $REGISTRY/$PROJECT_NAME-* --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
    tail -n +2 | sort -k2 -r | tail -n +4 | awk '{print $1}' | xargs -r docker rmi
    
    print_success "Cleanup completed"
}

# Function to show help
show_help() {
    echo "Usage: $0 [ENVIRONMENT] [VERSION] [COMMAND]"
    echo
    echo "ENVIRONMENT: development, staging, production (default: staging)"
    echo "VERSION: Docker image version tag (default: latest)"
    echo "COMMAND: deploy, rollback, status, cleanup (default: deploy)"
    echo
    echo "Examples:"
    echo "  $0 staging v1.2.3 deploy"
    echo "  $0 production latest rollback"
    echo "  $0 development latest status"
    echo
    echo "Environment variables:"
    echo "  DOCKER_REGISTRY: Docker registry URL"
    echo "  SKIP_TESTS: Skip running tests (true/false)"
    echo "  SKIP_BACKUP: Skip database backup (true/false)"
}

# Main deployment function
main() {
    COMMAND=${3:-deploy}
    
    case $COMMAND in
        deploy)
            echo -e "${BLUE}"
            echo "╔══════════════════════════════════════╗"
            echo "║        MedTrack Hub Deploy           ║"
            echo "║     Environment: $ENVIRONMENT"
            echo "║     Version: $VERSION"
            echo "╚══════════════════════════════════════╝"
            echo -e "${NC}"
            
            validate_environment
            check_prerequisites
            
            # Backup database (production only)
            if [ "$SKIP_BACKUP" != "true" ]; then
                backup_database
            fi
            
            # Run tests
            if [ "$SKIP_TESTS" != "true" ]; then
                run_tests
            fi
            
            # Build and push images
            build_images
            push_images
            
            # Deploy
            deploy_with_compose
            
            # Run migrations
            run_migrations
            
            # Health checks
            run_health_checks
            
            print_success "Deployment completed successfully!"
            ;;
        rollback)
            rollback
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
