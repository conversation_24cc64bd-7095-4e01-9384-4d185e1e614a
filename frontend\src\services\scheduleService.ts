import api from './api';

export interface Event {
  id: string;
  title: string;
  description?: string;
  date: Date;
  endDate: Date;
  type?: string;
  location?: string;
  instructor?: string | null;
}

const scheduleService = {
  async getEvents(startDate?: Date, endDate?: Date): Promise<Event[]> {
    try {
      let url = '/events';

      // Add date range parameters if provided
      if (startDate && endDate) {
        const startStr = startDate.toISOString();
        const endStr = endDate.toISOString();
        url += `?startDate=${encodeURIComponent(startStr)}&endDate=${encodeURIComponent(endStr)}`;
      }

      const response = await api.get(url);

      // Convert string dates to Date objects
      return response.data.map((event: any) => ({
        ...event,
        date: new Date(event.date),
        endDate: new Date(event.endDate),
      }));
    } catch (error) {
      console.error('Error fetching events:', error);

      // Return placeholder data if API fails
      const currentDate = new Date();
      return [
        {
          id: '1',
          title: 'Anatomy Lecture',
          description: 'Introduction to the skeletal system',
          date: new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            currentDate.getDate(),
            10,
            0
          ),
          endDate: new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            currentDate.getDate(),
            12,
            0
          ),
          type: 'lecture',
          location: 'Room 101',
          instructor: 'Dr. Sarah Johnson',
        },
        {
          id: '2',
          title: 'Physiology Lab',
          description: 'Cardiovascular system experiments',
          date: new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            currentDate.getDate() + 1,
            14,
            30
          ),
          endDate: new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            currentDate.getDate() + 1,
            16,
            30
          ),
          type: 'lab',
          location: 'Lab 3B',
          instructor: 'Dr. Michael Chen',
        },
      ];
    }
  },

  async getEventById(id: string): Promise<Event> {
    try {
      const response = await api.get(`/events/${id}`);

      // Convert string dates to Date objects
      return {
        ...response.data,
        date: new Date(response.data.date),
        endDate: new Date(response.data.endDate),
      };
    } catch (error) {
      console.error('Error fetching event by ID:', error);
      throw error;
    }
  },

  async createEvent(event: Omit<Event, 'id'>): Promise<Event> {
    try {
      const response = await api.post('/events', {
        ...event,
        date: event.date.toISOString(),
        endDate: event.endDate.toISOString(),
      });

      // Convert string dates to Date objects
      return {
        ...response.data,
        date: new Date(response.data.date),
        endDate: new Date(response.data.endDate),
      };
    } catch (error) {
      console.error('Error creating event:', error);
      throw error;
    }
  },

  async updateEvent(id: string, event: Partial<Omit<Event, 'id'>>): Promise<Event> {
    try {
      // Convert Date objects to ISO strings for API
      const eventData = { ...event };
      if (eventData.date) {
        eventData.date = eventData.date.toISOString();
      }
      if (eventData.endDate) {
        eventData.endDate = eventData.endDate.toISOString();
      }

      const response = await api.put(`/events/${id}`, eventData);

      // Convert string dates to Date objects
      return {
        ...response.data,
        date: new Date(response.data.date),
        endDate: new Date(response.data.endDate),
      };
    } catch (error) {
      console.error('Error updating event:', error);
      throw error;
    }
  },

  async deleteEvent(id: string): Promise<void> {
    try {
      await api.delete(`/events/${id}`);
    } catch (error) {
      console.error('Error deleting event:', error);
      throw error;
    }
  },
};

export default scheduleService;
