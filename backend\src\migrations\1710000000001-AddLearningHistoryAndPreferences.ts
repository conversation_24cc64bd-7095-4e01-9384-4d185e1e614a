import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLearningHistoryAndPreferences1710000000001
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add learning history and preferences to users table
    await queryRunner.query(`
            ALTER TABLE users
            ADD COLUMN IF NOT EXISTS learning_history jsonb,
            ADD COLUMN IF NOT EXISTS preferences jsonb;
        `);

    // Add category and difficulty to materials table
    await queryRunner.query(`
            ALTER TABLE materials
            ADD COLUMN IF NOT EXISTS category varchar,
            ADD COLUMN IF NOT EXISTS difficulty float;
        `);

    // Create indexes for better query performance
    await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_materials_category ON materials(category);
            CREATE INDEX IF NOT EXISTS idx_materials_difficulty ON materials(difficulty);
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove indexes
    await queryRunner.query(`
            DROP INDEX IF EXISTS idx_materials_category;
            DROP INDEX IF EXISTS idx_materials_difficulty;
        `);

    // Remove columns from materials table
    await queryRunner.query(`
            ALTER TABLE materials
            DROP COLUMN IF EXISTS category,
            DROP COLUMN IF EXISTS difficulty;
        `);

    // Remove columns from users table
    await queryRunner.query(`
            ALTER TABLE users
            DROP COLUMN IF EXISTS learning_history,
            DROP COLUMN IF EXISTS preferences;
        `);
  }
}
