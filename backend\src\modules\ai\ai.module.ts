import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AIRecommendationService } from './ai-recommendation.service';
import { AIRecommendationController } from './ai-recommendation.controller';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';
import { CPDActivity } from '../../entities/cpd-tracking.entity';
import { LearningSuggestion } from '../../entities/learning-suggestions.entity';
import { AIService } from './ai.service';
import { RedisModule } from '../redis/redis.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Material, CPDActivity, LearningSuggestion]),
    RedisModule,
  ],
  providers: [AIRecommendationService, AIService],
  controllers: [AIRecommendationController],
  exports: [AIRecommendationService],
})
export class AIModule {}
