import api from './api';

export interface ProgressStats {
  overallProgress: number;
  coursesCompleted: number;
  totalCourses: number;
  streak: number;
  lastActivity: string | null;
}

export interface CourseProgress {
  id: string;
  title: string;
  progress: number;
  completed: boolean;
}

export interface Activity {
  id: string;
  type: string;
  title: string;
  date: string;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  date: string;
}

export interface ProgressData {
  stats: ProgressStats;
  courseProgress: CourseProgress[];
  recentActivities: Activity[];
  achievements: Achievement[];
}

const progressService = {
  async getProgressStats(): Promise<ProgressStats> {
    try {
      const response = await api.get('/progress/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching progress stats:', error);

      // Return placeholder data if API fails
      return {
        overallProgress: 48,
        coursesCompleted: 7,
        totalCourses: 15,
        streak: 12,
        lastActivity: new Date().toISOString(),
      };
    }
  },

  async getCourseProgress(): Promise<CourseProgress[]> {
    try {
      const response = await api.get('/progress/courses');
      return response.data;
    } catch (error) {
      console.error('Error fetching course progress:', error);

      // Try to get courses and generate progress data
      try {
        const coursesResponse = await api.get('/courses');
        return coursesResponse.data.map((course: any) => ({
          id: course.id,
          title: course.title,
          progress: Math.floor(Math.random() * 100),
          completed: Math.random() > 0.8,
        }));
      } catch (fallbackError) {
        console.error('Error fetching course progress from fallback endpoint:', fallbackError);
        throw fallbackError;
      }
    }
  },

  async getRecentActivities(): Promise<Activity[]> {
    try {
      const response = await api.get('/progress/activities');
      return response.data;
    } catch (error) {
      console.error('Error fetching recent activities:', error);

      // Return placeholder data if API fails
      return [
        {
          id: '1',
          type: 'course',
          title: 'Completed quiz in Physiology Fundamentals',
          date: new Date().toISOString(),
        },
        {
          id: '2',
          type: 'material',
          title: 'Downloaded Anatomy Lecture Notes',
          date: new Date(Date.now() - 3600000).toISOString(),
        },
      ];
    }
  },

  async getAchievements(): Promise<Achievement[]> {
    try {
      const response = await api.get('/progress/achievements');
      return response.data;
    } catch (error) {
      console.error('Error fetching achievements:', error);

      // Return placeholder data if API fails
      return [
        {
          id: '1',
          title: 'Fast Learner',
          description: 'Completed 5 courses',
          icon: 'trophy',
          date: new Date(Date.now() - 86400000 * 7).toISOString(),
        },
        {
          id: '2',
          title: 'Consistent Student',
          description: 'Maintained a 10-day streak',
          icon: 'calendar',
          date: new Date().toISOString(),
        },
      ];
    }
  },

  async getAllProgressData(): Promise<ProgressData> {
    try {
      const stats = await this.getProgressStats();
      const courseProgress = await this.getCourseProgress();
      const recentActivities = await this.getRecentActivities();
      const achievements = await this.getAchievements();

      return {
        stats,
        courseProgress,
        recentActivities,
        achievements,
      };
    } catch (error) {
      console.error('Error fetching all progress data:', error);
      throw error;
    }
  },

  async updateUnitProgress(unitId: string, completed: boolean): Promise<void> {
    try {
      await api.post(`/progress/${unitId}`, { completed });
    } catch (error) {
      console.error('Error updating unit progress:', error);
      throw error;
    }
  },
};

export default progressService;
