import { getAuthToken } from '@/lib/auth';

interface LearningPatterns {
  studyTimeDistribution: {
    timeOfDay: string;
    duration: number;
  }[];
  topicMastery: {
    topic: string;
    masteryLevel: number;
    questionsAnswered: number;
    correctAnswers: number;
  }[];
  learningStreak: {
    currentStreak: number;
    longestStreak: number;
    lastStudyDate: string;
  };
}

interface PerformancePredictions {
  overallScore: number;
  topicScores: {
    topic: string;
    predictedScore: number;
    confidence: number;
  }[];
  improvementAreas: {
    topic: string;
    currentScore: number;
    potentialScore: number;
  }[];
}

interface StudyRecommendations {
  recommendedTopics: {
    topic: string;
    priority: 'high' | 'medium' | 'low';
    reason: string;
  }[];
  studySchedule: {
    day: string;
    topics: string[];
    estimatedDuration: number;
  }[];
  learningResources: {
    type: 'quiz' | 'reading' | 'video';
    title: string;
    description: string;
    url: string;
  }[];
}

class AnalyticsService {
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_ANALYTICS_API_URL || 'http://localhost:5000';
  }

  private async fetchWithAuth(endpoint: string) {
    const token = await getAuthToken();
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Analytics API error: ${response.statusText}`);
    }

    return response.json();
  }

  async getUserAnalytics(userId: string): Promise<{
    learningPatterns: LearningPatterns;
    performancePredictions: PerformancePredictions;
    studyRecommendations: StudyRecommendations;
  }> {
    return this.fetchWithAuth(`/analytics/user/${userId}`);
  }

  async getLearningPatterns(userId: string): Promise<LearningPatterns> {
    return this.fetchWithAuth(`/analytics/patterns/${userId}`);
  }

  async getPerformancePredictions(userId: string): Promise<PerformancePredictions> {
    return this.fetchWithAuth(`/analytics/predictions/${userId}`);
  }
}

export const analyticsService = new AnalyticsService();
