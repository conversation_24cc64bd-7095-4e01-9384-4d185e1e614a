const axios = require('axios');

async function testFrontendAppRouterFix() {
  console.log('🚀 Testing Frontend App Router Fixes...');
  
  const testUrls = [
    'http://localhost:3000/test-simple',
    'http://localhost:3000/auth/register-simple',
    'http://localhost:3000/dashboard',
    'http://localhost:3000/auth/register'
  ];
  
  const results = {};
  
  for (const url of testUrls) {
    try {
      console.log(`\n🔍 Testing: ${url}`);
      const response = await axios.get(url, { 
        timeout: 10000,
        validateStatus: (status) => status < 500 // Accept 4xx but not 5xx
      });
      
      console.log(`✅ ${url} - Status: ${response.status}`);
      results[url] = {
        success: true,
        status: response.status,
        error: null
      };
      
    } catch (error) {
      console.log(`❌ ${url} - Error: ${error.message}`);
      results[url] = {
        success: false,
        status: error.response?.status || 'No response',
        error: error.message
      };
    }
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log('=' * 50);
  
  let successCount = 0;
  for (const [url, result] of Object.entries(results)) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${url} (${result.status})`);
    if (result.success) successCount++;
  }
  
  console.log(`\n🎯 Overall: ${successCount}/${testUrls.length} tests passed`);
  
  if (successCount === testUrls.length) {
    console.log('🎉 All tests passed! Frontend App Router issues are fixed!');
  } else {
    console.log('⚠️ Some tests failed. Check the errors above.');
  }
  
  // Test authentication functionality
  console.log('\n🔐 Testing Authentication...');
  try {
    const registerData = {
      name: 'App Router Test User',
      email: '<EMAIL>',
      password: 'test123',
      role: 'student'
    };
    
    const registerResponse = await axios.post('http://localhost:3002/v1/auth/register', registerData, {
      timeout: 10000
    });
    
    console.log('✅ Backend registration working');
    console.log(`📊 User created: ${registerResponse.data.user.email}`);
    
  } catch (error) {
    console.log('❌ Backend registration failed:', error.message);
  }
  
  return results;
}

testFrontendAppRouterFix().catch(console.error);
