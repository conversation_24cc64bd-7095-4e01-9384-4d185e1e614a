import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { User } from './user.entity';
import { Topic } from './topic.entity';
import { SessionAttendance } from './session-attendance.entity';

@Entity()
export class StudySession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, (user: User) => user.study_sessions)
  user: User;

  @ManyToOne(() => Topic)
  topic: Topic;

  @Column()
  duration_minutes: number;

  @Column({ type: 'jsonb', nullable: true })
  activities: {
    type: string; // 'reading', 'quiz', 'notes', 'flashcards'
    duration_minutes: number;
    score?: number;
  }[];

  @Column({ type: 'float', nullable: true })
  focus_score: number; // 0-1 scale based on activity consistency

  @Column({ type: 'jsonb', nullable: true })
  notes: string;

  @Column({ default: false })
  is_offline: boolean;

  @Column({ type: 'timestamp' })
  started_at: Date;

  @Column({ type: 'timestamp' })
  ended_at: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Attendance records relationship
  @OneToMany(() => SessionAttendance, (attendance) => attendance.study_session)
  attendance_records: SessionAttendance[];
}
