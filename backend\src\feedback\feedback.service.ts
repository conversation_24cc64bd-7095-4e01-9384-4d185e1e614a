import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Feedback } from '../entities/feedback.entity';

@Injectable()
export class FeedbackService {
  constructor(
    @InjectRepository(Feedback)
    private feedbackRepository: Repository<Feedback>,
  ) {}

  async create(
    userId: string,
    comments: string,
    rating?: number,
    materialId?: string,
    unitId?: string,
  ): Promise<Feedback> {
    const feedback = this.feedbackRepository.create({
      user: { id: userId } as any, // Cast as any; ideally use a DTO
      comments,
      rating,
      material: materialId ? ({ id: materialId } as any) : null, // Cast as any
      unit: unitId ? ({ id: unitId } as any) : null, // Cast as any
    });
    return this.feedbackRepository.save(feedback) as Promise<Feedback>; // Type assertion for single entity
  }

  async findAll(): Promise<Feedback[]> {
    return this.feedbackRepository.find({
      relations: ['user', 'material', 'unit'],
    });
  }
}
