# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Testing
coverage
*.lcov
test-results
.nyc_output
__tests__
*.test.js
*.test.ts
*.test.jsx
*.test.tsx
*.spec.js
*.spec.ts
*.spec.jsx
*.spec.tsx
jest.config.js
jest.setup.js

# Environment variables
.env*

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Next.js build outputs (keep for production)
out

# Development files
*.tsbuildinfo
.eslintcache

# Git
.git
.gitignore
README.md
CHANGELOG.md

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Scripts
scripts

# Temporary files
tmp
temp

# Zip files
*.zip

# TypeScript build info
tsconfig.tsbuildinfo
