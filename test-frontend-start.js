const { spawn } = require('child_process');
const axios = require('axios');

async function testFrontendStart() {
  console.log('🚀 Testing Frontend Startup...');
  
  // Start the frontend development server
  console.log('📦 Installing dependencies...');
  
  const install = spawn('pnpm', ['install', '--ignore-scripts'], {
    cwd: './frontend',
    stdio: 'inherit',
    shell: true
  });
  
  await new Promise((resolve, reject) => {
    install.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Dependencies installed successfully');
        resolve();
      } else {
        console.error('❌ Failed to install dependencies');
        reject(new Error(`Install failed with code ${code}`));
      }
    });
  });
  
  console.log('🔧 Starting development server...');
  
  const dev = spawn('pnpm', ['run', 'dev'], {
    cwd: './frontend',
    stdio: 'pipe',
    shell: true
  });
  
  let serverStarted = false;
  let output = '';
  
  dev.stdout.on('data', (data) => {
    const text = data.toString();
    output += text;
    console.log(text);
    
    if (text.includes('Ready') || text.includes('started server') || text.includes('Local:')) {
      serverStarted = true;
    }
  });
  
  dev.stderr.on('data', (data) => {
    const text = data.toString();
    output += text;
    console.error(text);
  });
  
  // Wait for server to start or timeout
  const timeout = 60000; // 60 seconds
  const startTime = Date.now();
  
  while (!serverStarted && (Date.now() - startTime) < timeout) {
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  if (serverStarted) {
    console.log('✅ Frontend server started successfully');
    
    // Test if the server is accessible
    try {
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait a bit more
      const response = await axios.get('http://localhost:3000', { timeout: 5000 });
      console.log('✅ Frontend is accessible');
      console.log(`📊 Status: ${response.status}`);
    } catch (error) {
      console.log('⚠️ Server started but not yet accessible:', error.message);
    }
  } else {
    console.log('❌ Frontend server failed to start within timeout');
    console.log('📋 Output:', output);
  }
  
  // Kill the process
  dev.kill();
  
  return serverStarted;
}

testFrontendStart().catch(console.error);
