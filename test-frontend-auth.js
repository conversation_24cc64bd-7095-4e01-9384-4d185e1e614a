const axios = require('axios');

// Configuration
const config = {
  backend: 'http://localhost:3002',
  frontend: 'http://localhost:3000',
};

// Test data
const testUser = {
  email: '<EMAIL>',
  username: 'frontendtest',
  name: 'Frontend Test User',
  password: 'test123',
  role: 'student'
};

async function testBackendDirectly() {
  console.log('\n🔍 Testing Backend API Directly...');
  
  try {
    // Test health
    const healthResponse = await axios.get(`${config.backend}/health`);
    console.log('✅ Backend health:', healthResponse.data.status);
    
    // Test registration
    console.log('📝 Testing direct registration...');
    const registerResponse = await axios.post(`${config.backend}/auth/register`, testUser);
    console.log('✅ Registration successful');
    console.log('📊 User:', registerResponse.data.user.email);
    console.log('📊 Token present:', !!registerResponse.data.accessToken);

    // Test login
    console.log('🔐 Testing direct login...');
    const loginResponse = await axios.post(`${config.backend}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ Login successful');
    console.log('📊 User:', loginResponse.data.user.email);
    console.log('📊 Token present:', !!loginResponse.data.accessToken);
    
    return {
      health: true,
      register: true,
      login: true,
      token: loginResponse.data.accessToken
    };
  } catch (error) {
    console.error('❌ Backend test failed:', error.response?.data || error.message);
    return {
      health: false,
      register: false,
      login: false,
      token: null
    };
  }
}

async function testFrontendAPI() {
  console.log('\n🔍 Testing Frontend API Routes...');
  
  try {
    // Test frontend health
    const frontendResponse = await axios.get(config.frontend, { timeout: 5000 });
    console.log('✅ Frontend accessible');
    
    // Test NextAuth API routes
    console.log('🔐 Testing NextAuth API routes...');
    
    // Test registration through frontend API
    const registerResponse = await axios.post(`${config.frontend}/api/auth/register`, {
      email: '<EMAIL>',
      username: 'frontendapi',
      name: 'Frontend API Test',
      password: 'test123',
      role: 'student'
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 10000
    });
    
    console.log('✅ Frontend registration API working');
    console.log('📊 Response:', registerResponse.status);
    
    return {
      accessible: true,
      apiRoutes: true
    };
  } catch (error) {
    console.error('❌ Frontend test failed:', error.response?.data || error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Frontend server is not running');
    }
    return {
      accessible: false,
      apiRoutes: false
    };
  }
}

async function testAuthFlow() {
  console.log('\n🔍 Testing Complete Auth Flow...');
  
  try {
    // Test the complete authentication flow
    console.log('1️⃣ Testing registration flow...');
    
    const registerData = {
      email: '<EMAIL>',
      username: 'flowtest',
      name: 'Flow Test User',
      password: 'test123',
      role: 'student'
    };
    
    // Register via backend
    const registerResponse = await axios.post(`${config.backend}/auth/register`, registerData);
    const { accessToken, user } = registerResponse.data;

    console.log('✅ Registration completed');
    console.log('📊 User ID:', user.id);
    console.log('📊 Email:', user.email);
    console.log('📊 Role:', user.role);

    console.log('2️⃣ Testing login flow...');

    // Login via backend
    const loginResponse = await axios.post(`${config.backend}/auth/login`, {
      email: registerData.email,
      password: registerData.password
    });

    const { accessToken: loginToken, user: loginUser } = loginResponse.data;

    console.log('✅ Login completed');
    console.log('📊 User ID:', loginUser.id);
    console.log('📊 Token valid:', !!loginToken);

    console.log('3️⃣ Testing protected endpoint...');

    // Test protected endpoint
    const protectedResponse = await axios.get(`${config.backend}/auth/protected`, {
      headers: { 'Authorization': `Bearer ${loginToken}` }
    });
    
    console.log('✅ Protected endpoint access successful');
    console.log('📊 Message:', protectedResponse.data.message);
    
    return {
      register: true,
      login: true,
      protected: true
    };
  } catch (error) {
    console.error('❌ Auth flow test failed:', error.response?.data || error.message);
    return {
      register: false,
      login: false,
      protected: false
    };
  }
}

async function testCORS() {
  console.log('\n🔍 Testing CORS Configuration...');
  
  try {
    // Test CORS preflight
    const corsResponse = await axios.options(`${config.backend}/v1/auth/login`, {
      headers: {
        'Origin': config.frontend,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,Authorization'
      }
    });
    
    console.log('✅ CORS preflight successful');
    console.log('📊 Status:', corsResponse.status);
    
    return { cors: true };
  } catch (error) {
    console.error('❌ CORS test failed:', error.message);
    return { cors: false };
  }
}

async function runAllTests() {
  console.log('🚀 Starting Frontend Authentication Integration Tests...');
  console.log('=' * 60);
  
  const results = {
    backend: await testBackendDirectly(),
    frontend: await testFrontendAPI(),
    authFlow: await testAuthFlow(),
    cors: await testCORS()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('=' * 40);
  console.log(`Backend Health: ${results.backend.health ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Backend Register: ${results.backend.register ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Backend Login: ${results.backend.login ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Frontend Accessible: ${results.frontend.accessible ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Frontend API Routes: ${results.frontend.apiRoutes ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Auth Flow Register: ${results.authFlow.register ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Auth Flow Login: ${results.authFlow.login ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Auth Flow Protected: ${results.authFlow.protected ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`CORS Configuration: ${results.cors.cors ? '✅ PASS' : '❌ FAIL'}`);
  
  const backendWorking = results.backend.health && results.backend.register && results.backend.login;
  const frontendWorking = results.frontend.accessible;
  const authWorking = results.authFlow.register && results.authFlow.login && results.authFlow.protected;
  
  console.log(`\n🎯 Overall Status:`);
  console.log(`Backend: ${backendWorking ? '✅ WORKING' : '❌ ISSUES'}`);
  console.log(`Frontend: ${frontendWorking ? '✅ WORKING' : '❌ ISSUES'}`);
  console.log(`Auth Integration: ${authWorking ? '✅ WORKING' : '❌ ISSUES'}`);
  
  if (!frontendWorking) {
    console.log('\n💡 Frontend Issues:');
    console.log('   - Start frontend: cd frontend && npm run dev');
    console.log('   - Check for compilation errors');
    console.log('   - Verify port 3000 is available');
  }
  
  if (backendWorking && authWorking) {
    console.log('\n🎉 Authentication system is working correctly!');
    console.log('   - Backend API endpoints are functional');
    console.log('   - Registration and login work properly');
    console.log('   - JWT tokens are being generated and validated');
    console.log('   - Protected endpoints are secured');
  }
  
  return { backendWorking, frontendWorking, authWorking };
}

// Run the tests
runAllTests().catch(console.error);
