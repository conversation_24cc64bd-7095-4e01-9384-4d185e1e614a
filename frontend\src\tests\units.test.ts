/**
 * Units Module Tests
 *
 * This file contains tests for the units module.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import mockApiService from '../services/mockApiService';
import api from '../services/api';

// Mock the API service
vi.mock('../services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Create a simple units service for testing
const unitsService = {
  async getUnits() {
    try {
      const response = await api.get('/units');
      return response.data;
    } catch (error) {
      console.warn('Error fetching units from API, using mock data:', error);
      return mockApiService.getUnits();
    }
  },

  async getUnitById(id: string) {
    try {
      const response = await api.get(`/units/${id}`);
      return response.data;
    } catch (error) {
      console.warn('Error fetching unit by ID from API, using mock data:', error);
      return mockApiService.getUnitById(id);
    }
  },

  async createUnit(unitData: any) {
    try {
      const response = await api.post('/units', unitData);
      return response.data;
    } catch (error) {
      console.warn('Error creating unit via API, using mock data:', error);
      return mockApiService.createUnit(unitData);
    }
  },
};

describe('Units Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Get Units', () => {
    it('should fetch units from API when available', async () => {
      const mockUnits = [
        { id: '1', title: 'Unit 1', materialId: '1' },
        { id: '2', title: 'Unit 2', materialId: '1' },
      ];

      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockUnits });

      const units = await unitsService.getUnits();

      expect(api.get).toHaveBeenCalledWith('/units');
      expect(units).toEqual(mockUnits);
    });

    it('should fall back to mock API when real API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));

      // Spy on mock API
      const mockGetUnits = vi.spyOn(mockApiService, 'getUnits');

      const units = await unitsService.getUnits();

      expect(api.get).toHaveBeenCalledWith('/units');
      expect(mockGetUnits).toHaveBeenCalled();
      expect(units.length).toBeGreaterThan(0);
    });
  });

  describe('Get Unit by ID', () => {
    it('should fetch a specific unit from API when available', async () => {
      const mockUnit = { id: '1', title: 'Unit 1', materialId: '1' };

      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockUnit });

      const unit = await unitsService.getUnitById('1');

      expect(api.get).toHaveBeenCalledWith('/units/1');
      expect(unit).toEqual(mockUnit);
    });

    it('should fall back to mock API when real API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));

      // Spy on mock API
      const mockGetUnitById = vi.spyOn(mockApiService, 'getUnitById');

      const unit = await unitsService.getUnitById('1');

      expect(api.get).toHaveBeenCalledWith('/units/1');
      expect(mockGetUnitById).toHaveBeenCalledWith('1');
      expect(unit.id).toBe('1');
    });
  });

  describe('Create Unit', () => {
    it('should create unit via API when available', async () => {
      const unitData = {
        title: 'New Unit',
        description: 'Test description',
        materialId: '1',
      };

      const mockResponse = {
        id: '3',
        title: 'New Unit',
        description: 'Test description',
        materialId: '1',
      };

      // Mock successful API response
      (api.post as any).mockResolvedValueOnce({ data: mockResponse });

      const result = await unitsService.createUnit(unitData);

      expect(api.post).toHaveBeenCalledWith('/units', unitData);
      expect(result).toEqual(mockResponse);
    });

    it('should fall back to mock API when real API fails', async () => {
      const unitData = {
        title: 'New Unit',
        description: 'Test description',
        materialId: '1',
      };

      // Mock API failure
      (api.post as any).mockRejectedValueOnce(new Error('API error'));

      // Spy on mock API
      const mockCreateUnit = vi.spyOn(mockApiService, 'createUnit');

      const result = await unitsService.createUnit(unitData);

      expect(api.post).toHaveBeenCalledWith('/units', unitData);
      expect(mockCreateUnit).toHaveBeenCalledWith(unitData);
      expect(result.title).toBe(unitData.title);
    });
  });

  describe('Mock API Units', () => {
    it('should return units from mock API', async () => {
      const units = await mockApiService.getUnits();

      expect(units).toBeDefined();
      expect(Array.isArray(units)).toBe(true);
      expect(units.length).toBeGreaterThan(0);
      expect(units[0]).toHaveProperty('id');
      expect(units[0]).toHaveProperty('title');
      expect(units[0]).toHaveProperty('materialId');
    });

    it('should get a unit by ID from mock API', async () => {
      const unit = await mockApiService.getUnitById('1');

      expect(unit).toBeDefined();
      expect(unit.id).toBe('1');
      expect(unit.title).toBeDefined();
    });

    it('should create a unit with mock API', async () => {
      const unitData = {
        title: 'Test Unit',
        description: 'This is a test unit',
        materialId: '1',
      };

      const unit = await mockApiService.createUnit(unitData);

      expect(unit).toBeDefined();
      expect(unit.id).toBeDefined();
      expect(unit.title).toBe(unitData.title);
      expect(unit.description).toBe(unitData.description);
      expect(unit.materialId).toBe(unitData.materialId);
    });
  });
});
