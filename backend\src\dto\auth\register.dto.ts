// src/dto/auth/register.dto.ts
import {
  IsEmail,
  IsNotEmpty,
  MinLength,
  IsEnum,
  IsOptional,
} from 'class-validator';
import { UserRole } from '../../entities/user.entity';

export class RegisterDto {
  @IsNotEmpty()
  name: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  username: string;

  @IsNotEmpty()
  @MinLength(4) // Reduced to allow test accounts
  password: string;

  @IsEnum(UserRole)
  role: UserRole = UserRole.STUDENT;

  @IsOptional()
  bio?: string;

  @IsOptional()
  profile_picture?: string;

  @IsOptional()
  phone_number?: string;
}
