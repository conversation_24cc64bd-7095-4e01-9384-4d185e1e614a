import { useState, useEffect } from 'react';
import { analyticsService } from '@/services/analyticsService';
import { useAuth } from '@/hooks/useAuth';

export function useAnalytics() {
  const { user } = useAuth();
  const [learningPatterns, setLearningPatterns] = useState<any>(null);
  const [performancePredictions, setPerformancePredictions] = useState<any>(null);
  const [studyRecommendations, setStudyRecommendations] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadAnalytics = async () => {
      if (!user?.id) return;

      try {
        setIsLoading(true);
        const analytics = await analyticsService.getUserAnalytics(user.id);

        setLearningPatterns(analytics.learningPatterns);
        setPerformancePredictions(analytics.performancePredictions);
        setStudyRecommendations(analytics.studyRecommendations);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load analytics'));
      } finally {
        setIsLoading(false);
      }
    };

    loadAnalytics();
  }, [user?.id]);

  const refreshAnalytics = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      const analytics = await analyticsService.getUserAnalytics(user.id);

      setLearningPatterns(analytics.learningPatterns);
      setPerformancePredictions(analytics.performancePredictions);
      setStudyRecommendations(analytics.studyRecommendations);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to refresh analytics'));
    } finally {
      setIsLoading(false);
    }
  };

  return {
    learningPatterns,
    performancePredictions,
    studyRecommendations,
    isLoading,
    error,
    refreshAnalytics,
  };
}
