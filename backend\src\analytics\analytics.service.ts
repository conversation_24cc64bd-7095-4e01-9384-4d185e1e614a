/* eslint-disable prettier/prettier */
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Progress } from '../entities/progress.entity';
import { UserResponse } from '../entities/user-response.entity';
import { StudyEvent } from '../entities/study-event.entity';
import { Flashcard } from '../entities/flashcard.entity';
import { QuizAttempt } from '../entities/quiz-attempt.entity';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class AnalyticsService {
  private readonly modelPath = path.join(
    process.cwd(),
    'models',
    'performance-prediction',
  );

  constructor(
    @InjectRepository(Progress)
    private progressRepository: Repository<Progress>,
    @InjectRepository(UserResponse)
    private userResponseRepository: Repository<UserResponse>,
    @InjectRepository(StudyEvent)
    private studyEventRepository: Repository<StudyEvent>,
    @InjectRepository(Flashcard)
    private flashcardRepository: Repository<Flashcard>,
    @InjectRepository(QuizAttempt)
    private quizAttemptRepository: Repository<QuizAttempt>,
  ) {
    // Model initialization removed - using statistical methods instead
  }

  // Statistical prediction methods (replacing TensorFlow)
  private calculateStatisticalPrediction(features: number[]): number {
    // Simple weighted average prediction based on historical performance
    const weights = [0.3, 0.25, 0.2, 0.15, 0.1]; // Recent performance weighted more heavily
    let weightedSum = 0;
    let totalWeight = 0;

    for (let i = 0; i < Math.min(features.length, weights.length); i++) {
      weightedSum += features[i] * weights[i];
      totalWeight += weights[i];
    }

    return totalWeight > 0 ? weightedSum / totalWeight : 0.5;
  }

  async trackEvent(eventData: {
    user_id: string;
    event_type: string;
    data: any;
    timestamp: string;
  }) {
    const event = this.studyEventRepository.create({
      user_id: eventData.user_id,
      event_type: eventData.event_type,
      data: eventData.data,
      timestamp: new Date(eventData.timestamp),
    });
    return this.studyEventRepository.save(event);
  }

  async batchTrackEvents(
    events: Array<{
      user_id: string;
      event_type: string;
      data: any;
      timestamp: string;
    }>,
  ) {
    const studyEvents = events.map((event) =>
      this.studyEventRepository.create({
        user_id: event.user_id,
        event_type: event.event_type,
        data: event.data,
        timestamp: new Date(event.timestamp),
      }),
    );
    return this.studyEventRepository.save(studyEvents);
  }

  async calculatePerformanceMetrics(user_id: string) {
    const [flashcards, quizAttempts] = await Promise.all([
      this.flashcardRepository.find({ where: { user: { id: user_id } } }),
      this.quizAttemptRepository.find({ where: { user: { id: user_id } } }),
    ]);

    const now = new Date();
    const metrics = {
      totalCards: flashcards.length,
      cardsDue: flashcards.filter(
        (card: Flashcard) => new Date(card.next_review) <= now,
      ).length,
      averageInterval: this.calculateAverageInterval(flashcards),
      successRate: this.calculateSuccessRate(flashcards),
      quizPerformance: this.calculateQuizPerformance(quizAttempts),
      studyStreak: await this.calculateStudyStreak(user_id),
    };

    return metrics;
  }

  async generatePredictions(user_id: string) {
    const features = await this.extractFeatures(user_id);
    const prediction = this.calculateStatisticalPrediction(features);

    return {
      predictedSuccessRate: prediction,
      confidence: this.calculateConfidence(features),
      recommendedStudyTime: this.calculateRecommendedStudyTime(features),
    };
  }

  async analyzeStudyPatterns(user_id: string) {
    const events = await this.studyEventRepository.find({
      where: { user_id },
      order: { timestamp: 'ASC' },
    });

    return {
      preferredStudyTimes: this.analyzePreferredStudyTimes(events),
      studyDuration: this.analyzeStudyDuration(events),
      performanceByTopic: await this.analyzePerformanceByTopic(user_id),
      consistencyScore: this.calculateConsistencyScore(events),
    };
  }

  async generateRecommendations(user_id: string) {
    const [metrics, patterns, predictions] = await Promise.all([
      this.calculatePerformanceMetrics(user_id),
      this.analyzeStudyPatterns(user_id),
      this.generatePredictions(user_id),
    ]);

    return {
      studySchedule: this.generateStudySchedule(metrics, patterns),
      focusAreas: this.identifyFocusAreas(metrics),
      improvementTips: this.generateImprovementTips(
        metrics,
        patterns,
        predictions,
      ),
    };
  }

  private calculateAverageInterval(flashcards: Flashcard[]): number {
    if (flashcards.length === 0) return 0;
    return (
      flashcards.reduce((sum, card) => sum + card.interval, 0) /
      flashcards.length
    );
  }

  private calculateSuccessRate(flashcards: Flashcard[]): number {
    if (flashcards.length === 0) return 0;
    return (
      flashcards.reduce(
        (sum, card) => sum + (card.correct_streak > 0 ? 1 : 0),
        0,
      ) / flashcards.length
    );
  }

  private calculateQuizPerformance(attempts: QuizAttempt[]): any {
    if (attempts.length === 0) return { averageScore: 0, totalAttempts: 0 };

    const averageScore =
      attempts.reduce((sum, attempt) => sum + attempt.score, 0) /
      attempts.length;
    return {
      averageScore,
      totalAttempts: attempts.length,
      improvement: this.calculateImprovement(attempts),
    };
  }

  private async calculateStudyStreak(user_id: string): Promise<number> {
    const events = await this.studyEventRepository.find({
      where: { user_id },
      order: { timestamp: 'DESC' },
    });

    let streak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    for (const event of events) {
      const eventDate = new Date(event.timestamp);
      eventDate.setHours(0, 0, 0, 0);

      if (eventDate.getTime() === currentDate.getTime()) {
        continue;
      }

      if (eventDate.getTime() === currentDate.getTime() - 86400000) {
        streak++;
        currentDate = eventDate;
      } else {
        break;
      }
    }

    return streak;
  }

  private async extractFeatures(user_id: string): Promise<number[]> {
    const [metrics, patterns] = await Promise.all([
      this.calculatePerformanceMetrics(user_id),
      this.analyzeStudyPatterns(user_id),
    ]);

    return [
      metrics.successRate,
      metrics.averageInterval,
      patterns.consistencyScore,
      metrics.quizPerformance.averageScore,
      metrics.studyStreak,
      // Add more features as needed
    ];
  }

  private calculateConfidence(features: number[]): number {
    // Implement confidence calculation based on feature variance
    return 0.8; // Placeholder
  }

  private calculateRecommendedStudyTime(features: number[]): number {
    // Implement study time recommendation based on features
    return 60; // Placeholder (minutes)
  }

  private analyzePreferredStudyTimes(events: StudyEvent[]): any {
    // Implement study time pattern analysis
    return {
      morning: 0.3,
      afternoon: 0.4,
      evening: 0.3,
    };
  }

  private analyzeStudyDuration(events: StudyEvent[]): any {
    // Implement study duration analysis
    return {
      averageDuration: 45,
      longestSession: 120,
      shortestSession: 15,
    };
  }

  private async analyzePerformanceByTopic(user_id: string): Promise<any> {
    // Implement topic-based performance analysis
    return {
      anatomy: 0.85,
      physiology: 0.75,
      pathology: 0.65,
    };
  }

  private calculateConsistencyScore(events: StudyEvent[]): number {
    // Implement consistency score calculation
    return 0.7; // Placeholder
  }

  private generateStudySchedule(metrics: any, patterns: any): any {
    // Implement study schedule generation
    return {
      recommendedTimes: ['09:00', '15:00', '20:00'],
      duration: 45,
      frequency: 'daily',
    };
  }

  private identifyFocusAreas(metrics: any): string[] {
    // Implement focus area identification
    return ['Topic A', 'Topic B'];
  }

  private generateImprovementTips(
    metrics: any,
    patterns: any,
    predictions: any,
  ): string[] {
    // Implement improvement tips generation
    return [
      'Focus on reviewing difficult topics',
      'Increase study consistency',
      'Take more practice quizzes',
    ];
  }

  private calculateImprovement(attempts: QuizAttempt[]): number {
    if (attempts.length < 2) return 0;
    const recentAttempts = attempts.slice(-5);
    const improvement =
      recentAttempts[recentAttempts.length - 1].score - recentAttempts[0].score;
    return improvement;
  }
}
