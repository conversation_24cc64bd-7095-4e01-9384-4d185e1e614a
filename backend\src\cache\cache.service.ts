// src/cache/cache.service.ts
import { Inject, Injectable } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CacheService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async get<T>(key: string): Promise<T | undefined> {
    const value = await this.cacheManager.get<T>(key);
    return value ?? undefined;
  }

  async clear(prefix: string): Promise<void> {
    // This is the safer approach - clear the entire cache
    // Redis-specific key deletion would require direct Redis client access
    await this.cacheManager.clear();
  }

  async mget<T>(keys: string[]): Promise<(T | undefined)[]> {
    // Handle the case where mget might not be supported by the store
    try {
      const values = await this.cacheManager.mget<T>(keys);
      return values.map((value) => (value !== null ? value : undefined));
    } catch (error) {
      // Fallback to individual gets if mget is not supported
      return Promise.all(keys.map(async (key) => this.get<T>(key)));
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    await this.cacheManager.set(key, value, ttl);
  }

  async delete(key: string): Promise<void> {
    await this.cacheManager.del(key);
  }

  async reset(): Promise<void> {
    await this.cacheManager.clear();
  }
  async keys(pattern: string): Promise<string[]> {
    // Note: This requires Redis store. For other stores, you might need a different implementation
    const cacheManager = this.cacheManager as any;
    if (cacheManager.store?.getClient) {
      const client = await cacheManager.store.getClient();
      return client.keys(pattern);
    }
    return [];
  }

  generateKey(prefix: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce<Record<string, any>>((acc, key) => {
        acc[key] = params[key];
        return acc;
      }, {});

    return `${prefix}:${JSON.stringify(sortedParams)}`;
  }
}
