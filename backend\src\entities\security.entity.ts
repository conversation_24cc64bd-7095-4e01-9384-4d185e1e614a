import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('user_security_settings')
export class UserSecuritySettings {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ default: false })
  twoFactorEnabled: boolean;

  @Column({ type: 'text', nullable: true })
  twoFactorSecret: string;

  @Column('text', { array: true, nullable: true })
  backupCodes: string[];

  @Column({ type: 'json', nullable: true })
  securityQuestions: {
    question: string;
    answer: string;
  }[];
  @Column({ default: true })
  emailNotificationsEnabled: boolean;

  @Column({ default: false })
  isEmailVerified: boolean;
  @Column({ type: 'text', nullable: true })
  emailVerificationToken: string | null;

  @Column({ type: 'timestamp', nullable: true })
  emailVerificationExpires: Date | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('user_sessions')
export class UserSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;
  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'text', nullable: true })
  token: string;

  @Column({ nullable: true })
  deviceId: string;

  @Column()
  deviceType: string;

  @Column()
  ipAddress: string;

  @Column({ nullable: true })
  location: string;

  @Column()
  userAgent: string;

  @Column({ type: 'timestamp' })
  lastAccessed: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('security_events')
export class SecurityEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  eventType: string;

  @Column({ type: 'json' })
  eventData: any;

  @Column()
  ipAddress: string;

  @Column({ nullable: true })
  deviceId: string;

  @Column({ nullable: true })
  userAgent: string;

  @CreateDateColumn()
  timestamp: Date;
}
