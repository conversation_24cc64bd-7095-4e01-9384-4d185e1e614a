import React, { useState } from 'react';
import {
  Clock,
  Search,
  Filter,
  Download,
  User,
  LogIn,
  LogOut,
  Edit,
  Lock,
  Mail,
  Shield,
  BookOpen,
  FileText,
} from 'lucide-react';

interface ActivityLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  details: string;
  timestamp: string;
  category: 'auth' | 'profile' | 'content' | 'system';
}

const UserActivityLog: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [timeRange, setTimeRange] = useState<string>('all');

  // Mock activity data
  const activities: ActivityLog[] = [
    {
      id: '1',
      userId: '1',
      userName: 'Dr. <PERSON>',
      action: 'Logged in',
      details: 'Successfully logged in from Chrome browser',
      timestamp: '2024-03-15 10:30 AM',
      category: 'auth',
    },
    {
      id: '2',
      userId: '1',
      userName: '<PERSON>. <PERSON>',
      action: 'Updated Profile',
      details: 'Changed email address',
      timestamp: '2024-03-15 11:15 AM',
      category: 'profile',
    },
    {
      id: '3',
      userId: '2',
      userName: 'Dr. <PERSON>',
      action: 'Accessed Course',
      details: 'Viewed Cardiology Module 3',
      timestamp: '2024-03-15 09:45 AM',
      category: 'content',
    },
  ];

  const getActionIcon = (category: string) => {
    switch (category) {
      case 'auth':
        return <LogIn className="h-5 w-5 text-blue-500" />;
      case 'profile':
        return <User className="h-5 w-5 text-green-500" />;
      case 'content':
        return <BookOpen className="h-5 w-5 text-purple-500" />;
      case 'system':
        return <Shield className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'auth':
        return 'bg-blue-100 text-blue-800';
      case 'profile':
        return 'bg-green-100 text-green-800';
      case 'content':
        return 'bg-purple-100 text-purple-800';
      case 'system':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredActivities = activities.filter(activity => {
    const matchesSearch =
      activity.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.details.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = selectedCategory ? activity.category === selectedCategory : true;

    return matchesSearch && matchesCategory;
  });

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">User Activity Log</h2>
        <button
          onClick={() => {
            /* Export functionality */
          }}
          className="flex items-center px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          <Download className="w-4 h-4 mr-2" />
          Export Log
        </button>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search activities..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <select
            value={selectedCategory}
            onChange={e => setSelectedCategory(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Categories</option>
            <option value="auth">Authentication</option>
            <option value="profile">Profile</option>
            <option value="content">Content</option>
            <option value="system">System</option>
          </select>
        </div>
        <div>
          <select
            value={timeRange}
            onChange={e => setTimeRange(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>
      </div>

      {/* Activity Timeline */}
      <div className="space-y-6">
        {filteredActivities.map(activity => (
          <div key={activity.id} className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="p-2 rounded-full bg-gray-50">{getActionIcon(activity.category)}</div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900">{activity.userName}</p>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(activity.category)}`}
                >
                  {activity.category}
                </span>
              </div>
              <p className="text-sm text-gray-900 mt-1">{activity.action}</p>
              <p className="text-sm text-gray-500 mt-1">{activity.details}</p>
              <p className="text-xs text-gray-400 mt-2">{activity.timestamp}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="mt-6 flex items-center justify-between">
        <div className="text-sm text-gray-500">
          Showing {filteredActivities.length} of {activities.length} activities
        </div>
        <div className="flex space-x-2">
          <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
            Previous
          </button>
          <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserActivityLog;
