import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { AssessmentAttempt } from './assessment-attempt.entity';
import { Question } from './question.entity';

@Entity('assessment_answers')
export class AssessmentAnswer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'jsonb', nullable: true })
  answer_data: {
    // For multiple choice: selected option IDs
    selected_options?: string[];
    // For text-based answers
    text_answer?: string;
    // For fill in blank: array of answers
    blank_answers?: string[];
    // For matching: pairs
    matches?: { left: string; right: string }[];
    // For ordering: sequence
    sequence?: string[];
  };

  @Column({ type: 'boolean', default: false })
  is_correct: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  points_earned: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  points_possible: number;

  @Column({ type: 'int', default: 0 })
  time_spent_seconds: number;

  @Column({ type: 'int', default: 1 })
  confidence_level: number; // 1-5 scale

  @Column({ type: 'boolean', default: false })
  flagged_for_review: boolean;

  @Column({ type: 'text', nullable: true })
  student_notes: string;

  @Column({ type: 'jsonb', nullable: true })
  grading_details: {
    auto_graded?: boolean;
    manual_override?: boolean;
    grader_comments?: string;
    partial_credit_reason?: string;
    rubric_scores?: {
      criteria: string;
      points_earned: number;
      points_possible: number;
    }[];
  };

  @Column({ type: 'timestamp' })
  answered_at: Date;

  @Column({ type: 'uuid' })
  attempt_id: string;

  @Column({ type: 'uuid' })
  question_id: string;

  @ManyToOne(() => AssessmentAttempt, (attempt) => attempt.answers, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'attempt_id' })
  attempt: AssessmentAttempt;

  @ManyToOne(() => Question, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'question_id' })
  question: Question;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
