import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Course } from './course.entity';
import { StudyGroupMember } from './study-group-member.entity';
import { StudySession } from './study-session.entity';
import { GroupDiscussion } from './group-discussion.entity';

export enum StudyGroupType {
  COURSE_BASED = 'course_based',
  TOPIC_BASED = 'topic_based',
  EXAM_PREP = 'exam_prep',
  RESEARCH = 'research',
  GENERAL = 'general',
}

export enum StudyGroupPrivacy {
  PUBLIC = 'public',
  PRIVATE = 'private',
  INVITE_ONLY = 'invite_only',
}

export enum StudyGroupStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
}

@Entity('study_groups')
export class StudyGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: StudyGroupType,
    default: StudyGroupType.GENERAL,
  })
  type: StudyGroupType;

  @Column({
    type: 'enum',
    enum: StudyGroupPrivacy,
    default: StudyGroupPrivacy.PUBLIC,
  })
  privacy: StudyGroupPrivacy;

  @Column({
    type: 'enum',
    enum: StudyGroupStatus,
    default: StudyGroupStatus.ACTIVE,
  })
  status: StudyGroupStatus;

  @Column({ type: 'int', default: 10 })
  max_members: number;

  @Column({ type: 'int', default: 1 })
  current_member_count: number;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'simple-array', nullable: true })
  study_topics: string[];

  @Column({ type: 'jsonb', nullable: true })
  goals: {
    short_term: string[];
    long_term: string[];
    target_completion_date?: Date;
    success_metrics?: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  schedule: {
    meeting_frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'flexible';
    preferred_days: string[];
    preferred_times: string[];
    timezone: string;
    duration_minutes: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  rules: {
    attendance_policy?: string;
    participation_requirements?: string[];
    communication_guidelines?: string[];
    resource_sharing_rules?: string[];
    conflict_resolution?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  resources: {
    shared_materials: {
      id: string;
      name: string;
      type: 'document' | 'video' | 'link' | 'note';
      url: string;
      uploaded_by: string;
      uploaded_at: Date;
    }[];
    recommended_books: string[];
    useful_links: {
      title: string;
      url: string;
      description?: string;
    }[];
  };

  @Column({ type: 'varchar', length: 500, nullable: true })
  cover_image_url: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  invite_code: string;

  @Column({ type: 'timestamp', nullable: true })
  last_activity: Date;

  @Column({ type: 'uuid', nullable: true })
  course_id: string;

  @Column({ type: 'uuid' })
  created_by: string;

  @ManyToOne(() => Course, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => StudyGroupMember, (member) => member.study_group, { cascade: true })
  members: StudyGroupMember[];

  @OneToMany(() => StudySession, (session) => session.study_group)
  study_sessions: StudySession[];

  @OneToMany(() => GroupDiscussion, (discussion) => discussion.study_group)
  discussions: GroupDiscussion[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
