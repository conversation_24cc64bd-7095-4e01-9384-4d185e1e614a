'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import materialService from '@/services/materialService';
import { Download, FileText, Filter, Search, Upload } from 'lucide-react';

interface Material {
  id: string;
  title: string;
  description?: string;
  type?: string;
  author?: string;
  uploadDate?: string;
  size?: string;
  url?: string;
}

export default function MaterialsPage() {
  const router = useRouter();
  const [materials, setMaterials] = useState<Material[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    const fetchMaterials = async () => {
      try {
        setIsLoading(true);
        const materials = await materialService.getMaterials();
        setMaterials(materials);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching materials:', error);
        setIsLoading(false);

        // Use placeholder data if API fails
        const placeholderMaterials = [
          {
            id: '1',
            title: 'Anatomy Lecture Notes',
            description: 'Comprehensive notes on human anatomy systems.',
            type: 'pdf',
            author: 'Dr. Sarah Johnson',
            uploadDate: '2025-04-01T10:30:00Z',
            size: '2.4 MB',
          },
          {
            id: '2',
            title: 'Physiology Diagrams',
            description: 'Visual diagrams of physiological processes.',
            type: 'image',
            author: 'Dr. Michael Chen',
            uploadDate: '2025-04-02T14:15:00Z',
            size: '5.7 MB',
          },
          {
            id: '3',
            title: 'Clinical Case Studies',
            description: 'Collection of interesting clinical cases for analysis.',
            type: 'doc',
            author: 'Dr. Emily Rodriguez',
            uploadDate: '2025-04-03T09:45:00Z',
            size: '1.2 MB',
          },
          {
            id: '4',
            title: 'Pharmacology Reference Guide',
            description: 'Quick reference for common medications and their effects.',
            type: 'pdf',
            author: 'Dr. James Wilson',
            uploadDate: '2025-04-04T16:20:00Z',
            size: '3.8 MB',
          },
        ];

        setMaterials(placeholderMaterials);
      }
    };

    fetchMaterials();
  }, []);

  const filteredMaterials = materials.filter(material => {
    const matchesSearch =
      material.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.description?.toLowerCase().includes(searchTerm.toLowerCase());

    if (filter === 'all') return matchesSearch;
    return matchesSearch && material.type?.toLowerCase() === filter.toLowerCase();
  });

  const getFileIcon = (type?: string) => {
    switch (type?.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-6 w-6 text-red-500" />;
      case 'doc':
      case 'docx':
        return <FileText className="h-6 w-6 text-blue-500" />;
      case 'image':
      case 'jpg':
      case 'png':
        return <FileText className="h-6 w-6 text-green-500" />;
      default:
        return <FileText className="h-6 w-6 text-gray-500" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Study Materials</h1>
        <div className="flex items-center gap-2">
          <Button variant="primary" size="sm" onClick={() => router.push('/materials/upload')}>
            <Upload className="h-4 w-4 mr-2" />
            Upload Material
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4 mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search materials..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="relative w-48">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Filter className="h-4 w-4 text-gray-400" />
          </div>
          <select
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={filter}
            onChange={e => setFilter(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="pdf">PDF</option>
            <option value="doc">Document</option>
            <option value="image">Image</option>
          </select>
        </div>
      </div>

      {filteredMaterials.length === 0 ? (
        <div className="text-center py-10">
          <FileText className="h-12 w-12 mx-auto text-gray-400" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">No materials found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter to find what you're looking for.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {filteredMaterials.map(material => (
            <Card key={material.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="uppercase">
                    {material.type || 'File'}
                  </Badge>
                  <span className="text-sm text-gray-500">{material.size || ''}</span>
                </div>
                <div className="flex items-center mt-2">
                  {getFileIcon(material.type)}
                  <CardTitle className="text-lg ml-2">{material.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-500 mb-4 line-clamp-2">{material.description}</p>

                <div className="flex items-center justify-between text-sm mb-4">
                  <span className="text-gray-500">By: {material.author || 'Unknown'}</span>
                  <span className="text-gray-500">
                    {material.uploadDate ? formatDate(material.uploadDate) : ''}
                  </span>
                </div>

                <div className="flex justify-end">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => router.push(`/materials/${material.id}`)}
                    className="mr-2"
                  >
                    View
                  </Button>
                  <Button variant="primary" size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
