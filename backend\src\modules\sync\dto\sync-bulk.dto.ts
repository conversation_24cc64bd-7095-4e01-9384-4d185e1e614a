import { IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { QuizAttemptSyncDto } from './quiz-attempt-sync.dto';
import { ProgressSyncDto } from './progress-sync.dto';
import { ApiProperty } from '@nestjs/swagger';

export class SyncBulkDto {
  @ApiProperty({ type: [QuizAttemptSyncDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuizAttemptSyncDto)
  quizAttempts: QuizAttemptSyncDto[];

  @ApiProperty({ type: [ProgressSyncDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProgressSyncDto)
  progress: ProgressSyncDto[];
}
