import { useState } from 'react';

export default function ShareMaterial({
  materialId,
  userId,
}: {
  materialId: string;
  userId: string;
}) {
  const [sharedWithUserId, setSharedWithUserId] = useState('');

  const handleShare = async () => {
    const res = await fetch('/api/materials/share', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify({ materialId, sharedByUserId: userId, sharedWithUserId }),
    });
    if (res.ok) {
      alert('Material shared!');
      setSharedWithUserId('');
    }
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-2">Share Material</h2>
      <input
        value={sharedWithUserId}
        onChange={e => setSharedWithUserId(e.target.value)}
        placeholder="User ID to share with"
        className="w-full p-2 border rounded mb-4"
      />
      <button
        onClick={handleShare}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Share
      </button>
    </div>
  );
}
