import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDate<PERSON>olumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Assessment } from './assessment.entity';
import { QuestionOption } from './question-option.entity';
import { User } from './user.entity';

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  MULTIPLE_SELECT = 'multiple_select',
  TRUE_FALSE = 'true_false',
  SHORT_ANSWER = 'short_answer',
  ESSAY = 'essay',
  FILL_IN_BLANK = 'fill_in_blank',
  MATCHING = 'matching',
  ORDERING = 'ordering',
}

export enum QuestionDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
}

export enum QuestionCategory {
  ANATOMY = 'anatomy',
  PHYSIOLOGY = 'physiology',
  PATHOLOGY = 'pathology',
  PHARMACOLOGY = 'pharmacology',
  MICROBIOLOGY = 'microbiology',
  BIOCHEMISTRY = 'biochemistry',
  CLINICAL = 'clinical',
  ETHICS = 'ethics',
  RESEARCH = 'research',
}

@Entity('questions')
export class Question {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'text' })
  question_text: string;

  @Column({
    type: 'enum',
    enum: QuestionType,
    default: QuestionType.MULTIPLE_CHOICE,
  })
  type: QuestionType;

  @Column({
    type: 'enum',
    enum: QuestionDifficulty,
    default: QuestionDifficulty.MEDIUM,
  })
  difficulty: QuestionDifficulty;

  @Column({
    type: 'enum',
    enum: QuestionCategory,
    default: QuestionCategory.CLINICAL,
  })
  category: QuestionCategory;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 1 })
  points: number;

  @Column({ type: 'text', nullable: true })
  explanation: string;

  @Column({ type: 'text', nullable: true })
  reference: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  image_url: string;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    bloom_taxonomy_level?: string;
    learning_objective?: string;
    clinical_scenario?: string;
    case_context?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  correct_answer: {
    // For multiple choice: option IDs
    option_ids?: string[];
    // For short answer/essay: expected text
    text?: string;
    // For fill in blank: array of correct answers
    blanks?: string[];
    // For matching: pairs
    matches?: { left: string; right: string }[];
    // For ordering: correct sequence
    sequence?: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  grading_criteria: {
    partial_credit?: boolean;
    case_sensitive?: boolean;
    exact_match?: boolean;
    keywords?: string[];
    rubric?: {
      criteria: string;
      points: number;
    }[];
  };

  @Column({ type: 'int', default: 0 })
  usage_count: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  average_score: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  difficulty_index: number; // Calculated based on student performance

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  discrimination_index: number; // How well it differentiates between high/low performers

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Column({ type: 'uuid', nullable: true })
  assessment_id: string;

  @Column({ type: 'uuid' })
  created_by: string;

  @ManyToOne(() => Assessment, (assessment) => assessment.questions, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assessment_id' })
  assessment: Assessment;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => QuestionOption, (option) => option.question, { cascade: true })
  options: QuestionOption[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
