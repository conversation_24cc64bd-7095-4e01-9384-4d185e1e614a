import { Home, BookOpen, Calendar, Target, Award, Users, BarChart3, Zap } from 'lucide-react';

const navigationConfig = [
  { label: 'Dashboard', href: '/dashboard', icon: BarChart3 },
  { label: 'Courses', href: '/courses', icon: BookOpen },
  { label: 'Rapid Review', href: '/rapid-review', icon: Zap, highlight: true },
  { label: 'Schedule', href: '/schedule', icon: Calendar },
  { label: 'Goals', href: '/goals', icon: Target },
  { label: 'Achievements', href: '/achievements', icon: Award },
  { label: 'Study Groups', href: '/study-groups', icon: Users },
];

export default navigationConfig;
