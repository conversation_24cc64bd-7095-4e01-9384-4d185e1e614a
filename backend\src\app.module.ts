// src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from './cache/cache.module';
import { RedisModule } from './redis/redis.module';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';

// Import all modules
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './modules/auth/auth.module';
import { MaterialsModule } from './modules/materials/materials.module';
import { UnitsModule } from './modules/units/units.module';
import { ProgressModule } from './progress/progress.module';
import { QuizModule } from './quiz/quiz.module';
import { NotificationsModule } from './notifications/notifications.module';
import { FeedbackModule } from './feedback/feedback.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { HealthModule } from './health/health.module';
import { AppThrottlerModule } from './modules/throttler/throttler.module';
import { TestModule } from './test/test.module';
import { ChatModule } from './modules/chat/chat.module';
import { CoursesModule } from './modules/courses/courses.module';
import { ClinicalCasesModule } from './modules/clinical-cases/clinical-cases.module';
import { CPDModule } from './modules/cpd/cpd.module';
import { AIModule } from './modules/ai/ai.module';
import { WeeklyDigestModule } from './modules/digest/weekly-digest.module';
import { RolesModule } from './modules/roles/roles.module';
// import { FeaturesModule } from './modules/features/features.module'; // Disabled due to AI dependency

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('POSTGRES_HOST'),
        port: parseInt(configService.get('POSTGRES_PORT', '5432')),
        username: configService.get('POSTGRES_USER'),
        password: String(configService.get('POSTGRES_PASSWORD')),
        database: configService.get('POSTGRES_DB'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: false,  // Disable synchronize to use migrations
        migrations: [__dirname + '/migrations/*{.ts,.js}'],
        migrationsRun: false,  // Disable auto-run migrations for now
        migrationsTableName: 'migrations',
        ssl: false, // Disable SSL for local development
      }),
      inject: [ConfigService],
    }),
    CacheModule,
    // RedisModule,
    UsersModule,
    AuthModule,
    MaterialsModule,
    UnitsModule,
    ProgressModule,
    QuizModule,
    NotificationsModule,
    FeedbackModule,
    AnalyticsModule,
    HealthModule,
    AppThrottlerModule,
    TestModule,
    ChatModule,
    CoursesModule,
    ClinicalCasesModule,
    // CPDModule,
    AIModule,
    // WeeklyDigestModule,
    RolesModule,
    // FeaturesModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}