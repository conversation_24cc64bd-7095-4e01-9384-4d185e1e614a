/* eslint-disable prettier/prettier */
import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuizService } from './quiz.service';
import { QuizController } from './quiz.controller';
import { QuizQuestion } from '../entities/quiz-question.entity';
import { UserResponse } from '../entities/user-response.entity';
import { FeaturesModule } from '../modules/features/features.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([QuizQuestion, UserResponse]),
    FeaturesModule,
  ],
  controllers: [QuizController],
  providers: [QuizService],
  exports: [QuizService],
})
export class QuizModule {}
