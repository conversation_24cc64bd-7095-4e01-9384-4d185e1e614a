/* eslint-disable prettier/prettier */
import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuizService } from './quiz.service';
import { QuizController } from './quiz.controller';
import { QuizQuestion } from '../entities/quiz-question.entity';
import { UserResponse } from '../entities/user-response.entity';
import { UserFeaturesService } from '../modules/features/user-features.service';
import { FeaturesModule } from '../modules/features/features.module';


@Module({
  imports: [TypeOrmModule.forFeature([QuizQuestion, UserResponse]),FeaturesModule],
  controllers: [QuizController],
  providers: [QuizService,UserFeaturesService],
  exports: [QuizService],
  
  
})
export class QuizModule {}
