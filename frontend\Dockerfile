# frontend/Dockerfile
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files (handle both npm and pnpm)
COPY package.json ./
COPY package-lock.json* ./
COPY pnpm-lock.yaml* ./

# Install dependencies (prefer npm as per user preference)
RUN npm install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN npm run build

FROM node:20-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
