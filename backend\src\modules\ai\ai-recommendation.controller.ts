import { Controller, UseGuards } from '@nestjs/common';
import { AIRecommendationService } from './ai-recommendation.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guards';

@Controller('ai-recommendations')
@UseGuards(JwtAuthGuard, RolesGuard)
export class AIRecommendationController {
  constructor(
    private readonly aiRecommendationService: AIRecommendationService,
  ) {}
}
