import { Entity, PrimaryGeneratedColumn, ManyToOne, Column } from 'typeorm'; // Added Column
import { User } from './user.entity';
import { Material } from './materials.entity';

@Entity()
export class MaterialShare {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Material, (material: Material) => material.shares)
  material: Material;

  @ManyToOne(() => User)
  shared_with_user: User;

  @ManyToOne(() => User)
  shared_by_user: User;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;
}
