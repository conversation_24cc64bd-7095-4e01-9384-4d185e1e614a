const { spawn } = require('child_process');
const axios = require('axios');

async function testFrontendSimple() {
  console.log('🚀 Testing Frontend with Circular Dependency Fix...');
  
  console.log('🔧 Starting development server...');
  
  const dev = spawn('pnpm', ['run', 'dev'], {
    cwd: './frontend',
    stdio: 'pipe',
    shell: true
  });
  
  let serverStarted = false;
  let hasError = false;
  let output = '';
  
  dev.stdout.on('data', (data) => {
    const text = data.toString();
    output += text;
    console.log('STDOUT:', text);
    
    if (text.includes('Ready') || text.includes('started server') || text.includes('Local:')) {
      serverStarted = true;
    }
  });
  
  dev.stderr.on('data', (data) => {
    const text = data.toString();
    output += text;
    console.error('STDERR:', text);
    
    if (text.includes('Error') || text.includes('Failed')) {
      hasError = true;
    }
  });
  
  // Wait for server to start or timeout
  const timeout = 45000; // 45 seconds
  const startTime = Date.now();
  
  while (!serverStarted && !hasError && (Date.now() - startTime) < timeout) {
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  if (serverStarted) {
    console.log('✅ Frontend server started successfully');
    
    // Test if the server is accessible
    try {
      await new Promise(resolve => setTimeout(resolve, 3000)); // Wait a bit more
      const response = await axios.get('http://localhost:3000/test-simple', { timeout: 10000 });
      console.log('✅ Frontend test page is accessible');
      console.log(`📊 Status: ${response.status}`);
      
      // Test auth page
      try {
        const authResponse = await axios.get('http://localhost:3000/auth/register', { timeout: 10000 });
        console.log('✅ Auth register page is accessible');
        console.log(`📊 Auth Status: ${authResponse.status}`);
      } catch (authError) {
        console.log('⚠️ Auth page has issues:', authError.message);
      }
      
    } catch (error) {
      console.log('⚠️ Server started but pages not accessible:', error.message);
    }
  } else if (hasError) {
    console.log('❌ Frontend server failed to start due to errors');
  } else {
    console.log('❌ Frontend server failed to start within timeout');
  }
  
  console.log('\n📋 Full Output:');
  console.log(output);
  
  // Kill the process
  dev.kill();
  
  return { serverStarted, hasError, output };
}

testFrontendSimple().catch(console.error);
