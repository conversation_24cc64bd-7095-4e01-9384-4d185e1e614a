#!/usr/bin/env node

/**
 * Version Checking Script
 * Checks version consistency across all services in the monorepo
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function readPackageJson(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    return null;
  }
}

function readPythonVersion(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }
    const content = fs.readFileSync(filePath, 'utf8');
    const match = content.match(/__version__\s*=\s*["']([^"']+)["']/);
    return match ? match[1] : null;
  } catch (error) {
    return null;
  }
}

function checkVersions() {
  log('🔍 Checking version consistency across services...', 'cyan');
  
  // Read root package.json to get the master version
  const rootPackagePath = path.join(process.cwd(), 'package.json');
  const rootPackage = readPackageJson(rootPackagePath);
  
  if (!rootPackage) {
    log('❌ Failed to read root package.json', 'red');
    process.exit(1);
  }
  
  const masterVersion = rootPackage.version;
  log(`📦 Master version: ${masterVersion}`, 'bright');
  
  const versions = {
    root: masterVersion,
    frontend: null,
    backend: null,
    analytics: null
  };
  
  let allConsistent = true;
  
  // Check frontend version
  const frontendPackagePath = path.join(process.cwd(), 'frontend', 'package.json');
  const frontendPackage = readPackageJson(frontendPackagePath);
  if (frontendPackage) {
    versions.frontend = frontendPackage.version;
    if (frontendPackage.version === masterVersion) {
      log(`✅ Frontend: ${frontendPackage.version}`, 'green');
    } else {
      log(`❌ Frontend: ${frontendPackage.version} (expected: ${masterVersion})`, 'red');
      allConsistent = false;
    }
  } else {
    log(`⚠️ Frontend package.json not found`, 'yellow');
  }
  
  // Check backend version
  const backendPackagePath = path.join(process.cwd(), 'backend', 'package.json');
  const backendPackage = readPackageJson(backendPackagePath);
  if (backendPackage) {
    versions.backend = backendPackage.version;
    if (backendPackage.version === masterVersion) {
      log(`✅ Backend: ${backendPackage.version}`, 'green');
    } else {
      log(`❌ Backend: ${backendPackage.version} (expected: ${masterVersion})`, 'red');
      allConsistent = false;
    }
  } else {
    log(`⚠️ Backend package.json not found`, 'yellow');
  }
  
  // Check Python analytics version
  const pythonVersionPath = path.join(process.cwd(), 'backend', 'python_analytics', '__init__.py');
  const pythonVersion = readPythonVersion(pythonVersionPath);
  if (pythonVersion) {
    versions.analytics = pythonVersion;
    if (pythonVersion === masterVersion) {
      log(`✅ Analytics: ${pythonVersion}`, 'green');
    } else {
      log(`❌ Analytics: ${pythonVersion} (expected: ${masterVersion})`, 'red');
      allConsistent = false;
    }
  } else {
    log(`⚠️ Analytics version not found in __init__.py`, 'yellow');
  }
  
  // Check version.json if it exists
  const versionInfoPath = path.join(process.cwd(), 'version.json');
  if (fs.existsSync(versionInfoPath)) {
    try {
      const versionInfo = JSON.parse(fs.readFileSync(versionInfoPath, 'utf8'));
      log(`\n📄 Version info file:`, 'cyan');
      log(`  Version: ${versionInfo.version}`, 'blue');
      log(`  Last updated: ${new Date(versionInfo.timestamp).toLocaleString()}`, 'blue');
      
      if (versionInfo.version !== masterVersion) {
        log(`  ❌ Version info file is outdated`, 'red');
        allConsistent = false;
      }
    } catch (error) {
      log(`⚠️ Error reading version.json: ${error.message}`, 'yellow');
    }
  }
  
  // Summary
  log('\n📊 Version Summary:', 'cyan');
  log(`Root:      ${versions.root}`, 'bright');
  log(`Frontend:  ${versions.frontend || 'N/A'}`, versions.frontend === masterVersion ? 'green' : 'red');
  log(`Backend:   ${versions.backend || 'N/A'}`, versions.backend === masterVersion ? 'green' : 'red');
  log(`Analytics: ${versions.analytics || 'N/A'}`, versions.analytics === masterVersion ? 'green' : 'red');
  
  if (allConsistent) {
    log('\n🎉 All versions are consistent!', 'green');
    process.exit(0);
  } else {
    log('\n❌ Version inconsistencies detected!', 'red');
    log('💡 Run "npm run version:sync" to fix inconsistencies', 'yellow');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  checkVersions();
}

module.exports = { checkVersions };
