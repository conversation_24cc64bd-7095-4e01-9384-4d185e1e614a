import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiError, ApiResponse, RequestOptions } from '@/types/api';
import { rateLimiter } from './rateLimiter';
import Cookies from 'js-cookie';

class ApiService {
  private static instance: ApiService;
  private api: AxiosInstance;

  private constructor() {
    this.api = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL,
      timeout: 30000,
      withCredentials: true,
    });

    this.setupInterceptors();
  }

  static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      async (config) => {
        try {
          // Check rate limit
          const endpoint = config.url?.replace(/^\//, '') || '';
          const isAllowed = await rateLimiter.checkLimit(endpoint);
          
          if (!isAllowed) {
            const resetTime = rateLimiter.getResetTime(endpoint);
            const remainingTime = Math.ceil((resetTime - Date.now()) / 1000);
            throw new Error(`Rate limit exceeded. Please try again in ${remainingTime} seconds.`);
          }

          const token = Cookies.get('access_token');
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          return config;
        } catch (error) {
          return Promise.reject(error);
        }
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle token refresh
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await authService.getAccessToken();
            return this.api(originalRequest);
          } catch (refreshError) {
            return Promise.reject(refreshError);
          }
        }

        // Transform error response
        const apiError: ApiError = {
          code: error.response?.data?.code || 'UNKNOWN_ERROR',
          message: error.response?.data?.message || error.message || 'An unexpected error occurred',
          details: error.response?.data?.details,
          status: error.response?.status || 500,
        };

        return Promise.reject(apiError);
      }
    );
  }

  async get<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    const config: AxiosRequestConfig = {
      headers: options?.headers,
      params: options?.params,
      timeout: options?.timeout,
      withCredentials: options?.withCredentials,
    };

    const response: AxiosResponse<ApiResponse<T>> = await this.api.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    const config: AxiosRequestConfig = {
      headers: options?.headers,
      params: options?.params,
      timeout: options?.timeout,
      withCredentials: options?.withCredentials,
    };

    const response: AxiosResponse<ApiResponse<T>> = await this.api.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    const config: AxiosRequestConfig = {
      headers: options?.headers,
      params: options?.params,
      timeout: options?.timeout,
      withCredentials: options?.withCredentials,
    };

    const response: AxiosResponse<ApiResponse<T>> = await this.api.put(url, data, config);
    return response.data;
  }

  async patch<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    const config: AxiosRequestConfig = {
      headers: options?.headers,
      params: options?.params,
      timeout: options?.timeout,
      withCredentials: options?.withCredentials,
    };

    const response: AxiosResponse<ApiResponse<T>> = await this.api.patch(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    const config: AxiosRequestConfig = {
      headers: options?.headers,
      params: options?.params,
      timeout: options?.timeout,
      withCredentials: options?.withCredentials,
    };

    const response: AxiosResponse<ApiResponse<T>> = await this.api.delete(url, config);
    return response.data;
  }
}

const apiService = ApiService.getInstance();
export { apiService };
export default apiService;
