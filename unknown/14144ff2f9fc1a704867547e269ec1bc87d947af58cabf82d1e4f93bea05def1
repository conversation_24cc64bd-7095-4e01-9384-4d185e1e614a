# 🤝 Contributing to MedTrack Hub

Thank you for your interest in contributing to MedTrack Hub! This guide will help you get started.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)

## 📜 Code of Conduct

This project adheres to a code of conduct. By participating, you are expected to uphold this code.

## 🚀 Getting Started

### Prerequisites

- Node.js 20+
- Python 3.11+
- Docker and Docker Compose
- Git

### Development Setup

1. **Fork and clone the repository**:
   ```bash
   git clone https://github.com/your-username/medical.git
   cd medical
   ```

2. **Set up environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development environment**:
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

4. **Verify setup**:
   ```bash
   ./scripts/health-check.sh
   ```

## 🔄 Development Workflow

### Branch Naming Convention

- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `hotfix/description` - Critical fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### Commit Message Convention

We use [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

[optional body]

[optional footer]
```

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

**Examples**:
```bash
feat(auth): add JWT refresh token functionality
fix(frontend): resolve navigation menu overflow issue
docs(api): update authentication endpoint documentation
```

## 📝 Coding Standards

### TypeScript/JavaScript

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Use meaningful variable and function names
- Add JSDoc comments for public APIs

```typescript
/**
 * Authenticates a user with email and password
 * @param email - User's email address
 * @param password - User's password
 * @returns Promise resolving to authentication result
 */
async function authenticateUser(email: string, password: string): Promise<AuthResult> {
  // Implementation
}
```

### Python

- Follow PEP 8 style guide
- Use type hints
- Add docstrings for functions and classes
- Use meaningful variable names

```python
def calculate_learning_score(
    user_id: int, 
    session_data: Dict[str, Any]
) -> float:
    """
    Calculate learning score based on session data.
    
    Args:
        user_id: The user's unique identifier
        session_data: Dictionary containing session metrics
        
    Returns:
        Learning score between 0.0 and 1.0
    """
    # Implementation
```

### File Organization

#### Frontend Structure
```
src/
├── app/                 # Next.js App Router pages
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components
│   └── features/       # Feature-specific components
├── lib/                # Utilities and configurations
├── hooks/              # Custom React hooks
├── types/              # TypeScript type definitions
└── styles/             # Global styles
```

#### Backend Structure
```
src/
├── auth/               # Authentication module
├── users/              # User management
├── common/             # Shared utilities
├── config/             # Configuration
├── database/           # Database setup
└── modules/            # Feature modules
```

## 🧪 Testing Guidelines

### Frontend Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- UserComponent.test.tsx
```

**Test Structure**:
```typescript
describe('UserComponent', () => {
  it('should render user information correctly', () => {
    // Test implementation
  });

  it('should handle loading state', () => {
    // Test implementation
  });
});
```

### Backend Testing

```bash
# Run all tests
npm run test:all

# Run unit tests
npm run test

# Run e2e tests
npm run test:e2e
```

### Python Testing

```bash
# Run tests
python -m pytest

# Run with coverage
python -m pytest --cov=analytics
```

### Test Requirements

- Write tests for new features
- Maintain test coverage above 80%
- Include both unit and integration tests
- Test error scenarios
- Mock external dependencies

## 🔍 Code Review Process

### Before Submitting

1. **Run all tests**: Ensure all tests pass
2. **Check linting**: Fix all linting errors
3. **Update documentation**: Update relevant docs
4. **Test manually**: Verify changes work as expected

### Pull Request Checklist

- [ ] Tests added/updated
- [ ] Documentation updated
- [ ] No linting errors
- [ ] All CI checks pass
- [ ] Descriptive PR title and description
- [ ] Screenshots for UI changes

## 📤 Pull Request Process

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**:
   - Follow coding standards
   - Add tests
   - Update documentation

3. **Commit your changes**:
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

4. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```

5. **Create a Pull Request**:
   - Use descriptive title
   - Fill out PR template
   - Link related issues
   - Add screenshots for UI changes

### PR Review Process

1. **Automated checks**: CI/CD pipeline runs
2. **Code review**: Team members review code
3. **Testing**: Manual testing if needed
4. **Approval**: At least one approval required
5. **Merge**: Squash and merge to main

## 🐛 Issue Reporting

### Bug Reports

Use the bug report template and include:

- **Description**: Clear description of the bug
- **Steps to reproduce**: Detailed steps
- **Expected behavior**: What should happen
- **Actual behavior**: What actually happens
- **Environment**: OS, browser, versions
- **Screenshots**: If applicable

### Feature Requests

Use the feature request template and include:

- **Problem**: What problem does this solve?
- **Solution**: Proposed solution
- **Alternatives**: Alternative solutions considered
- **Additional context**: Any other relevant information

## 🏷️ Labels and Milestones

### Issue Labels

- `bug`: Something isn't working
- `enhancement`: New feature or request
- `documentation`: Documentation improvements
- `good first issue`: Good for newcomers
- `help wanted`: Extra attention needed
- `priority:high`: High priority
- `priority:medium`: Medium priority
- `priority:low`: Low priority

### Component Labels

- `frontend`: Frontend-related
- `backend`: Backend-related
- `analytics`: Analytics service
- `docker`: Docker-related
- `ci/cd`: CI/CD pipeline

## 🎯 Development Tips

### Performance

- Use React.memo for expensive components
- Implement proper database indexing
- Use caching where appropriate
- Optimize Docker images

### Security

- Never commit secrets
- Validate all inputs
- Use parameterized queries
- Implement proper authentication

### Accessibility

- Use semantic HTML
- Add proper ARIA labels
- Ensure keyboard navigation
- Test with screen readers

## 📚 Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [NestJS Documentation](https://docs.nestjs.com/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Python Style Guide](https://pep8.org/)

## 🆘 Getting Help

- **Documentation**: Check the `docs/` directory
- **Issues**: Search existing issues
- **Discussions**: Use GitHub Discussions
- **Discord**: Join our Discord server (if available)

Thank you for contributing to MedTrack Hub! 🎉
