import {
  <PERSON>tity,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Material } from './materials.entity';

@Entity('weekly_digests')
export class WeeklyDigest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  user: User;

  @Column({ type: 'date' })
  week_start_date: Date;

  @Column({ type: 'date' })
  week_end_date: Date;

  @Column({ type: 'boolean', default: false })
  is_sent: boolean;

  @Column({ type: 'timestamp', nullable: true })
  sent_at: Date;

  @Column({
    type: 'enum',
    enum: ['pending', 'read', 'archived'],
    default: 'pending',
  })
  status: 'pending' | 'read' | 'archived';

  @Column({ type: 'timestamp', nullable: true })
  read_at: Date;

  @Column({ type: 'jsonb' })
  content: {
    new_materials: {
      material: Material;
      relevance: number;
    }[];
    recommended_topics: {
      topic: string;
      reason: string;
      materials: Material[];
    }[];
    cpd_progress: {
      points_earned: number;
      points_required: number;
      activities: {
        type: string;
        points: number;
        date: Date;
      }[];
    };
    upcoming_deadlines: {
      type: string;
      description: string;
      due_date: Date;
    }[];
  };

  @Column({ type: 'jsonb', nullable: true })
  delivery_status: {
    email?: {
      sent: boolean;
      error?: string;
      timestamp?: Date;
    };
    push?: {
      sent: boolean;
      error?: string;
      timestamp?: Date;
    };
  };

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
