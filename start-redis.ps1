# PowerShell script to start Redis using Docker

Write-Host "Starting Redis server using Docker..." -ForegroundColor Green

# Check if <PERSON><PERSON> is running
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop and try again." -ForegroundColor Red
    exit 1
}

# Stop any existing Redis container
Write-Host "Stopping any existing Redis container..." -ForegroundColor Yellow
docker stop medtrack-redis 2>$null
docker rm medtrack-redis 2>$null

# Start Redis container
Write-Host "Starting Redis container..." -ForegroundColor Yellow
$redisPassword = "MedTrack2024SecureRedisPassword!"

docker run -d `
  --name medtrack-redis `
  -p 6379:6379 `
  redis:7-alpine `
  redis-server --requirepass $redisPassword

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Redis started successfully on port 6379" -ForegroundColor Green
    Write-Host "Password: $redisPassword" -ForegroundColor Cyan
    
    # Test Redis connection
    Start-Sleep -Seconds 2
    Write-Host "Testing Redis connection..." -ForegroundColor Yellow
    
    $testResult = docker exec medtrack-redis redis-cli -a $redisPassword ping 2>$null
    if ($testResult -eq "PONG") {
        Write-Host "✅ Redis connection test successful!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Redis started but connection test failed" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Failed to start Redis container" -ForegroundColor Red
    exit 1
}

Write-Host "`nRedis is now running and ready for the backend!" -ForegroundColor Blue
