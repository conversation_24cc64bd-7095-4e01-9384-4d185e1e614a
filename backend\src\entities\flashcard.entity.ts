import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { QuizQuestion } from './quiz-question.entity';

@Entity('flashcards')
export class Flashcard {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  user_id: string;

  @ManyToOne(() => User, (user) => user.flashcards)
  user: User;

  @ManyToOne(() => QuizQuestion)
  question: QuizQuestion;

  @Column('float')
  ease_factor: number;

  @Column('int')
  interval: number;

  @Column('timestamp with time zone')
  next_review: Date;

  @Column('int')
  correct_streak: number;

  @Column('timestamp with time zone')
  last_review: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
