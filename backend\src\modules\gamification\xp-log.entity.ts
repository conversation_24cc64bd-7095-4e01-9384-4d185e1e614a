import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
} from 'typeorm';
import { User } from '../../entities/user.entity';

@Entity('xp_logs')
export class XPLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, { eager: true })
  user: User;

  @Column('int')
  amount: number;

  @Column()
  reason: string;

  @CreateDateColumn()
  createdAt: Date;
}
