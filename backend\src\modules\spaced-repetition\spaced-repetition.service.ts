// src/modules/spaced-repetition/spaced-repetition.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Material } from '../../entities/materials.entity';
import { Progress } from '../../entities/progress.entity';

interface FlashcardItem {
  id: number;
  content: string;
  answer: string;
  ease_factor: number;
  interval: number;
  next_review_date: Date;
}

@Injectable()
export class SpacedRepetitionService {
  constructor(
    @InjectRepository(Progress)
    private progressRepository: Repository<Progress>,
    @InjectRepository(Material)
    private materialRepository: Repository<Material>,
  ) {}

  // SM-2 Algorithm implementation for spaced repetition
  calculateNextReview(quality: number, item: FlashcardItem): FlashcardItem {
    // Quality is from 0 to 5 where:
    // 0 = complete blackout, 5 = perfect recall

    // Update ease factor
    let ease_factor =
      item.ease_factor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02));
    if (ease_factor < 1.3) ease_factor = 1.3;

    let interval: number;

    if (quality < 3) {
      // If quality is less than 3, reset interval
      interval = 1;
    } else {
      // Calculate new interval
      if (item.interval === 0) {
        interval = 1;
      } else if (item.interval === 1) {
        interval = 6;
      } else {
        interval = Math.round(item.interval * ease_factor);
      }
    }

    // Calculate next review date
    const next_review_date = new Date();
    next_review_date.setDate(next_review_date.getDate() + interval);

    return {
      ...item,
      ease_factor,
      interval,
      next_review_date,
    };
  }

  async addFlashcard(
    userId: number,
    content: string,
    answer: string,
  ): Promise<FlashcardItem> {
    // Create a new flashcard material
    const material = this.materialRepository.create({
      user: { id: userId.toString() },
      content,
      answer,
    });
    const savedMaterial = await this.materialRepository.save(material);

    // Create initial progress for the flashcard
    const progress = this.progressRepository.create({
      user: { id: userId.toString() },
      material: savedMaterial,
    });
    await this.progressRepository.save(progress);

    return {
      id: Number(progress.id),
      content,
      answer,
      ease_factor: 2.5,
      interval: 0,
      next_review_date: new Date(),
    };
  }

  async calculateNextReviewDate(
    userId: string,
    materialId: string,
  ): Promise<Date> {
    // Implementation needed
    return new Date();
  }

  async getDueReviews(userId: string): Promise<Progress[]> {
    // Implementation needed
    return [];
  }

  async getDueFlashcards(
    userId: number,
    limit: number = 20,
  ): Promise<FlashcardItem[]> {
    // Get materials that are due for review based on user's progress
    const today = new Date();

    // Query would depend on your actual database schema
    // This is a simplified example
    const dueItems = await this.progressRepository
      .createQueryBuilder('progress')
      .innerJoinAndSelect('progress.material', 'material')
      .where('progress.userId = :userId', { userId })
      .andWhere('progress.next_review_date <= :today', { today })
      .orderBy('progress.next_review_date', 'ASC')
      .take(limit)
      .getMany();

    // Transform to flashcard items
    return dueItems.map((item: any) => ({
      id: Number(item.id),
      content: item.material.content,
      answer: item.material.answer || '',
      ease_factor: item.ease_factor || 2.5, // Default ease factor
      interval: item.interval || 0,
      next_review_date: item.next_review_date,
    }));
  }

  async getFlashcardById(id: number): Promise<FlashcardItem> {
    // Get a specific flashcard by ID
    const flashcard = await this.progressRepository.findOne({
      where: { id: id.toString() },
      relations: ['material'],
    });

    if (!flashcard) {
      throw new Error('Flashcard not found');
    }

    return {
      id: Number(flashcard.id),
      content: flashcard.material.content,
      answer: flashcard.material.answer || '',
      ease_factor: flashcard.ease_factor || 2.5,
      interval: flashcard.interval || 0,
      next_review_date: flashcard.next_review_date,
    };
  }

  async recordFlashcardResponse(
    flashcardId: number,
    userId: number,
    quality: number,
  ): Promise<void> {
    // Get the current flashcard progress
    const progress = await this.progressRepository.findOne({
      where: { id: flashcardId.toString(), user: { id: userId.toString() } },
    });

    if (!progress) {
      throw new Error('Flashcard progress not found');
    }

    // Calculate new parameters
    const updatedItem = this.calculateNextReview(quality, {
      id: Number(progress.id),
      content: '', // Not needed for calculation
      answer: '', // Not needed for calculation
      ease_factor: progress.ease_factor || 2.5,
      interval: progress.interval || 0,
      next_review_date: progress.next_review_date,
    });

    // Update the progress in the database
    await this.progressRepository.update(flashcardId, {
      ease_factor: updatedItem.ease_factor,
      interval: updatedItem.interval,
      next_review_date: updatedItem.next_review_date,
      last_reviewed_at: new Date(),
    });
  }

  async getFlashcardMaterials(userId: number): Promise<Material[]> {
    // Get all materials for the user
    return this.materialRepository.find({
      where: { user: { id: userId.toString() } },
    });
  }

  async addMaterial(
    userId: number,
    content: string,
    answer: string,
  ): Promise<Material> {
    // Create a new material
    const material = this.materialRepository.create({
      user: { id: userId.toString() },
      content,
      answer,
    });
    const savedMaterial = await this.materialRepository.save(material);
    return Array.isArray(savedMaterial) ? savedMaterial[0] : savedMaterial;
  }

  async updateMaterial(
    id: number,
    content: string,
    answer: string,
  ): Promise<Material> {
    // Update an existing material
    await this.materialRepository.update(id, { content, answer });
    const material = await this.materialRepository.findOne({
      where: { id: id.toString() },
    });
    if (!material) {
      throw new Error('Material not found');
    }
    return material;
  }

  async deleteMaterial(id: number): Promise<void> {
    // Delete a material
    await this.materialRepository.delete(id);
  }

  async getProgress(userId: number): Promise<Progress[]> {
    // Get all progress for the user
    return this.progressRepository.find({
      where: { user: { id: userId.toString() } },
    });
  }

  async addProgress(userId: number, materialId: number): Promise<Progress> {
    // Create a new progress entry
    const progress = this.progressRepository.create({
      user: { id: userId.toString() },
      material: { id: materialId.toString() },
    });
    const savedProgress = await this.progressRepository.save(progress);
    return Array.isArray(savedProgress) ? savedProgress[0] : savedProgress;
  }

  async updateProgress(id: number, materialId: number): Promise<Progress> {
    // Update an existing progress entry
    await this.progressRepository.update(id.toString(), {
      material: { id: materialId.toString() },
    });
    const progress = await this.progressRepository.findOne({
      where: { id: id.toString() },
    });
    if (!progress) {
      throw new Error('Progress not found');
    }
    return progress;
  }
}
