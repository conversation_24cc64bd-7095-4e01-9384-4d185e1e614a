# Build stage
FROM python:3.11-slim AS builder

WORKDIR /app

# Install system dependencies for building (including TensorFlow requirements)
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    python3-dev \
    libhdf5-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy requirements file
COPY requirements.txt .

# Create virtual environment and install dependencies with optimizations
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir --upgrade pip setuptools wheel \
    && pip install --no-cache-dir --no-compile -r requirements.txt \
    && find /opt/venv -name "*.pyc" -delete \
    && find /opt/venv -name "__pycache__" -type d -exec rm -rf {} + || true

# Production stage
FROM python:3.11-slim AS runner

WORKDIR /app

# Install only essential runtime dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    curl \
    tini \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && rm -rf /var/cache/apt/*

# Create non-root user for security
RUN groupadd --gid 1001 python \
    && useradd --uid 1001 --gid python --shell /bin/bash --create-home python

# Copy virtual environment from builder
COPY --from=builder --chown=python:python /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy source code (excluding unnecessary files)
COPY --chown=python:python main.py ./
COPY --chown=python:python analytics/ ./analytics/
COPY --chown=python:python auth/ ./auth/
COPY --chown=python:python config.py ./
COPY --chown=python:python __init__.py ./
COPY --chown=python:python scripts/ ./scripts/

# Create necessary directories
RUN mkdir -p /app/models /app/logs \
    && chown -R python:python /app/models /app/logs

# Switch to non-root user
USER python

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PORT=5000 \
    PYTHONPATH=/app \
    PYTHONOPTIMIZE=1

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose the port
EXPOSE 5000

# Use tini as init system for proper signal handling
ENTRYPOINT ["tini", "--"]

# Start the application with optimizations
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5000", "--workers", "1", "--access-log"]