import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Course } from './course.entity';
import { Unit } from './unit.entity';
import { User } from './user.entity';
import { CaseAttempt } from './case-attempt.entity';

export enum CaseComplexity {
  SIMPLE = 'simple',
  MODERATE = 'moderate',
  COMPLEX = 'complex',
  EXPERT = 'expert',
}

export enum CaseSpecialty {
  INTERNAL_MEDICINE = 'internal_medicine',
  CARDIOLOGY = 'cardiology',
  NEUROLOGY = 'neurology',
  PEDIATRICS = 'pediatrics',
  SURGERY = 'surgery',
  EMERGENCY = 'emergency',
  PSYCHIATRY = 'psychiatry',
  OBSTETRICS = 'obstetrics',
  DERMATOLOGY = 'dermatology',
  RADIOLOGY = 'radiology',
}

export enum CaseStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

@Entity('clinical_cases')
export class ClinicalCase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: CaseComplexity,
    default: CaseComplexity.MODERATE,
  })
  complexity: CaseComplexity;

  @Column({
    type: 'enum',
    enum: CaseSpecialty,
    default: CaseSpecialty.INTERNAL_MEDICINE,
  })
  specialty: CaseSpecialty;

  @Column({
    type: 'enum',
    enum: CaseStatus,
    default: CaseStatus.DRAFT,
  })
  status: CaseStatus;

  @Column({ type: 'int', default: 30 })
  estimated_duration_minutes: number;

  @Column({ type: 'simple-array', nullable: true })
  learning_objectives: string[];

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'jsonb' })
  patient_info: {
    age: number;
    gender: string;
    chief_complaint: string;
    history_of_present_illness: string;
    past_medical_history?: string[];
    medications?: string[];
    allergies?: string[];
    social_history?: string;
    family_history?: string;
    vital_signs?: {
      temperature?: string;
      blood_pressure?: string;
      heart_rate?: string;
      respiratory_rate?: string;
      oxygen_saturation?: string;
      weight?: string;
      height?: string;
    };
  };

  @Column({ type: 'jsonb' })
  case_flow: {
    sections: {
      id: string;
      type:
        | 'history'
        | 'physical_exam'
        | 'diagnostics'
        | 'treatment'
        | 'follow_up';
      title: string;
      content: string;
      order: number;
      is_unlocked_initially?: boolean;
      unlock_conditions?: string[];
      points?: number;
    }[];
    decision_points: {
      id: string;
      section_id: string;
      question: string;
      options: {
        id: string;
        text: string;
        is_correct: boolean;
        feedback: string;
        consequences?: string;
        unlocks_sections?: string[];
        points?: number;
      }[];
      type: 'single_choice' | 'multiple_choice' | 'ranking';
      required: boolean;
    }[];
  };

  @Column({ type: 'jsonb', nullable: true })
  diagnostic_criteria: {
    differential_diagnoses: {
      diagnosis: string;
      probability: number;
      supporting_evidence: string[];
      contradicting_evidence: string[];
    }[];
    final_diagnosis: string;
    key_findings: string[];
    red_flags: string[];
  };

  @Column({ type: 'uuid', nullable: true })
  course_id: string;

  @Column({ type: 'uuid', nullable: true })
  unit_id: string;

  @Column({ type: 'uuid' })
  created_by: string;

  @ManyToOne(() => Course, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @ManyToOne(() => Unit, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'unit_id' })
  unit: Unit;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  // Alias for creator to match User entity relationship
  get author(): User {
    return this.creator;
  }

  @OneToMany(() => CaseAttempt, (attempt) => attempt.clinical_case)
  attempts: CaseAttempt[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
