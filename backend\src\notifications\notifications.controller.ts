import { Controller, Get, Post, Body, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../modules/auth/jwt-auth.guard';
import { NotificationsService } from './notifications.service';

@Controller('notifications')
@UseGuards(JwtAuthGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  create(@Body() body: { userId: string; message: string }) {
    return this.notificationsService.create(body.userId, body.message);
  }

  @Get()
  findByUser(@Body('userId') userId: string) {
    return this.notificationsService.findByUser(userId);
  }
}
