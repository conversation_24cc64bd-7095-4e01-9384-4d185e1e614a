import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { AIModel } from './model';
import { Material } from '../../entities/materials.entity';
import { LearningSuggestion } from '../../entities/learning-suggestion.entity';
import { RedisService } from '../redis/redis.service';
import { join } from 'path';
import { User } from '../../entities/user.entity';

interface Recommendation {
  materialId: string;
  score: number;
  reason: string;
}

interface UserFeatures {
  completedMaterials: number;
  averageScore: number;
  studyTime: number;
  preferredCategories: string[];
  lastActivity: Date;
  difficultyPreference: number;
  learningStyle: number;
  engagementLevel: number;
  quizPerformance: number;
  materialInteraction: number;
}

interface LearningHistoryItem {
  score?: number;
  duration?: number;
  category: string;
  timestamp: string;
  difficulty?: number;
  engagement?: number;
  type?: string;
  interactionScore?: number;
}

interface UserPreferences {
  learningStyle: number;
}

@Injectable()
export class AIService {
  private readonly logger = new Logger(AIService.name);
  private model: AIModel;
  private readonly modelPath: string;
  private readonly cacheTTL = 3600;

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Material)
    private readonly materialRepository: Repository<Material>,
    @InjectRepository(LearningSuggestion)
    private readonly suggestionRepository: Repository<LearningSuggestion>,
    private readonly redisService: RedisService,
  ) {
    this.modelPath = join(process.cwd(), 'models', 'recommendation-model');
    this.initializeModel();
  }

  private async initializeModel() {
    this.model = new AIModel(this.modelPath);
    try {
      await this.model.load();
      this.logger.log('Loaded existing model');
    } catch (error) {
      this.logger.log('No existing model found, initializing new model');
      await this.model.initialize();
    }
  }

  async getRecommendations(userId: string): Promise<Recommendation[]> {
    try {
      const cacheKey = `recommendations:${userId}`;
      const cachedRecommendations = await this.redisService.get(cacheKey);
      if (cachedRecommendations) {
        return JSON.parse(cachedRecommendations);
      }

      const userFeatures = await this.extractUserFeatures(userId);
      const score = await this.model.predict(
        this.featuresToArray(userFeatures),
      );
      const materials = await this.getRelevantMaterials(userFeatures);

      const recommendations = materials.map((material) => ({
        materialId: material.id,
        score: score * this.calculateMaterialRelevance(material, userFeatures),
        reason: this.generateRecommendationReason(material, userFeatures),
      }));

      const topRecommendations = recommendations
        .sort((a, b) => b.score - a.score)
        .slice(0, 5);

      await this.redisService.set(
        cacheKey,
        JSON.stringify(topRecommendations),
        this.cacheTTL,
      );

      return topRecommendations;
    } catch (error) {
      this.logger.error('Error generating recommendations:', error);
      return [];
    }
  }

  private async extractUserFeatures(userId: string): Promise<UserFeatures> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['learningHistory', 'preferences'],
    });

    if (!user) {
      throw new Error('User not found');
    }

    const learningHistory = (user.learningHistory || []).map((item) => ({
      ...item,
      timestamp:
        item.timestamp instanceof Date
          ? item.timestamp.toISOString()
          : item.timestamp,
    })) as LearningHistoryItem[];
    const preferences = (user.preferences as UserPreferences) || {
      learningStyle: 0.5,
    };

    return {
      completedMaterials: learningHistory.length,
      averageScore: this.calculateAverageScore(learningHistory),
      studyTime: this.calculateTotalStudyTime(learningHistory),
      preferredCategories: this.extractPreferredCategories(learningHistory),
      lastActivity: this.getLastActivityDate(learningHistory),
      difficultyPreference: this.calculateDifficultyPreference(learningHistory),
      learningStyle: this.determineLearningStyle(preferences),
      engagementLevel: this.calculateEngagementLevel(learningHistory),
      quizPerformance: this.calculateQuizPerformance(learningHistory),
      materialInteraction: this.calculateMaterialInteraction(learningHistory),
    };
  }

  private featuresToArray(features: UserFeatures): number[] {
    return [
      features.completedMaterials,
      features.averageScore,
      features.studyTime,
      features.difficultyPreference,
      features.learningStyle,
      features.engagementLevel,
      features.quizPerformance,
      features.materialInteraction,
      this.calculateCategoryScore(features.preferredCategories),
      this.calculateActivityScore(features.lastActivity),
    ];
  }

  private async getRelevantMaterials(
    features: UserFeatures,
  ): Promise<Material[]> {
    return this.materialRepository.find({
      where: {
        category: In(features.preferredCategories),
        difficulty: features.difficultyPreference,
      },
      relations: ['author', 'unit'],
    });
  }

  private calculateMaterialRelevance(
    material: Material,
    features: UserFeatures,
  ): number {
    return 1.0; // Placeholder
  }

  private generateRecommendationReason(
    material: Material,
    features: UserFeatures,
  ): string {
    return `Based on your learning style and performance in ${material.category}`;
  }

  private calculateAverageScore(history: LearningHistoryItem[]): number {
    if (!history?.length) return 0;
    return (
      history.reduce((sum, item) => sum + (item.score || 0), 0) / history.length
    );
  }

  private calculateTotalStudyTime(history: LearningHistoryItem[]): number {
    if (!history?.length) return 0;
    return history.reduce((sum, item) => sum + (item.duration || 0), 0);
  }

  private extractPreferredCategories(history: LearningHistoryItem[]): string[] {
    if (!history?.length) return [];
    const categories = history.map((item) => item.category);
    return [...new Set(categories)];
  }

  private getLastActivityDate(history: LearningHistoryItem[]): Date {
    if (!history?.length) return new Date(0);
    return new Date(
      Math.max(...history.map((item) => new Date(item.timestamp).getTime())),
    );
  }

  private calculateDifficultyPreference(
    history: LearningHistoryItem[],
  ): number {
    if (!history?.length) return 0.5;
    return (
      history.reduce((sum, item) => sum + (item.difficulty || 0.5), 0) /
      history.length
    );
  }

  private determineLearningStyle(preferences: UserPreferences): number {
    return preferences?.learningStyle || 0.5;
  }

  private calculateEngagementLevel(history: LearningHistoryItem[]): number {
    if (!history?.length) return 0;
    return (
      history.reduce((sum, item) => sum + (item.engagement || 0), 0) /
      history.length
    );
  }

  private calculateQuizPerformance(history: LearningHistoryItem[]): number {
    if (!history?.length) return 0;
    const quizHistory = history.filter((item) => item.type === 'quiz');
    return quizHistory.length
      ? quizHistory.reduce((sum, item) => sum + (item.score || 0), 0) /
          quizHistory.length
      : 0;
  }

  private calculateMaterialInteraction(history: LearningHistoryItem[]): number {
    if (!history?.length) return 0;
    return (
      history.reduce((sum, item) => sum + (item.interactionScore || 0), 0) /
      history.length
    );
  }

  private calculateCategoryScore(categories: string[]): number {
    return categories.length / 10;
  }

  private calculateActivityScore(lastActivity: Date): number {
    const daysSinceLastActivity =
      (Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24);
    return Math.max(0, 1 - daysSinceLastActivity / 30);
  }

  async trainModel(trainingData: {
    inputs: number[][];
    labels: number[];
  }): Promise<void> {
    try {
      await this.model.train(trainingData.inputs, trainingData.labels);
      await this.saveModel();
      this.logger.log('Model trained and saved successfully');
    } catch (error) {
      this.logger.error('Error training model:', error);
      throw error;
    }
  }

  private async saveModel(): Promise<void> {
    try {
      await this.model.save();
    } catch (error) {
      this.logger.error('Failed to save model:', error);
      throw error;
    }
  }
}
