import { openDB, DBSchema, IDBPDatabase } from 'idb';

interface FlashcardDB extends DBSchema {
  flashcards: {
    key: string;
    value: {
      id: string;
      userId: string;
      questionId: string;
      ef: number; // ease factor
      interval: number; // in days
      nextReview: Date;
      correctStreak: number;
      lastReview: Date;
      synced: boolean;
    };
    indexes: {
      'by-user': string;
      'by-next-review': Date;
      'by-sync-status': boolean;
    };
  };
}

class SpacedRepetitionService {
  private db: IDBPDatabase<FlashcardDB> | null = null;
  private readonly DB_NAME = 'medical-flashcards-db';
  private readonly DB_VERSION = 1;
  private readonly INITIAL_EF = 2.5;
  private readonly MIN_EF = 1.3;

  async init() {
    this.db = await openDB<FlashcardDB>(this.DB_NAME, this.DB_VERSION, {
      upgrade(db) {
        const store = db.createObjectStore('flashcards', { keyPath: 'id' });
        store.createIndex('by-user', 'userId');
        store.createIndex('by-next-review', 'nextReview');
        store.createIndex('by-sync-status', 'synced');
      },
    });
  }

  async createFlashcard(userId: string, questionId: string) {
    if (!this.db) await this.init();
    const flashcard = {
      id: crypto.randomUUID(),
      userId,
      questionId,
      ef: this.INITIAL_EF,
      interval: 0,
      nextReview: new Date(),
      correctStreak: 0,
      lastReview: new Date(),
      synced: false,
    };

    const tx = this.db!.transaction('flashcards', 'readwrite');
    await tx.store.put(flashcard);
    await tx.done;
    return flashcard;
  }

  async getDueCards(userId: string): Promise<FlashcardDB['flashcards']['value'][]> {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('flashcards', 'readonly');
    const index = tx.store.index('by-user');
    const cards = await index.getAll(userId);
    return cards.filter(card => card.nextReview <= new Date());
  }

  async updateCard(cardId: string, quality: number) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('flashcards', 'readwrite');
    const card = await tx.store.get(cardId);

    if (!card) return null;

    // SM-2 Algorithm
    const newEf = Math.max(
      this.MIN_EF,
      card.ef + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02))
    );

    let newInterval;
    if (quality >= 3) {
      if (card.correctStreak === 0) {
        newInterval = 1;
      } else if (card.correctStreak === 1) {
        newInterval = 6;
      } else {
        newInterval = Math.round(card.interval * newEf);
      }
      card.correctStreak += 1;
    } else {
      card.correctStreak = 0;
      newInterval = 1;
    }

    const updatedCard = {
      ...card,
      ef: newEf,
      interval: newInterval,
      nextReview: new Date(Date.now() + newInterval * 24 * 60 * 60 * 1000),
      lastReview: new Date(),
      synced: false,
    };

    await tx.store.put(updatedCard);
    await tx.done;
    return updatedCard;
  }

  async getUnsyncedCards() {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('flashcards', 'readonly');
    const index = tx.store.index('by-sync-status');
    return index.getAll(false);
  }

  async markAsSynced(cardIds: string[]) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('flashcards', 'readwrite');
    for (const id of cardIds) {
      const card = await tx.store.get(id);
      if (card) {
        card.synced = true;
        await tx.store.put(card);
      }
    }
    await tx.done;
  }

  async getCardStats(userId: string) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('flashcards', 'readonly');
    const index = tx.store.index('by-user');
    const cards = await index.getAll(userId);

    const now = new Date();
    const dueCards = cards.filter(card => card.nextReview <= now);
    const upcomingCards = cards.filter(card => card.nextReview > now);

    return {
      total: cards.length,
      due: dueCards.length,
      upcoming: upcomingCards.length,
      averageInterval: cards.reduce((sum, card) => sum + card.interval, 0) / cards.length,
    };
  }
}

export const spacedRepetition = new SpacedRepetitionService();
