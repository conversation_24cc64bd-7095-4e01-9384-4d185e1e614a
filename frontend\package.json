{"name": "medtrackhub", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-progress": "^1.0.3", "@tensorflow/tfjs-node": "^4.22.0", "axios": "^1.9.0", "caniuse-lite": "^1.0.30001726", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "idb": "^8.0.3", "ioredis": "^5.6.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.323.0", "next-auth": "^4.24.11", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "recharts": "^2.15.3", "sonner": "^2.0.6", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "zod": "^3.22.4", "zustand": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "@next/eslint-plugin-next": "^15.3.5", "@tailwindcss/typography": "^0.5.16", "@types/js-cookie": "^3.0.6", "@types/node": "^20.19.0", "@types/react": "^18.3.23", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "8.34.0", "@typescript-eslint/parser": "8.34.0", "autoprefixer": "^10.4.21", "eslint": "^9.28.0", "eslint-config-next": "^15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.2.0", "next": "^15.3.5", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.3.0", "typescript": "^5.8.3"}, "pnpm": {"ignoredBuiltDependencies": ["core-js", "sharp", "unrs-resolver"]}}