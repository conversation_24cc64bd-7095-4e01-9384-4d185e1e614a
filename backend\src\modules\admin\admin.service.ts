import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  async getAllUsers() {
    return this.usersRepository.find();
  }

  async getUserById(id: string) {
    return this.usersRepository.findOne({ where: { id } });
  }

  async createTestUser(userData: Partial<User>) {
    const user = this.usersRepository.create(userData);
    return this.usersRepository.save(user);
  }

  async deleteUser(id: string) {
    return this.usersRepository.delete(id);
  }

  async updateUser(id: string, userData: Partial<User>) {
    await this.usersRepository.update(id, userData);
    return this.getUserById(id);
  }
}
