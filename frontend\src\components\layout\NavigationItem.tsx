'use client';

import React, { useState } from 'react';
import { ChevronRight } from 'lucide-react';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  badge?: string | number;
  children?: NavigationItem[];
}

interface NavigationItemProps {
  item: NavigationItem;
  sidebarCollapsed?: boolean;
  sidebarOpen?: boolean;
  onClick?: () => void;
}

export const NavigationItemComponent: React.FC<NavigationItemProps> = ({
  item,
  sidebarCollapsed = false,
  sidebarOpen = false,
  onClick,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleClick = () => {
    if (item.children) {
      setIsExpanded(!isExpanded);
    }
    if (onClick && !item.children) {
      onClick();
    }
  };

  const showLabel = !sidebarCollapsed || sidebarOpen;

  return (
    <div>
      <button
        type="button"
        onClick={handleClick}
        className="w-full flex items-center justify-between px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
      >
        <div className="flex items-center space-x-3">
          <item.icon className="h-5 w-5 flex-shrink-0" />
          {showLabel && (
            <>
              <span>{item.label}</span>
              {item.badge && (
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {item.badge}
                </span>
              )}
            </>
          )}
        </div>
        {item.children && showLabel && (
          <ChevronRight
            className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
          />
        )}
      </button>

      {item.children && isExpanded && showLabel && (
        <div className="ml-6 mt-1 space-y-1">
          {item.children.map(child => (
            <NavigationItemComponent
              key={child.id}
              item={child}
              sidebarCollapsed={sidebarCollapsed}
              sidebarOpen={sidebarOpen}
            />
          ))}
        </div>
      )}
    </div>
  );
};
