import { useEffect, useState } from 'react';

export default function Notifications({ userId }: { userId: string }) {
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    fetch(`/api/notifications?userId=${userId}`, {
      headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
    })
      .then(res => res.json())
      .then(data => setNotifications(data));
  }, [userId]);

  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-2">Notifications</h2>
      {notifications.length ? (
        <ul>
          {notifications.map((notif: any) => (
            <li key={notif.id} className="py-2">
              {notif.message} - {notif.is_read ? 'Read' : 'Unread'}
            </li>
          ))}
        </ul>
      ) : (
        <p>No notifications yet.</p>
      )}
    </div>
  );
}
