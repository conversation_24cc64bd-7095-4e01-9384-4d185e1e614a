import { Test } from '@nestjs/testing';
import { MaterialsService } from '../../src/modules/materials/materials.service';
import { MaterialsController } from '../../src/modules/materials/materials.controller';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Material } from '../../src/entities/materials.entity';
import { MaterialShare } from '../../src/entities/material_shares.entity';
import { ConfigService } from '@nestjs/config';
import { materialFixture } from '../fixtures/material.fixture';

describe('Materials Integration', () => {
  let materialsService: MaterialsService;
  let materialsController: MaterialsController;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [MaterialsController],
      providers: [
        MaterialsService,
        {
          provide: getRepositoryToken(Material),
          useValue: {
            find: jest.fn().mockResolvedValue([materialFixture]),
            findOne: jest.fn().mockResolvedValue(materialFixture),
            save: jest.fn().mockResolvedValue(materialFixture),
            create: jest.fn().mockReturnValue(materialFixture),
          },
        },
        {
          provide: getRepositoryToken(MaterialShare),
          useValue: {
            create: jest.fn().mockReturnValue({}),
            save: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              if (key === 'AWS_S3_BUCKET') return 'test-bucket';
              return null;
            }),
          },
        },
      ],
    }).compile();

    materialsService = moduleRef.get<MaterialsService>(MaterialsService);
    materialsController =
      moduleRef.get<MaterialsController>(MaterialsController);
  });

  it('should create a material', async () => {
    const result = await materialsController.create({
      title: materialFixture.title,
      description: materialFixture.description,
      type: materialFixture.type,
    });

    expect(result).toBeDefined();
    expect(result.title).toBe(materialFixture.title);
  });
});
