# MedTrack Hub Docker Setup Script (PowerShell)
# This script sets up the complete Docker environment for the MedTrack Hub application

param(
    [Parameter(Position=0)]
    [ValidateSet("start", "dev", "monitoring", "stop", "restart", "logs", "status", "clean")]
    [string]$Command = "start"
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is installed
function Test-Docker {
    Write-Status "Checking Docker installation..."
    
    try {
        $dockerVersion = docker --version
        $composeVersion = docker-compose --version
        Write-Success "Docker and Docker Compose are installed"
        return $true
    }
    catch {
        Write-Error "Docker or Docker Compose is not installed. Please install Docker Desktop first."
        return $false
    }
}

# Check if .env file exists
function Test-EnvFile {
    Write-Status "Checking environment configuration..."
    
    if (-not (Test-Path ".env")) {
        Write-Warning ".env file not found. Creating from .env.example..."
        Copy-Item ".env.example" ".env"
        Write-Warning "Please edit .env file with your actual configuration values"
        Write-Warning "Especially update passwords and secrets!"
    }
    else {
        Write-Success ".env file found"
    }
}

# Create necessary directories
function New-Directories {
    Write-Status "Creating necessary directories..."
    
    $directories = @(
        "nginx\ssl",
        "backend\uploads",
        "backend\logs",
        "frontend\logs",
        "monitoring\data"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Status "Created directory: $dir"
        }
    }
    
    Write-Success "All directories created"
}

# Generate self-signed SSL certificates for development
function New-SSLCertificates {
    Write-Status "Checking SSL certificates..."
    
    if (-not (Test-Path "nginx\ssl\cert.pem") -or -not (Test-Path "nginx\ssl\key.pem")) {
        Write-Warning "SSL certificates not found. Generating self-signed certificates for development..."
        
        try {
            # Check if OpenSSL is available
            $opensslPath = Get-Command openssl -ErrorAction SilentlyContinue
            
            if ($opensslPath) {
                & openssl req -x509 -newkey rsa:4096 -keyout nginx\ssl\key.pem -out nginx\ssl\cert.pem -days 365 -nodes -subj "/C=US/ST=State/L=City/O=MedTrack Hub/CN=localhost" 2>$null
                Write-Success "Self-signed SSL certificates generated"
            }
            else {
                Write-Warning "OpenSSL not found. SSL certificates will need to be generated manually."
                Write-Warning "You can install OpenSSL or use the nginx container without SSL for development."
            }
        }
        catch {
            Write-Warning "Could not generate SSL certificates. Continuing without SSL..."
        }
    }
    else {
        Write-Success "SSL certificates found"
    }
}

# Build Docker images
function Build-Images {
    Write-Status "Building Docker images..."
    
    try {
        Write-Status "Building backend image..."
        docker-compose build backend
        
        Write-Status "Building frontend image..."
        docker-compose build frontend
        
        Write-Status "Building analytics image..."
        docker-compose build analytics
        
        Write-Success "All images built successfully"
    }
    catch {
        Write-Error "Failed to build images: $_"
        exit 1
    }
}

# Start services
function Start-Services {
    param([string]$Mode)
    
    try {
        switch ($Mode) {
            "dev" {
                Write-Status "Starting development environment..."
                docker-compose -f docker-compose.dev.yml up -d
            }
            "monitoring" {
                Write-Status "Starting monitoring services..."
                docker-compose -f docker-compose.monitoring.yml up -d
            }
            default {
                Write-Status "Starting production environment..."
                docker-compose up -d
            }
        }
        
        Write-Success "Services started successfully"
    }
    catch {
        Write-Error "Failed to start services: $_"
        exit 1
    }
}

# Show service status
function Show-Status {
    Write-Status "Service Status:"
    docker-compose ps
    
    Write-Host ""
    Write-Status "Available Services:"
    Write-Host "🌐 Frontend: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "🔧 Backend API: http://localhost:3002" -ForegroundColor Cyan
    Write-Host "📊 Analytics: http://localhost:5000" -ForegroundColor Cyan
    Write-Host "🗄️  Database: localhost:5432" -ForegroundColor Cyan
    Write-Host "🔴 Redis: localhost:6379" -ForegroundColor Cyan
    Write-Host "📈 Prometheus: http://localhost:9090" -ForegroundColor Cyan
    Write-Host "📊 Grafana: http://localhost:3001" -ForegroundColor Cyan
    Write-Host "🚨 AlertManager: http://localhost:9093" -ForegroundColor Cyan
}

# Main script logic
Write-Host "🚀 MedTrack Hub Docker Setup" -ForegroundColor Magenta
Write-Host "==============================" -ForegroundColor Magenta

switch ($Command) {
    "start" {
        if (-not (Test-Docker)) { exit 1 }
        Test-EnvFile
        New-Directories
        New-SSLCertificates
        Build-Images
        Start-Services "prod"
        Show-Status
    }
    "dev" {
        if (-not (Test-Docker)) { exit 1 }
        Test-EnvFile
        New-Directories
        Start-Services "dev"
        Show-Status
    }
    "monitoring" {
        if (-not (Test-Docker)) { exit 1 }
        Test-EnvFile
        Start-Services "monitoring"
        Show-Status
    }
    "stop" {
        Write-Status "Stopping all services..."
        docker-compose down
        docker-compose -f docker-compose.dev.yml down 2>$null
        docker-compose -f docker-compose.monitoring.yml down 2>$null
        Write-Success "All services stopped"
    }
    "restart" {
        & $PSCommandPath stop
        Start-Sleep -Seconds 2
        & $PSCommandPath start
    }
    "logs" {
        docker-compose logs -f
    }
    "status" {
        Show-Status
    }
    "clean" {
        Write-Warning "This will remove all containers, images, and volumes!"
        $response = Read-Host "Are you sure? (y/N)"
        if ($response -eq "y" -or $response -eq "Y") {
            docker-compose down -v --rmi all
            docker system prune -f
            Write-Success "Cleanup completed"
        }
    }
    default {
        Write-Host "Usage: .\docker-setup.ps1 {start|dev|monitoring|stop|restart|logs|status|clean}" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Commands:" -ForegroundColor Yellow
        Write-Host "  start      - Start production environment"
        Write-Host "  dev        - Start development environment"
        Write-Host "  monitoring - Start monitoring services only"
        Write-Host "  stop       - Stop all services"
        Write-Host "  restart    - Restart all services"
        Write-Host "  logs       - Show logs"
        Write-Host "  status     - Show service status"
        Write-Host "  clean      - Remove all containers and images"
    }
}
