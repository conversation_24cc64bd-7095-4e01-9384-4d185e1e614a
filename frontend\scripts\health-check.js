#!/usr/bin/env node
/**
 * Frontend health check script
 */

const http = require('http');

const PORT = process.env.PORT || 3000;
const TIMEOUT = 5000;

const healthCheck = () => {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: 'localhost',
      port: PORT,
      path: '/api/health',
      method: 'GET',
      timeout: TIMEOUT
    }, (res) => {
      if (res.statusCode === 200) {
        resolve('Frontend is healthy');
      } else {
        reject(new Error(`Frontend health check failed with status: ${res.statusCode}`));
      }
    });

    req.on('error', (err) => {
      reject(new Error(`Frontend health check failed: ${err.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Frontend health check timed out'));
    });

    req.end();
  });
};

// Run health check
healthCheck()
  .then((message) => {
    console.log(message);
    process.exit(0);
  })
  .catch((error) => {
    console.error(error.message);
    process.exit(1);
  });
