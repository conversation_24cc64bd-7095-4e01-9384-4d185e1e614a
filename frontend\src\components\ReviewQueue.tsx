import React, { useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
} from '@mui/material';
import { PlayArrow, Refresh } from '@mui/icons-material';
import { flashcardApi, Flashcard } from '../services/flashcardApi';
import { useAuth } from '../hooks/useAuth';
import { useRouter } from 'next/router';

export const ReviewQueue: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [dueCards, setDueCards] = useState<Flashcard[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchDueCards = async () => {
    if (!user) return;
    try {
      const cards = await flashcardApi.getDueCards(user.id);
      setDueCards(cards);
    } catch (error) {
      console.error('Failed to fetch due cards:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDueCards();
  }, [user]);

  const handleStartReview = () => {
    router.push('/review');
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <Typography>Loading review queue...</Typography>
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">Review Queue</Typography>
          <Box>
            <IconButton onClick={fetchDueCards} size="small" sx={{ mr: 1 }}>
              <Refresh />
            </IconButton>
            <Button
              variant="contained"
              color="primary"
              startIcon={<PlayArrow />}
              onClick={handleStartReview}
              disabled={dueCards.length === 0}
            >
              Start Review
            </Button>
          </Box>
        </Box>

        {dueCards.length === 0 ? (
          <Typography color="textSecondary" align="center">
            No cards due for review
          </Typography>
        ) : (
          <List>
            {dueCards.map(card => (
              <ListItem key={card.id} divider>
                <ListItemText
                  primary={`Card #${card.id.slice(0, 8)}`}
                  secondary={`Last review: ${formatDate(card.lastReview)}`}
                />
                <ListItemSecondaryAction>
                  <Chip
                    label={`${card.correctStreak} streak`}
                    color="primary"
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Chip label={`${card.interval} days`} color="secondary" size="small" />
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </CardContent>
    </Card>
  );
};
