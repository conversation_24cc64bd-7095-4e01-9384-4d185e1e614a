import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  CircularProgress,
  LinearProgress,
  Rating,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import { flashcardApi, Flashcard } from '../../services/flashcardApi';
import { useAuth } from '../../hooks/useAuth';

export default function ReviewPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [cards, setCards] = useState<Flashcard[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showAnswer, setShowAnswer] = useState(false);
  const [showCompletion, setShowCompletion] = useState(false);
  const [rating, setRating] = useState<number | null>(null);

  useEffect(() => {
    const fetchCards = async () => {
      if (!user) {
        router.push('/login');
        return;
      }
      try {
        const dueCards = await flashcardApi.getDueCards(user.id);
        setCards(dueCards);
      } catch (error) {
        console.error('Failed to fetch cards:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCards();
  }, [user, router]);

  const handleShowAnswer = () => {
    setShowAnswer(true);
  };

  const handleRating = async (value: number) => {
    setRating(value);
    const currentCard = cards[currentIndex];

    try {
      await flashcardApi.updateCard(currentCard.id, value);

      if (currentIndex < cards.length - 1) {
        setCurrentIndex(currentIndex + 1);
        setShowAnswer(false);
        setRating(null);
      } else {
        setShowCompletion(true);
      }
    } catch (error) {
      console.error('Failed to update card:', error);
    }
  };

  const handleComplete = () => {
    router.push('/dashboard');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  if (cards.length === 0) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom>
            No cards due for review
          </Typography>
          <Button variant="contained" onClick={() => router.push('/dashboard')}>
            Return to Dashboard
          </Button>
        </Paper>
      </Container>
    );
  }

  const progress = ((currentIndex + 1) / cards.length) * 100;
  const currentCard = cards[currentIndex];

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper sx={{ p: 4 }}>
        <Box mb={3}>
          <LinearProgress variant="determinate" value={progress} />
          <Typography variant="body2" color="textSecondary" align="right" mt={1}>
            {currentIndex + 1} of {cards.length}
          </Typography>
        </Box>

        <Box minHeight="300px" display="flex" flexDirection="column" justifyContent="center">
          <Typography variant="h5" gutterBottom>
            Question
          </Typography>
          <Typography variant="body1" paragraph>
            {currentCard.questionId} {/* Replace with actual question text */}
          </Typography>

          {showAnswer ? (
            <Box mt={4}>
              <Typography variant="h6" gutterBottom>
                How well did you remember?
              </Typography>
              <Rating
                value={rating}
                onChange={(_, value) => value && handleRating(value)}
                max={5}
                size="large"
              />
            </Box>
          ) : (
            <Button variant="contained" color="primary" onClick={handleShowAnswer} sx={{ mt: 4 }}>
              Show Answer
            </Button>
          )}
        </Box>
      </Paper>

      <Dialog open={showCompletion} onClose={handleComplete}>
        <DialogTitle>Review Complete!</DialogTitle>
        <DialogContent>
          <Typography>You've completed all cards due for review today. Great job!</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleComplete} color="primary">
            Return to Dashboard
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
