// src/store/index.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, Course, Material, Settings } from '../types';

interface AppState {
  // User state
  user: User | null;
  setUser: (user: User | null) => void;

  // Course state
  courses: Record<string, Course>;
  setCourses: (courses: Course[]) => void;
  addCourse: (course: Course) => void;
  updateCourse: (id: string, course: Partial<Course>) => void;
  removeCourse: (id: string) => void;

  // Material state
  materials: Record<string, Material>;
  setMaterials: (materials: Material[]) => void;
  addMaterial: (material: Material) => void;
  updateMaterial: (id: string, material: Partial<Material>) => void;
  removeMaterial: (id: string) => void;

  // Settings state
  settings: Settings;
  updateSettings: (settings: Partial<Settings>) => void;

  // UI state
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;

  // Loading states
  loading: Record<string, boolean>;
  setLoading: (key: string, loading: boolean) => void;

  // Error states
  errors: Record<string, string>;
  setError: (key: string, error: string | null) => void;
  clearErrors: () => void;
}

const defaultSettings: Settings = {
  notifications: true,
  emailNotifications: true,
  darkMode: false,
  language: 'en',
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
};

export const useStore = create<AppState>()(
  persist(
    (set, get) => ({
      // User state
      user: null,
      setUser: user => set({ user }),

      // Course state
      courses: {},
      setCourses: courses =>
        set({
          courses: courses.reduce(
            (acc, course) => ({
              ...acc,
              [course.id]: course,
            }),
            {}
          ),
        }),
      addCourse: course =>
        set(state => ({
          courses: { ...state.courses, [course.id]: course },
        })),
      updateCourse: (id, course) =>
        set(state => ({
          courses: {
            ...state.courses,
            [id]: { ...state.courses[id], ...course },
          },
        })),
      removeCourse: id =>
        set(state => {
          const { [id]: removed, ...courses } = state.courses;
          return { courses };
        }),

      // Material state
      materials: {},
      setMaterials: materials =>
        set({
          materials: materials.reduce(
            (acc, material) => ({
              ...acc,
              [material.id]: material,
            }),
            {}
          ),
        }),
      addMaterial: material =>
        set(state => ({
          materials: { ...state.materials, [material.id]: material },
        })),
      updateMaterial: (id, material) =>
        set(state => ({
          materials: {
            ...state.materials,
            [id]: { ...state.materials[id], ...material },
          },
        })),
      removeMaterial: id =>
        set(state => {
          const { [id]: removed, ...materials } = state.materials;
          return { materials };
        }),

      // Settings state
      settings: defaultSettings,
      updateSettings: settings =>
        set(state => ({
          settings: { ...state.settings, ...settings },
        })),

      // UI state
      sidebarOpen: true,
      setSidebarOpen: open => set({ sidebarOpen: open }),
      theme: 'light',
      setTheme: theme => set({ theme }),

      // Loading states
      loading: {},
      setLoading: (key, loading) =>
        set(state => ({
          loading: { ...state.loading, [key]: loading },
        })),

      // Error states
      errors: {},
      setError: (key, error) =>
        set(state => ({
          errors: error
            ? { ...state.errors, [key]: error }
            : Object.fromEntries(Object.entries(state.errors).filter(([k]) => k !== key)),
        })),
      clearErrors: () => set({ errors: {} }),
    }),
    {
      name: 'app-storage',
      partialize: state => ({
        user: state.user,
        settings: state.settings,
        theme: state.theme,
      }),
    }
  )
);
