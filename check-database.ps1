# PowerShell script to check PostgreSQL database setup for MedTrack Hub

Write-Host "Checking PostgreSQL database setup..." -ForegroundColor Green

# Check if PostgreSQL is running
Write-Host "`n1. Checking PostgreSQL service status..." -ForegroundColor Yellow
$pgStatus = pg_isready -h localhost -p 5432
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PostgreSQL is running on localhost:5432" -ForegroundColor Green
} else {
    Write-Host "❌ PostgreSQL is not running" -ForegroundColor Red
    exit 1
}

# List all users
Write-Host "`n2. Listing all PostgreSQL users..." -ForegroundColor Yellow
Write-Host "Please enter the PostgreSQL superuser (postgres) password when prompted." -ForegroundColor Cyan
psql -h localhost -p 5432 -U postgres -c "\du"

# List all databases
Write-Host "`n3. Listing all databases..." -ForegroundColor Yellow
psql -h localhost -p 5432 -U postgres -c "\l"

# Check if medical user exists and can connect
Write-Host "`n4. Testing medical user connection..." -ForegroundColor Yellow
$env:PGPASSWORD = "AU110s/6081/2021MT"
$testResult = psql -h localhost -p 5432 -U medical -d medical_tracker -c "SELECT current_user, current_database();" 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Medical user can connect to medical_tracker database" -ForegroundColor Green
    Write-Host "Connection details:" -ForegroundColor Cyan
    Write-Host $testResult
} else {
    Write-Host "❌ Medical user cannot connect to database" -ForegroundColor Red
    Write-Host "Error: $testResult" -ForegroundColor Red
}

# Check database permissions
Write-Host "`n5. Checking medical user permissions..." -ForegroundColor Yellow
$permResult = psql -h localhost -p 5432 -U medical -d medical_tracker -c "
SELECT 
    grantee, 
    privilege_type 
FROM information_schema.role_table_grants 
WHERE grantee = 'medical'
UNION
SELECT 
    grantee,
    privilege_type
FROM information_schema.role_usage_grants 
WHERE grantee = 'medical';
" 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Medical user permissions:" -ForegroundColor Green
    Write-Host $permResult
} else {
    Write-Host "❌ Could not check permissions" -ForegroundColor Red
}

# Test creating a table (to verify full permissions)
Write-Host "`n6. Testing table creation permissions..." -ForegroundColor Yellow
$createTest = psql -h localhost -p 5432 -U medical -d medical_tracker -c "
CREATE TABLE IF NOT EXISTS test_permissions (
    id SERIAL PRIMARY KEY,
    test_column VARCHAR(50)
);
INSERT INTO test_permissions (test_column) VALUES ('test');
SELECT * FROM test_permissions;
DROP TABLE test_permissions;
" 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Medical user has full table creation/modification permissions" -ForegroundColor Green
} else {
    Write-Host "❌ Medical user lacks some permissions" -ForegroundColor Red
    Write-Host "Error: $createTest" -ForegroundColor Red
}

Write-Host "`n7. Summary:" -ForegroundColor Blue
Write-Host "Database Host: localhost:5432" -ForegroundColor White
Write-Host "Database Name: medical_tracker" -ForegroundColor White
Write-Host "Database User: medical" -ForegroundColor White
Write-Host "Password: AU110s/6081/2021MT" -ForegroundColor White

Write-Host "`nDatabase check completed!" -ForegroundColor Green
