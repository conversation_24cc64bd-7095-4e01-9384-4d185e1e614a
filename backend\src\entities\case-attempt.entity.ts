import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ClinicalCase } from './clinical-case.entity';
import { User } from './user.entity';

export enum CaseAttemptStatus {
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  ABANDONED = 'abandoned',
}

@Entity('case_attempts')
export class CaseAttempt {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: CaseAttemptStatus,
    default: CaseAttemptStatus.IN_PROGRESS,
  })
  status: CaseAttemptStatus;

  @Column({ type: 'timestamp' })
  started_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  completed_at: Date;

  @Column({ type: 'int', default: 0 })
  time_spent_seconds: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  score: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  max_score: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  percentage: number;

  @Column({ type: 'jsonb', nullable: true })
  progress: {
    current_section: string;
    completed_sections: string[];
    unlocked_sections: string[];
    decisions_made: {
      decision_point_id: string;
      selected_option_id: string;
      timestamp: Date;
      points_earned: number;
    }[];
    section_times: {
      section_id: string;
      time_spent: number;
    }[];
  };

  @Column({ type: 'jsonb', nullable: true })
  clinical_reasoning: {
    differential_diagnoses: {
      diagnosis: string;
      confidence: number;
      reasoning: string;
      timestamp: Date;
    }[];
    final_diagnosis: string;
    diagnostic_confidence: number;
    key_findings_identified: string[];
    missed_findings: string[];
    treatment_plan: {
      immediate_actions: string[];
      medications: string[];
      follow_up: string[];
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  feedback: {
    overall_performance: string;
    strengths: string[];
    areas_for_improvement: string[];
    specific_feedback: {
      section_id: string;
      feedback: string;
      score: number;
    }[];
    recommendations: {
      study_topics: string[];
      resources: string[];
      next_cases: string[];
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    decision_accuracy: number;
    time_efficiency: number;
    clinical_reasoning_score: number;
    knowledge_gaps: string[];
    learning_objectives_met: string[];
    difficulty_appropriate: boolean;
  };

  @Column({ type: 'uuid' })
  clinical_case_id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @ManyToOne(() => ClinicalCase, (clinicalCase) => clinicalCase.attempts, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'clinical_case_id' })
  clinical_case: ClinicalCase;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
