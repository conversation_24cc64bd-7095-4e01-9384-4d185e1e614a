'use client';

import { useEffect, useState } from 'react';
import { performanceMonitor } from '@/services/performanceMonitor';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

interface MetricData {
  timestamp: number;
  value: number;
  name: string;
}

export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<MetricData[]>([]);
  const [pageLoadMetrics, setPageLoadMetrics] = useState<any[]>([]);
  const [apiMetrics, setApiMetrics] = useState<any[]>([]);
  const [resourceMetrics, setResourceMetrics] = useState<any[]>([]);

  useEffect(() => {
    const updateMetrics = () => {
      const allMetrics = performanceMonitor.getMetrics();
      const formattedMetrics = allMetrics.map(metric => ({
        timestamp: new Date(metric.timestamp).toLocaleTimeString(),
        value: metric.value,
        name: metric.name,
      }));
      setMetrics(formattedMetrics);

      const pageLoads = performanceMonitor.getPageLoadMetrics();
      setPageLoadMetrics(pageLoads);

      const apis = performanceMonitor.getApiMetrics();
      setApiMetrics(apis);

      const resources = performanceMonitor.getResourceMetrics();
      setResourceMetrics(resources);
    };

    // Update metrics every second
    const interval = setInterval(updateMetrics, 1000);
    updateMetrics();

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="p-6 space-y-8">
      <h1 className="text-2xl font-bold mb-6">Performance Dashboard</h1>

      {/* Page Load Metrics */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Page Load Performance</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {pageLoadMetrics[0] && (
            <>
              <MetricCard title="Load Time" value={`${pageLoadMetrics[0].loadTime.toFixed(2)}ms`} />
              <MetricCard
                title="First Contentful Paint"
                value={`${pageLoadMetrics[0].firstContentfulPaint.toFixed(2)}ms`}
              />
              <MetricCard
                title="Largest Contentful Paint"
                value={`${pageLoadMetrics[0].largestContentfulPaint.toFixed(2)}ms`}
              />
              <MetricCard
                title="Time to Interactive"
                value={`${pageLoadMetrics[0].timeToInteractive.toFixed(2)}ms`}
              />
            </>
          )}
        </div>
      </div>

      {/* API Performance */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">API Performance</h2>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={apiMetrics}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="duration" stroke="#8884d8" name="Duration (ms)" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Resource Loading */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Resource Loading</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr>
                <th className="px-4 py-2">Resource</th>
                <th className="px-4 py-2">Type</th>
                <th className="px-4 py-2">Size (KB)</th>
                <th className="px-4 py-2">Load Time (ms)</th>
              </tr>
            </thead>
            <tbody>
              {resourceMetrics.map((resource, index) => (
                <tr key={index}>
                  <td className="px-4 py-2">{resource.name}</td>
                  <td className="px-4 py-2">{resource.type}</td>
                  <td className="px-4 py-2">{(resource.size / 1024).toFixed(2)}</td>
                  <td className="px-4 py-2">{resource.loadTime.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Custom Metrics */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Custom Metrics</h2>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={metrics}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="value" stroke="#82ca9d" name="Value" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}

function MetricCard({ title, value }: { title: string; value: string }) {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h3 className="text-sm font-medium text-gray-500">{title}</h3>
      <p className="text-2xl font-semibold text-gray-900">{value}</p>
    </div>
  );
}
