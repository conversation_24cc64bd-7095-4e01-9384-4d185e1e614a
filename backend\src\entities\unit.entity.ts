import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, OneToMany, ManyToOne, CreateDateColumn, UpdateDateColumn, JoinColumn } from 'typeorm';
import { Material } from './materials.entity';
import { Progress } from './progress.entity';
import { Topic } from './topic.entity';
import { UnitQuiz } from './unit-quiz.entity';
import { Course } from './course.entity';

@Entity('units')
export class Unit {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    title: string;

    @Column('text')
    description: string;

    @Column()
    order_index: number;

    @Column({ type: 'uuid', nullable: true })
    course_id: string;

    @ManyToOne(() => Course, (course) => course.units, { nullable: true })
    @JoinColumn({ name: 'course_id' })
    course: Course;

    @ManyToOne(() => Topic, (topic: Topic) => topic.units, { nullable: true })
    topic: Topic;

    @OneToMany(() => Material, (material: Material) => material.unit)
    materials: Material[];

    @OneToMany(() => Progress, (progress: Progress) => progress.unit)
    progress: Progress[];

    @OneToMany(() => UnitQuiz, (quiz: UnitQuiz) => quiz.unit)
    unitQuizzes: UnitQuiz[];

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}