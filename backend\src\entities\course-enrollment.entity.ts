import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { User } from './user.entity';
import { Course } from './course.entity';

export enum EnrollmentStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  DROPPED = 'dropped',
  SUSPENDED = 'suspended',
}

@Entity('course_enrollments')
@Unique(['user_id', 'course_id'])
export class CourseEnrollment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ type: 'uuid' })
  course_id: string;

  @Column({
    type: 'enum',
    enum: EnrollmentStatus,
    default: EnrollmentStatus.ACTIVE,
  })
  status: EnrollmentStatus;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  progress_percentage: number;

  @Column({ type: 'int', default: 0 })
  completed_units: number;

  @Column({ type: 'int', default: 0 })
  total_units: number;

  @Column({ type: 'int', default: 0 })
  time_spent_minutes: number;

  @Column({ type: 'timestamp', nullable: true })
  last_accessed: Date;

  @Column({ type: 'timestamp', nullable: true })
  completed_at: Date;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  final_grade: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  letter_grade: string; // A, B, C, D, F

  @Column({ type: 'jsonb', nullable: true })
  preferences: {
    notifications_enabled?: boolean;
    reminder_frequency?: string;
    study_schedule?: {
      days: string[];
      time: string;
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    quiz_scores: number[];
    study_sessions: {
      date: Date;
      duration: number;
      units_covered: string[];
    }[];
    performance_trends: {
      week: number;
      score: number;
      time_spent: number;
    }[];
  };

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Course, (course) => course.enrollments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @CreateDateColumn()
  enrolled_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
