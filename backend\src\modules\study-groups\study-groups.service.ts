import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  StudyGroup,
  StudyGroupType,
  StudyGroupPrivacy,
  StudyGroupStatus,
} from '../../entities/study-group.entity';
import {
  StudyGroupMember,
  MemberRole,
  MemberStatus,
} from '../../entities/study-group-member.entity';
import {
  GroupDiscussion,
  DiscussionType,
  DiscussionStatus,
} from '../../entities/group-discussion.entity';
import {
  DiscussionMessage,
  MessageType,
} from '../../entities/discussion-message.entity';
import { User } from '../../entities/user.entity';
import { Course } from '../../entities/course.entity';

export interface CreateStudyGroupDto {
  name: string;
  description?: string;
  type: StudyGroupType;
  privacy: StudyGroupPrivacy;
  max_members?: number;
  tags?: string[];
  study_topics?: string[];
  goals?: any;
  schedule?: any;
  rules?: any;
  course_id?: string;
}

export interface JoinGroupDto {
  invite_code?: string;
  message?: string;
}

export interface CreateDiscussionDto {
  title: string;
  content: string;
  type: DiscussionType;
  tags?: string[];
  poll_data?: any;
  attachments?: any[];
}

export interface CreateMessageDto {
  content: string;
  type?: MessageType;
  attachments?: any[];
  reply_to_id?: string;
}

@Injectable()
export class StudyGroupsService {
  constructor(
    @InjectRepository(StudyGroup)
    private readonly studyGroupRepository: Repository<StudyGroup>,
    @InjectRepository(StudyGroupMember)
    private readonly memberRepository: Repository<StudyGroupMember>,
    @InjectRepository(GroupDiscussion)
    private readonly discussionRepository: Repository<GroupDiscussion>,
    @InjectRepository(DiscussionMessage)
    private readonly messageRepository: Repository<DiscussionMessage>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
  ) {}

  async create(
    createStudyGroupDto: CreateStudyGroupDto,
    creatorId: string,
  ): Promise<StudyGroup> {
    // Validate course if provided
    if (createStudyGroupDto.course_id) {
      const course = await this.courseRepository.findOne({
        where: { id: createStudyGroupDto.course_id },
      });
      if (!course) {
        throw new NotFoundException('Course not found');
      }
    }

    // Generate invite code
    const inviteCode = this.generateInviteCode();

    const studyGroup = this.studyGroupRepository.create({
      ...createStudyGroupDto,
      created_by: creatorId,
      invite_code: inviteCode,
      current_member_count: 1,
      last_activity: new Date(),
    });

    const savedGroup = await this.studyGroupRepository.save(studyGroup);

    // Add creator as owner
    await this.addMember(savedGroup.id, creatorId, MemberRole.OWNER);

    return savedGroup;
  }

  async findAll(
    filters: {
      type?: StudyGroupType;
      privacy?: StudyGroupPrivacy;
      course_id?: string;
      search?: string;
      user_id?: string;
    } = {},
  ) {
    const queryBuilder = this.studyGroupRepository
      .createQueryBuilder('group')
      .leftJoinAndSelect('group.course', 'course')
      .leftJoinAndSelect('group.creator', 'creator')
      .leftJoinAndSelect('group.members', 'members')
      .leftJoinAndSelect('members.user', 'member_user');

    if (filters.type) {
      queryBuilder.andWhere('group.type = :type', { type: filters.type });
    }

    if (filters.privacy) {
      queryBuilder.andWhere('group.privacy = :privacy', {
        privacy: filters.privacy,
      });
    }

    if (filters.course_id) {
      queryBuilder.andWhere('group.course_id = :course_id', {
        course_id: filters.course_id,
      });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(group.name ILIKE :search OR group.description ILIKE :search OR group.tags && ARRAY[:search])',
        { search: `%${filters.search}%` },
      );
    }

    if (filters.user_id) {
      queryBuilder.andWhere('members.user_id = :user_id', {
        user_id: filters.user_id,
      });
    }

    // Only show active groups and public/invite-only groups (unless user is a member)
    queryBuilder.andWhere('group.status = :status', {
      status: StudyGroupStatus.ACTIVE,
    });

    if (!filters.user_id) {
      queryBuilder.andWhere('group.privacy != :private', {
        private: StudyGroupPrivacy.PRIVATE,
      });
    }

    queryBuilder.orderBy('group.last_activity', 'DESC');

    return await queryBuilder.getMany();
  }

  async findOne(id: string, userId?: string): Promise<StudyGroup> {
    const studyGroup = await this.studyGroupRepository.findOne({
      where: { id },
      relations: [
        'course',
        'creator',
        'members',
        'members.user',
        'discussions',
        'discussions.creator',
      ],
    });

    if (!studyGroup) {
      throw new NotFoundException('Study group not found');
    }

    // Check if user has access to private group
    if (studyGroup.privacy === StudyGroupPrivacy.PRIVATE && userId) {
      const membership = await this.memberRepository.findOne({
        where: { study_group_id: id, user_id: userId },
      });
      if (!membership) {
        throw new ForbiddenException('Access denied to private group');
      }
    }

    // Add user-specific data if user is provided
    if (userId) {
      const membership = await this.memberRepository.findOne({
        where: { study_group_id: id, user_id: userId },
      });
      (studyGroup as any).user_membership = membership;
      (studyGroup as any).is_member = !!membership;
    }

    return studyGroup;
  }

  async join(
    groupId: string,
    userId: string,
    joinGroupDto: JoinGroupDto = {},
  ): Promise<StudyGroupMember> {
    const studyGroup = await this.studyGroupRepository.findOne({
      where: { id: groupId },
    });

    if (!studyGroup) {
      throw new NotFoundException('Study group not found');
    }

    if (studyGroup.status !== StudyGroupStatus.ACTIVE) {
      throw new BadRequestException('Study group is not active');
    }

    // Check if already a member
    const existingMember = await this.memberRepository.findOne({
      where: { study_group_id: groupId, user_id: userId },
    });

    if (existingMember) {
      throw new BadRequestException('Already a member of this group');
    }

    // Check capacity
    if (studyGroup.current_member_count >= studyGroup.max_members) {
      throw new BadRequestException('Study group is full');
    }

    // Check privacy and invite code
    if (studyGroup.privacy === StudyGroupPrivacy.PRIVATE) {
      throw new ForbiddenException(
        'Cannot join private group without invitation',
      );
    }

    if (studyGroup.privacy === StudyGroupPrivacy.INVITE_ONLY) {
      if (
        !joinGroupDto.invite_code ||
        joinGroupDto.invite_code !== studyGroup.invite_code
      ) {
        throw new BadRequestException('Invalid invite code');
      }
    }

    // Add member
    const member = await this.addMember(groupId, userId, MemberRole.MEMBER);

    // Update group member count
    await this.studyGroupRepository.update(groupId, {
      current_member_count: studyGroup.current_member_count + 1,
      last_activity: new Date(),
    });

    return member;
  }

  async leave(groupId: string, userId: string): Promise<void> {
    const member = await this.memberRepository.findOne({
      where: { study_group_id: groupId, user_id: userId },
    });

    if (!member) {
      throw new NotFoundException('Membership not found');
    }

    if (member.role === MemberRole.OWNER) {
      // Check if there are other members to transfer ownership
      const otherMembers = await this.memberRepository.find({
        where: { study_group_id: groupId },
      });

      if (otherMembers.length > 1) {
        throw new BadRequestException(
          'Transfer ownership before leaving the group',
        );
      }
    }

    await this.memberRepository.remove(member);

    // Update group member count
    const studyGroup = await this.studyGroupRepository.findOne({
      where: { id: groupId },
    });
    if (studyGroup) {
      await this.studyGroupRepository.update(groupId, {
        current_member_count: Math.max(0, studyGroup.current_member_count - 1),
      });
    }
  }

  async createDiscussion(
    groupId: string,
    createDiscussionDto: CreateDiscussionDto,
    userId: string,
  ): Promise<GroupDiscussion> {
    // Verify membership
    await this.verifyMembership(groupId, userId);

    const discussion = this.discussionRepository.create({
      ...createDiscussionDto,
      study_group_id: groupId,
      created_by: userId,
      last_activity: new Date(),
    });

    const savedDiscussion = await this.discussionRepository.save(discussion);

    // Update group last activity
    await this.studyGroupRepository.update(groupId, {
      last_activity: new Date(),
    });

    return savedDiscussion;
  }

  async getDiscussions(groupId: string, userId?: string) {
    // Verify access if user is provided
    if (userId) {
      await this.verifyMembership(groupId, userId);
    }

    return await this.discussionRepository.find({
      where: { study_group_id: groupId },
      relations: ['creator', 'messages', 'messages.user'],
      order: {
        is_pinned: 'DESC',
        last_activity: 'DESC',
      },
    });
  }

  async createMessage(
    discussionId: string,
    createMessageDto: CreateMessageDto,
    userId: string,
  ): Promise<DiscussionMessage> {
    const discussion = await this.discussionRepository.findOne({
      where: { id: discussionId },
      relations: ['study_group'],
    });

    if (!discussion) {
      throw new NotFoundException('Discussion not found');
    }

    // Verify membership
    await this.verifyMembership(discussion.study_group_id, userId);

    const message = this.messageRepository.create({
      ...createMessageDto,
      discussion_id: discussionId,
      user_id: userId,
    });

    const savedMessage = await this.messageRepository.save(message);

    // Update discussion stats
    await this.discussionRepository.update(discussionId, {
      message_count: discussion.message_count + 1,
      last_activity: new Date(),
      last_message_by: userId,
    });

    // Update group last activity
    await this.studyGroupRepository.update(discussion.study_group_id, {
      last_activity: new Date(),
    });

    return savedMessage;
  }

  async getMessages(discussionId: string, userId?: string) {
    const discussion = await this.discussionRepository.findOne({
      where: { id: discussionId },
    });

    if (!discussion) {
      throw new NotFoundException('Discussion not found');
    }

    // Verify access if user is provided
    if (userId) {
      await this.verifyMembership(discussion.study_group_id, userId);
    }

    return await this.messageRepository.find({
      where: { discussion_id: discussionId, is_deleted: false },
      relations: ['user', 'reply_to', 'reply_to.user'],
      order: { created_at: 'ASC' },
    });
  }

  private async addMember(
    groupId: string,
    userId: string,
    role: MemberRole,
  ): Promise<StudyGroupMember> {
    const member = this.memberRepository.create({
      study_group_id: groupId,
      user_id: userId,
      role,
      status: MemberStatus.ACTIVE,
      joined_at: new Date(),
      last_active: new Date(),
      statistics: {
        messages_sent: 0,
        resources_shared: 0,
        questions_asked: 0,
        questions_answered: 0,
        study_hours_logged: 0,
        peer_ratings: {
          helpfulness: 0,
          knowledge: 0,
          collaboration: 0,
          reliability: 0,
        },
      },
    });

    return await this.memberRepository.save(member);
  }

  private async verifyMembership(
    groupId: string,
    userId: string,
  ): Promise<StudyGroupMember> {
    const member = await this.memberRepository.findOne({
      where: {
        study_group_id: groupId,
        user_id: userId,
        status: MemberStatus.ACTIVE,
      },
    });

    if (!member) {
      throw new ForbiddenException(
        'You must be a member to access this resource',
      );
    }

    return member;
  }

  private generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  async updateMemberRole(
    groupId: string,
    memberId: string,
    newRole: MemberRole,
    requesterId: string,
  ): Promise<StudyGroupMember> {
    // Verify requester has permission
    const requesterMember = await this.verifyMembership(groupId, requesterId);
    if (
      requesterMember.role !== MemberRole.OWNER &&
      requesterMember.role !== MemberRole.MODERATOR
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    const member = await this.memberRepository.findOne({
      where: { id: memberId, study_group_id: groupId },
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    member.role = newRole;
    return await this.memberRepository.save(member);
  }

  async removeMember(
    groupId: string,
    memberId: string,
    requesterId: string,
  ): Promise<void> {
    // Verify requester has permission
    const requesterMember = await this.verifyMembership(groupId, requesterId);
    if (
      requesterMember.role !== MemberRole.OWNER &&
      requesterMember.role !== MemberRole.MODERATOR
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    const member = await this.memberRepository.findOne({
      where: { id: memberId, study_group_id: groupId },
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    if (member.role === MemberRole.OWNER) {
      throw new BadRequestException('Cannot remove group owner');
    }

    await this.memberRepository.remove(member);

    // Update group member count
    const studyGroup = await this.studyGroupRepository.findOne({
      where: { id: groupId },
    });
    if (studyGroup) {
      await this.studyGroupRepository.update(groupId, {
        current_member_count: Math.max(0, studyGroup.current_member_count - 1),
      });
    }
  }
}
