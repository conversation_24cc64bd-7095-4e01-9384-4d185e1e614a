"use client";
import { useState, useEffect } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaEyeSlash, FaEnvelope, FaCheckCircle, FaExclamationCircle } from 'react-icons/fa';

interface FormErrors {
  identifier?: string;
  password?: string;
}

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | React.ReactNode>('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formTouched, setFormTouched] = useState({
    identifier: false,
    password: false
  });
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [message, setMessage] = useState('');

  // Determine if identifier is an email
  const isEmail = identifier.includes('@');

  // Validate form fields
  const validateField = (name: string, value: string) => {
    let error = '';

    switch (name) {
      case 'identifier':
        if (!value) {
          error = 'Username or email is required';
        } else if (value.length < 3) {
          error = 'Username or email must be at least 3 characters';
        }
        break;

      case 'password':
        if (!value) {
          error = 'Password is required';
        } else if (value.length < 8) {
          error = 'Password must be at least 8 characters';
        }
        break;
    }

    return error;
  };

  useEffect(() => {
    // Check if user was redirected from registration
    const registered = searchParams.get('registered');
    if (registered === 'true') {
      setSuccess('Registration successful! You can now log in with your credentials.');
    }

    // Check for verification success message
    const verified = searchParams.get('verified');
    if (verified === 'true') {
      setMessage('Email verified successfully! You can now log in.');
    }
  }, [searchParams]);

  const handleSocialLogin = async (provider: string) => {
    try {
      setLoading(true);
      await signIn(provider, { callbackUrl: '/dashboard' });
    } catch (error) {
      setError(`Error signing in with ${provider}`);
      setLoading(false);
    }
  };

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'identifier') {
      setIdentifier(value);
    } else if (name === 'password') {
      setPassword(value);
    } else if (name === 'rememberMe') {
      setRememberMe(e.target.checked);
    }

    // Mark field as touched
    if (!formTouched[name as keyof typeof formTouched]) {
      setFormTouched({
        ...formTouched,
        [name]: true
      });
    }

    // Validate the field if it's been touched
    if (formTouched[name as keyof typeof formTouched]) {
      const error = validateField(name, value);
      setFormErrors({
        ...formErrors,
        [name]: error
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    // Mark all fields as touched for validation
    setFormTouched({
      identifier: true,
      password: true
    });

    // Validate all fields
    const errors: FormErrors = {
      identifier: validateField('identifier', identifier),
      password: validateField('password', password)
    };

    // Update form errors
    setFormErrors(errors);

    // If there are any errors, don't submit
    if (errors.identifier || errors.password) {
      setError("Please fix the errors in the form before submitting.");
      setLoading(false);
      return;
    }

    // Increment login attempts
    setLoginAttempts(prev => prev + 1);

    try {
      const result = await signIn('credentials', {
        username: identifier, // This can be either email or username
        password,
        rememberMe,
        redirect: false,
      });

      if (result?.error) {
        // Check for specific error messages
        if (result.error.toLowerCase().includes('not found')) {
          setError(
            <>
              Account not found. Please check your username/email or{' '}
              <Link href="/auth/register" className="text-blue-600 hover:underline">
                register for a new account
              </Link>.
            </>
          );
        } else if (result.error.toLowerCase().includes('locked')) {
          setError(
            <>
              Your account has been locked due to too many failed login attempts. 
              Please try again later or{' '}
              <Link href="/auth/forgot-password" className="text-blue-600 hover:underline">
                reset your password
              </Link>.
            </>
          );
        } else if (result.error.toLowerCase().includes('inactive')) {
          setError(
            <>
              Your account is inactive. Please check your email for activation instructions or{' '}
              <Link href="/auth/resend-verification" className="text-blue-600 hover:underline">
                resend verification email
              </Link>.
            </>
          );
        } else if (result.error.toLowerCase().includes('password')) {
          setError('Incorrect password. Please try again or use the forgot password link.');
          
          // If multiple failed attempts, suggest password reset
          if (loginAttempts >= 2) {
            setError(
              <>
                Multiple failed login attempts. If you have forgotten your password,{' '}
                <Link href="/auth/forgot-password" className="text-blue-600 hover:underline">
                  reset it here
                </Link>.
              </>
            );
          }
        } else {
          setError('Invalid credentials. Please check your username/email and password.');
        }
      } else {
        // Show brief success message before redirecting
        setSuccess('Login successful! Redirecting to dashboard...');
        setTimeout(() => {
          router.push('/dashboard');
        }, 500);
      }
    } catch (error) {
      setError('An error occurred during login. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter your username or email to continue
          </p>
        </div>

        {message && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaCheckCircle className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{message}</p>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="text-sm text-green-700">{success}</div>
          </div>
        )}

        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationCircle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">
                  {error}
                  {error.includes('not verified') && (
                    <Link
                      href="/auth/resend-verification"
                      className="ml-2 text-indigo-600 hover:text-indigo-500"
                    >
                      Resend verification email
                    </Link>
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm space-y-4">
            <div>
              <label htmlFor="identifier" className="block text-sm font-medium text-gray-700 mb-1">
                Username or Email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  {isEmail ? (
                    <FaEnvelope className="h-5 w-5 text-gray-400" />
                  ) : (
                    <FaUser className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                <input
                  id="identifier"
                  name="identifier"
                  type="text"
                  required
                  value={identifier}
                  onChange={handleChange}
                  onBlur={() => {
                    if (!formTouched.identifier) {
                      setFormTouched({...formTouched, identifier: true});
                      setFormErrors({
                        ...formErrors,
                        identifier: validateField('identifier', identifier)
                      });
                    }
                  }}
                  className={`appearance-none block w-full pl-10 pr-3 py-2 border ${
                    formErrors.identifier ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  } rounded-md placeholder-gray-500 text-gray-900 focus:outline-none sm:text-sm`}
                  placeholder="Enter your username or email"
                />
              </div>
              {formErrors.identifier && formTouched.identifier && (
                <p className="mt-1 text-sm text-red-600">{formErrors.identifier}</p>
              )}
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  value={password}
                  onChange={handleChange}
                  onBlur={() => {
                    if (!formTouched.password) {
                      setFormTouched({...formTouched, password: true});
                      setFormErrors({
                        ...formErrors,
                        password: validateField('password', password)
                      });
                    }
                  }}
                  className={`appearance-none block w-full pl-10 pr-10 py-2 border ${
                    formErrors.password ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  } rounded-md placeholder-gray-500 text-gray-900 focus:outline-none sm:text-sm`}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <FaEyeSlash className="h-5 w-5 text-gray-400" />
                  ) : (
                    <FaEye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {formErrors.password && formTouched.password && (
                <p className="mt-1 text-sm text-red-600">{formErrors.password}</p>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="rememberMe"
                name="rememberMe"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
              />
              <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-900">
                Remember me
              </label>
            </div>

            <div className="text-sm">
              <Link href="/auth/forgot-password" className="font-medium text-blue-600 hover:text-blue-500">
                Forgot your password?
              </Link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                loading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {loading ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  Signing in...
                </>
              ) : (
                'Sign in'
              )}
            </button>
          </div>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => handleSocialLogin('google')}
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
              >
                <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"/>
                </svg>
                <span className="ml-2">Google</span>
              </button>

              <button
                type="button"
                onClick={() => handleSocialLogin('github')}
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
              >
                <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                <span className="ml-2">GitHub</span>
              </button>
            </div>
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link href="/auth/register" className="font-medium text-blue-600 hover:text-blue-500">
                Sign up
              </Link>
            </p>
            {process.env.NODE_ENV === 'development' && (
              <p className="text-xs text-gray-500 mt-2">
                <button
                  type="button"
                  onClick={async () => {
                    try {
                      setLoading(true);
                      const response = await fetch('http://localhost:3002/v1/auth/create-test-users');
                      const data = await response.json();
                      if (response.ok) {
                        setSuccess(
                          <>
                            Test users created successfully!<br/>
                            <strong>Test User:</strong> username: test, password: Test123!@#<br/>
                            <strong>Admin User:</strong> username: admin, password: Admin123!@#
                          </>
                        );
                      } else {
                        setError('Failed to create test users: ' + data.message);
                      }
                    } catch (error) {
                      setError('Error creating test users');
                      console.error(error);
                    } finally {
                      setLoading(false);
                    }
                  }}
                  className="text-blue-500 hover:underline"
                >
                  Create test users
                </button>
              </p>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}
