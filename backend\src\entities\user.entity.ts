import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Progress } from './progress.entity';
import { Material } from './materials.entity';
import { UserResponse } from './user-response.entity';
import { MaterialShare } from './material_shares.entity';
import { Feedback } from './feedback.entity';
import { Notification } from './notifications.entity';
import { ClinicalCase } from './clinical-case.entity';
import { StudySession } from './study-session.entity';
import { TopicProgress } from './topic-progress.entity';
import { Flashcard } from './flashcard.entity';
import { Role } from './role.entity';

// Re-export Role for use in other modules
export { Role } from './role.entity';
import { CPDCycle } from './cpd-tracking.entity';
import { CPDActivity } from './cpd-activity.entity';
import { WeeklyDigest } from './weekly-digest.entity';
import { LearningSuggestion } from './learning-suggestion.entity';

export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  ADMIN = 'admin',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column({ unique: true })
  username: string;

  @Column({ default: true })
  is_active: boolean;

  @Column({ unique: true, nullable: true })
  password_reset_token: string;

  @Column({ nullable: true })
  password_reset_expires: Date;

  @Column({ nullable: true })
  email_verification_token: string;

  @Column({ nullable: true })
  email_verification_expires: Date;

  @Column({ default: false })
  email_verified: boolean;

  @Column()
  name: string;

  @Column()
  password_hash: string;

  @Column()
  first_name: string;

  @Column()
  last_name: string;

  @Column({ nullable: true })
  profile_picture: string;

  @Column({ nullable: true })
  bio: string;

  @Column({ nullable: true })
  phone_number: string;

  @Column({ type: 'enum', enum: UserRole, default: UserRole.STUDENT })
  role: UserRole;

  @Column({ type: 'boolean', default: false })
  is_locked: boolean;

  @Column({ type: 'timestamp', nullable: true })
  locked_until: Date;

  @Column({ type: 'int', default: 0 })
  failed_login_attempts: number;

  @OneToMany(() => Progress, (progress) => progress.user)
  progress: Progress[];

  @OneToMany(() => UserResponse, (response: UserResponse) => response.user)
  quiz_responses: UserResponse[];

  @OneToMany(() => Material, (material) => material.author)
  created_materials: Material[];

  @OneToMany(
    () => MaterialShare,
    (share: MaterialShare) => share.shared_by_user,
  )
  shared_materials: MaterialShare[];

  @OneToMany(() => Feedback, (feedback: Feedback) => feedback.user)
  feedback: Feedback[];

  @OneToMany(
    () => Notification,
    (notification: Notification) => notification.user,
  )
  notifications: Notification[];

  @OneToMany(() => ClinicalCase, (clinicalCase) => clinicalCase.author)
  created_clinical_cases: ClinicalCase[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'int', default: 0 })
  streak_days: number;

  @OneToMany(() => StudySession, (session: StudySession) => session.user)
  study_sessions: StudySession[];

  @OneToMany(() => TopicProgress, (progress: TopicProgress) => progress.user)
  topic_progress: TopicProgress[];

  @OneToMany(() => Flashcard, (flashcard) => flashcard.user)
  flashcards: Flashcard[];

  @ManyToMany(() => Role)
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
  })
  roles: Role[];

  @OneToMany(() => CPDCycle, (cycle) => cycle.user)
  cpd_cycles: CPDCycle[];

  @OneToMany(() => CPDActivity, (activity) => activity.user)
  cpd_activities: CPDActivity[];

  @OneToMany(() => WeeklyDigest, (digest) => digest.user)
  weekly_digests: WeeklyDigest[];

  @OneToMany(() => LearningSuggestion, (suggestion) => suggestion.user)
  learning_suggestions: LearningSuggestion[];

  @Column({ type: 'jsonb', nullable: true })
  learningHistory: {
    timestamp: Date;
    type: string;
    score?: number;
    duration?: number;
    category?: string;
    difficulty?: number;
    engagement?: number;
    interactionScore?: number;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  preferences: {
    learningStyle: number;
    preferredCategories: string[];
    difficultyPreference: number;
  };
}

export interface IUser {
  id: string;
  username: string;
  email: string;
  role: string;
  created_at: Date;
  updated_at: Date;
  password: string;
  password_hash: string;
  password_salt: string;
  password_reset_token: string;
  password_reset_expires: Date;
  email_verification_token: string;
  email_verification_expires: Date;
  email_verified: boolean;
}

export interface ICreateUser {
  id: string;
  username: string;
  email: string;
  role: string;
  created_at: Date;
  updated_at: Date;
  password: string;
  password_hash: string;
  password_salt: string;
  password_reset_token: string;
  password_reset_expires: Date;
  email_verification_token: string;
  email_verification_expires: Date;
  email_verified: boolean;
}
