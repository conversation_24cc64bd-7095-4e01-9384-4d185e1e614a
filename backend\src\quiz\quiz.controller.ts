import { Controller, Get, Post, Body, Param, UseGuards, Query } from '@nestjs/common';
import { QuizService } from './quiz.service';
import { JwtAuthGuard } from '../modules/auth/jwt-auth.guard';

@Controller('quiz')
@UseGuards(JwtAuthGuard)
export class QuizController {
    constructor(private readonly quizService: QuizService) {}

    @Get('unit/:unitId')
    async getQuestionsByUnit(@Param('unitId') unitId: string) {
        return this.quizService.getQuestionsByUnit(unitId);
    }

    @Post('submit')
    async submitAnswer(
        @Body('userId') userId: string,
        @Body('questionId') questionId: string,
        @Body('answer') answer: string,
    ) {
        return this.quizService.submitAnswer(userId, questionId, answer);
    }

    @Get('results/:userId/:unitId')
    async getQuizResults(
        @Param('userId') userId: string,
        @Param('unitId') unitId: string,
    ) {
        return this.quizService.getUserQuizResults(userId, unitId);
    }

    @Get('rapid-review/:userId')
    async getRapidReviewQuestions(
        @Param('userId') userId: string,
        @Query('topics') topics?: string
    ) {
        const topicsArray = topics ? topics.split(',') : undefined;
        return this.quizService.getRapidReviewQuestions(userId, topicsArray);
    }
}