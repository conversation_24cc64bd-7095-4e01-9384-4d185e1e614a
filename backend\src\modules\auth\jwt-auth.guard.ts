import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Logger } from '@nestjs/common';
import { ExecutionContext } from '@nestjs/common';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    const logger = new Logger('JwtAuthGuard');
    if (err || !user) {
      logger.warn('Authentication failed', err || info);
      throw err || new UnauthorizedException();
    }
    logger.debug('Authentication successful');
    return user;
  }
}
